<template>
    <div class="lx-select" v-click-outside="closeDropdown">

        <div class="lx-select-input" @click="toggleDropdown">
            <!-- 支持自定义头部内容的插槽 -->
            <slot name="header" :modelValue="modelValue" :selectedItems="selectedItems" :isOpen="isOpen">
                <!-- 默认头部内容 -->
                <template v-if="modelValue && selectedItems.length > 0">
                    <el-tag type="info" v-for="value in selectedItems.slice(0, 3)" :key="value.value"
                        class="selected-item">{{ value.label }}</el-tag>
                    <el-tag v-if="selectedItems.length > 3" type="info" class="selected-item">+ {{
                        selectedItems.length - 3 }}</el-tag>
                </template>
                <template v-else>
                    <span class="placeholder">{{ placeholder }}</span>
                </template>
            </slot>

            <!-- 支持自定义后缀图标的插槽 -->
            <slot name="suffix" :clearable="clearable" :modelValue="modelValue" :isOpen="isOpen"
                :handleClear="handleClear">
                <!-- 默认后缀图标 -->
                <div class="suffix-icon" v-if="clearable && modelValue" @click.stop="handleClear">
                    <el-icon>
                        <CircleClose />
                    </el-icon>
                </div>
                <div class="suffix-icon" v-else>
                    <el-icon>
                        <ArrowUp v-if="isOpen" />
                        <ArrowDown v-else />
                    </el-icon>
                </div>
            </slot>
        </div>
        <div v-show="isOpen" class="lx-select-dropdown">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { ref, provide } from 'vue'
import { vClickOutside } from '@/components/directives/clickOutside'
import { ArrowDown, ArrowUp, CircleClose } from '@element-plus/icons-vue'

const props = defineProps({
    modelValue: {
        type: [String, Number, Array],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    clearable: {
        type: Boolean,
        default: false
    },
    option: {
        type: Array,
        default: () => ([])
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const isOpen = ref(false)
const selectedItems = ref([])


watch(() => [props.option, props.modelValue], ([newOption, newModelValue]) => {
    if (Array.isArray(newModelValue) && newModelValue.length > 0) {
        selectedItems.value = newOption.filter(item => newModelValue.includes(item.value));

    } else {
        selectedItems.value = [];
    }
}, {
    immediate: true,
    deep: true
})

const toggleDropdown = () => {
    console.log('toggleDropdown')
    isOpen.value = !isOpen.value
}

const closeDropdown = () => {
    isOpen.value = false
}

const handleClear = () => {
    emit('update:modelValue', '')
    emit('change', '')
    selectedItems.value = [];
    closeDropdown()
}

const handleSelect = (value, label) => {
    emit('update:modelValue', value)
    emit('change', value)
    closeDropdown()
}

defineExpose({
    handleSelect
})
</script>

<style lang="scss" scoped>
.lx-select {
    position: relative;
    width: 120px;
    user-select: none;

    .selected-item {
        margin-right: 4px;
        max-width: 120px;

        :deep(.el-tag__content) {
            display: block;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    &-input {
        height: 30px;
        line-height: 30px;
        padding: 0 30px 0 12px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        background-color: #fff;
        cursor: pointer;
        position: relative;
        display: flex;
        align-items: center;

        &:hover {
            border-color: #c0c4cc;

            .suffix-icon {
                .el-icon {
                    display: flex;
                }
            }
        }

        .placeholder {
            font-size: 14px;
            color: #a8abb2;
        }

        .suffix-icon {
            position: absolute;
            right: 8px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #c0c4cc;
            font-size: 12px;

            .el-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 14px;
                height: 14px;
                cursor: pointer;

                &:hover {
                    color: #909399;
                }
            }
        }
    }

    &-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 350px;
        margin: 5px 0;
        background: #FFFFFF;
        box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px 8px 8px 8px;
        z-index: 1000;
        overflow-y: auto;
    }
}
</style>
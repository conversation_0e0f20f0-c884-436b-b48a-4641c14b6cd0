<template>
    <div class="col-competitor" @click="onClick">
        <div class="competitor-info">
            <span class="primary-competitor">{{ primaryCompetitor }}</span>
            <span class="competitor-count" v-if="competitorCount > 0">+{{ competitorCount }}</span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps(['row']);
const emit = defineEmits(['callback']);

const primaryCompetitor = computed(() => {
    return props.row?.primaryCompetitor || '酷学院';
});

const competitorCount = computed(() => {
    return props.row?.competitorCount || 8;
});

const onClick = () => {
    emit("callback", props.row, "competition");
};

defineExpose({
    primaryCompetitor,
    competitorCount,
    onClick
});
</script>

<style lang="scss" scoped>
.col-competitor {
    display: flex;
    align-items: center;
    height: 44px;
    cursor: pointer;

    .competitor-info {
        display: flex;
        align-items: center;
        gap: 4px;

        .primary-competitor {
            font-size: 14px;
            color: #262626;
            font-weight: 400;
        }

        .competitor-count {
            font-size: 12px;
            color: #8c8c8c;
            font-weight: 400;
        }
    }

    &:hover {
        .competitor-info {
            .primary-competitor {
                color: #436bff;
            }
        }
    }
}
</style>

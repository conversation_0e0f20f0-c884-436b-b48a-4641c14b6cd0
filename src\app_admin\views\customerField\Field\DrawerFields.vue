<template>
  <el-drawer v-model="is_show" direction="rtl" class="drawer_add_customer_field" :close-on-click-modal="true">
    <template #header>
      <div class="vd_title">{{ title }}</div>
    </template>
    <template #default>
      <div class="form-container">
        <el-form :model="formData" label-width="70px" label-position="top">
          <!-- 名称 -->
          <el-form-item label="名称" required>
            <el-input v-model="formData.fieldName" placeholder="1-20个字符，不能与现有字段名称重复" minlength="1"
              maxlength="20"></el-input>
          </el-form-item>

          <!-- 类型 -->
          <el-form-item label="类型" required>
            <el-select v-model="formData.fieldType" placeholder="请选择类型" size="large" style="width: 240px"
              @change="onChangeFieldType" :disabled="isEdit">
              <el-option v-for="item in fieldTypeList" :key="item" :label="getLang(item)" :value="item" />
            </el-select>
          </el-form-item>

          <div class="form-item-container" v-if="formData.fieldType !== 'Date'">
            <template v-if="formData.fieldType === 'String'">
              <StringField v-model="formData" />
            </template>

            <template v-if="formData.fieldType === 'TextArea'">
              <TextField v-model="formData" />
            </template>

            <template v-if="formData.fieldType === 'DateTime'">
              <DateTimeField v-model="formData" />
            </template>

            <template v-if="formData.fieldType === 'Integer'">
              <IntegerField v-model="formData" />
            </template>

            <template v-if="formData.fieldType === 'Select'">
              <SelectField v-model="formData" />
            </template>

            <template v-if="formData.fieldType === 'MultiSelect'">
              <MultiSelectField v-model="formData" />
            </template>
          </div>
        </el-form>
        <el-form-item label="排序值" required>
          <el-input-number v-model="formData.sortOrder" :min="1" :max="999" />
          <el-tooltip content="排序值越小，越靠前" placement="top">
            <el-icon style="margin-left: 10px;">
              <InfoFilled />
            </el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="是否必填" required>
          <el-switch v-model="formData.isRequired" :active-value="1" :inactive-value="0" />
        </el-form-item>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCancel">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { InfoFilled } from '@element-plus/icons-vue'
import { addFormField, updateFormField } from '@/app_admin/tools/api'
import StringField from './components/StringField.vue'
import SelectField from './components/SelectField.vue'
import TextField from './components/TextField.vue'
import DateTimeField from './components/DateTimeField.vue'
import IntegerField from './components/IntegerField.vue'
import MultiSelectField from './components/MultiSelectField.vue'
import { FieldTypeList } from '@/js/const_value'
import getLang from '@/js/lang'

const is_show = ref(false);
const emit = defineEmits(["callback"]);
const title = ref('')

const props = defineProps({
  readonly: {
    type: Boolean,
    required: false,
    default: false,
  },
  type: {
    type: String,
    default: ''
  }
});

// 单行文本：最小长度0，最大长度255
// 多行文本：最小长度0，最大长度4000
// 日期时间：可控制显示时或时分
// 日期
// 整数：最小0，最大 -1 表示不限制
// 单选列表
// 多选列表
const fieldTypeList = ref(FieldTypeList)

const _default = {
  "fieldName": "",
  "fieldStatus": 1,
  "fieldType": "String",
  "fieldOptions": [''],
  "isRequired": 1,
  "maxLength": 50,
  'multiSelectLimit': 10,
  "minLength": 1,
  "minValue": 0,
  "maxValue": -1,
  "placeholder": "",
  "sortOrder": 1
};

const formData = ref({ ..._default });
const formCode = ref(props.type);
const isEdit = ref(false);

const onCancel = () => {
  is_show.value = false;
};

const onChangeFieldType = (value) => {
  if (value === 'MultiSelect' || value === 'Select') {
    formData.value.fieldOptions = [''];
  }
}

const getdata = () => {
  const param = { ...formData.value };
  if (param.fieldType === 'MultiSelect' || param.fieldType === 'Select') {
    const fieldOptions = param.fieldOptions.filter(item => item.trim() !== '');
    if (fieldOptions.length === 0) {
      ElMessage.error('请至少输入一个选项');
      return;
    }
    param.fieldOptions = fieldOptions.join(",");
  }
  return param;

}

const onConfirm = () => {
  if (props.readonly) {
    is_show.value = false;
    emit("callback", "confirm", formData.value);
  } else {
    const param = getdata();
    if (!param) {
      return;
    }
    if (isEdit.value) {
      updateFormField(formData.value.id, param).then(res => {
        if (res.code == 0) {
          ElMessage({
            grouping: true,
            message: '修改成功',
            type: 'success'
          });
          is_show.value = false;
          emit("callback", "reload", param);
        } else {
          ElMessage.error(res.message);
        }
      });
    } else {
      addFormField(formCode.value, param).then(res => {
        if (res.code == 0) {
          ElMessage.success("添加成功");
          is_show.value = false;
          emit("callback", "reload", param);
        } else {
          ElMessage.error(res.message);
        }
      });
    }
  }
};

const showAdd = () => {
  is_show.value = true;
  isEdit.value = false;
  formData.value = { ..._default };
  title.value = '添加字段'
};

const showEdit = (field) => {
  isEdit.value = true;
  is_show.value = true;
  const fieldCopy = { ...field }
  if (fieldCopy.fieldType === 'MultiSelect' || fieldCopy.fieldType === 'Select') {
    fieldCopy.fieldOptions = fieldCopy.fieldOptions.split(",");
  }
  formData.value = fieldCopy;
  title.value = '编辑字段'
};


defineExpose({
  title,
  fieldTypeList,
  showAdd,
  showEdit,
  onCancel,
  onConfirm,
  is_show,
  formData,
  TextField,
  StringField,
  DateTimeField,
  IntegerField,
  MultiSelectField
});
</script>

<style lang="scss">
.drawer_add_customer_field {
  width: 600px !important;

  .el-drawer__header {
    height: 56px;
    padding: 0;
    border: 1px solid #e9e9e9;
    font-size: 16px;
    color: #262626;
    margin-bottom: 0;
  }

  .el-drawer__body {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    margin: 0;

    .ed_main {
      height: calc(100vh - 100px);

      .av_item {
        .av_item_value {
          width: 90%;
        }
      }
    }

    .msg_ai {
      .mbody {
        background: #bdd2ff;
      }
    }

    .msg_my {
      .mbody {
        background: #fff;
      }
    }
  }

  .el-drawer__footer {
    border-top: 1px solid #e9e9e9;
    padding-top: 20px;

    .dialog-footer {
      text-align: right;
    }
  }

  .form-container {
    padding: 20px;

    .form-item-container {
      padding: 20px;
      border-radius: 4px;
      background-color: #fafafa;
    }
  }

  .char-limit {
    display: flex;
    gap: 20px;

    .limit-item {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .switch-group {
    display: flex;
    gap: 20px;
  }



  .options-container {
    .option-item {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 10px;

      .option-index {
        min-width: 20px;
      }

      .el-input {
        width: 454px;
      }

      .char-count {
        font-size: 12px;
        color: #999;
        margin: 0 8px;
      }

      .delete-btn {
        cursor: pointer;
        color: #8C8C8C;
      }

      .delete-btn:hover {
        color: #FF4D4F;
      }
    }
  }

  .add-option {
    margin-top: 10px;
    color: #436BFF;
  }
}
</style>
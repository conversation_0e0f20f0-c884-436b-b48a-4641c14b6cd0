<template>
  <div class="field-settings-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">创建</el-button>
      </template>
      <template #col_fieldName="{ row }">
        <span>{{ row.fieldName }}</span>
        <el-tag v-if="row.fieldName === '姓名' || row.fieldName === '负责人'" size="small">系统</el-tag>
      </template>
      <template #col_fieldType="{ row }">
        <span>{{ row.fieldType }}</span>
      </template>
      <template #col_creator="{ row }">
        <span>{{ row.creator }}</span>
      </template>
      <template #col_createTime="{ row }">
        <span>{{ row.createTime }}</span>
      </template>
      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
      </template>
    </MyTable>
    <DrawerFields ref="refDrawerField" @callback="onDrawerCallback" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import DrawerFields from "@/app_admin/views/customerField/Field/DrawerFields.vue";
import { userManageApi } from "@/app_admin/api";
import { ref, reactive } from "vue";

const refTable = ref();
const refDrawerField = ref();

const tableConfig = reactive({
  tableid: 'field_settings_list',
  param: { searchKey: "" },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_btn_add: false,
  form: {},
  search_ph: "字段名称",
  delete_hint_column: 'fieldName',
  show_link_column: true,
  show_link_edit: true,
  show_link_view: false,
  show_link_delete: true,
  columns: ["fieldName", "fieldType", "creator", "createTime"],
  template: ["fieldName", "fieldType"],
  urlGet: userManageApi.getFieldSettingsListData,
  urlDelete: userManageApi.deleteFieldSetting
});

const handleTableCallback = (action, data) => {
  console.log(action, data);
  if (action == 'init_edit') {
    refDrawerField.value.showEdit(data);
  }

};

const handleAdd = () => {
  refDrawerField.value.showAdd();
};


const onDrawerCallback = (action, data) => {
  if (action === "reload") {
    refTable.value.search();
  }
};
defineExpose({ refTable });
</script>

<style lang="scss" scoped>
.field-settings-table {
  padding: 24px;

  .page-header {
    margin-bottom: 24px;

    h2 {
      margin: 0;
      color: #303133;
      font-size: 20px;
      font-weight: 500;
    }
  }

  .content {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
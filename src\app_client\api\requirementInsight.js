import {
  getCVDemandInsightListApi,
  getCVDemandInsightDetailApi,
} from "@/app_client/tools/api.js";

export const getReqTabelData = async (days, obj) => {
  const mock_data = {
    code: 0,
    data: {
      datas: [
        // {
        //     "categoryIds": [
        //         "1930884828975075328"
        //     ],
        //     "categoryNames": [
        //         "核心功能"
        //     ],
        //     "demand": "mock明确财务相关流程和规则",
        //     "faqIds": [
        //         "1934495891440349186"
        //     ],
        //     "faqs": [
        //         "财务相关问题，包括付款频率、报销流程、订单周期、费用支付、发票开具等"
        //     ],
        //     "id": "1934865737969643520",
        //     "orgId": "6a759132-4ae7-40b2-8da5-7ce4bfc81798",
        //     "priority": "HIGH",
        //     "suggestion": "梳理付款频率、报销流程等规则，形成文档并在系统显著位置展示。"
        // }
      ],
      totalNum: 0,
    },
    message: "success",
  };
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getCVDemandInsightListApi(days, obj);
      if (res.code == 0) {
        // 按priority字段排序：HIGH > MEDIUM > LOW
        if (res.data && res.data.datas && Array.isArray(res.data.datas)) {
          const priorityOrder = { HIGH: 1, MEDIUM: 2, LOW: 3 };
          res.data.datas.sort((a, b) => {
            return (
              (priorityOrder[a.priority] || 999) -
              (priorityOrder[b.priority] || 999)
            );
          });
        }
        resolve(res);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      // console.log('getQuadrantData error', obj, e)
      resolve(mock_data);
    }
  });
};

export const getReqTabelDetail = async (id, param) => {
  const mock_data = {
    code: 0,
    data: {
      // "customerFeedbackList": [{
      //     "attitudeType": "NEGATIVE",
      //     "conferenceId": "1925851300935786496",
      //     "contactId": "1925851929351577600",
      //     "contactName": "杨经理",
      //     "createdTime": "2025-05-23 17:48:26",
      //     "createdUser": "许健",
      //     "customerId": "1925851132895191040",
      //     "customerName": "六福珠宝（广州）有限公司",
      //     "feedbackContent": "Mock你用了之后，我看到我们别的我们那个兄弟公司，人家也做了一个就类似于像微课一样的，十几分钟，然后宣讲一个知识点内容的，或者是做企业介绍。"
      // }],
      // "customerQaList": [{
      //     "answer": "是的，现在我们有个合作订单，财务周期太长，需要说明原因。特别是东奥商务车的订单，财务周期超出了正常范围，甚至有半年没有收到款项，这在我们内部属于超延期项目。",
      //     "conferenceId": "1933458489649184768",
      //     "contactIds": "user0",
      //     "contactNames": "",
      //     "createdTime": "2025-06-13 17:36:41",
      //     "createdUser": "时东东",
      //     "customerId": "1933139701544280064",
      //     "customerName": "东风汽车集团股份有限公司人事共享服务中心",
      //     "dimensionId": "1934495891440349186",
      //     "question": "在财务方面，我们有一个关于订单周期过长的问题，能具体说明一下吗？"
      // }]
    },
    message: "success",
  };
  return new Promise(async (resolve, reject) => {
    try {
      const res = await getCVDemandInsightDetailApi(id, param);
      if (res.code == 0) {
        const list = res || mock_data;
        resolve(list);
      } else {
        resolve(mock_data);
      }
    } catch (e) {
      console.log("getReqTabelDetail error", e);
      resolve(mock_data);
    }
  });
};

# LxCollapse 自定义折叠组件

这是一个基于 Vue 3 的自定义折叠组件，用于替代 Element Plus 的折叠组件。

## 功能特性

- ✅ 支持多个折叠项同时展开
- ✅ 支持手风琴模式（一次只能展开一个）
- ✅ 支持禁用状态
- ✅ 支持自定义标题
- ✅ 支持 v-model 双向绑定
- ✅ 支持展开/收起动画

## 使用方法

### 基础用法

```vue
<template>
  <LxCollapse>
    <LxCollapseItem title="折叠项1" name="1">
      <p>这是第一个折叠项的内容</p>
    </LxCollapseItem>
    <LxCollapseItem title="折叠项2" name="2">
      <p>这是第二个折叠项的内容</p>
    </LxCollapseItem>
  </LxCollapse>
</template>

<script setup>
import { LxCollapse, LxCollapseItem } from '@/components/LxCollapse'
</script>
```

### 手风琴模式

```vue
<template>
  <LxCollapse :accordion="true" v-model="activeName">
    <LxCollapseItem title="折叠项1" name="1">
      <p>这是第一个折叠项的内容</p>
    </LxCollapseItem>
    <LxCollapseItem title="折叠项2" name="2">
      <p>这是第二个折叠项的内容</p>
    </LxCollapseItem>
  </LxCollapse>
</template>

<script setup>
import { ref } from 'vue'
import { LxCollapse, LxCollapseItem } from '@/components/LxCollapse'

const activeName = ref('1')
</script>
```

### 禁用状态

```vue
<template>
  <LxCollapse>
    <LxCollapseItem title="正常折叠项" name="1">
      <p>这是正常折叠项的内容</p>
    </LxCollapseItem>
    <LxCollapseItem title="禁用折叠项" name="2" :disabled="true">
      <p>这个折叠项被禁用了</p>
    </LxCollapseItem>
  </LxCollapse>
</template>
```

### 自定义标题

```vue
<template>
  <LxCollapse>
    <LxCollapseItem name="1">
      <template #title>
        <span style="color: #409eff;">自定义标题</span>
      </template>
      <p>这是自定义标题的折叠项内容</p>
    </LxCollapseItem>
  </LxCollapse>
</template>
```

## API

### LxCollapse Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| accordion | 是否手风琴模式 | boolean | — | false |
| modelValue | 当前激活的面板 | string/number/array | — | — |

### LxCollapse Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| change | 切换面板的回调 | (activeNames: string \| array) |
| update:modelValue | 当前激活的面板改变时触发 | (value: string \| array) |

### LxCollapseItem Props

| 参数 | 说明 | 类型 | 可选值 | 默认值 |
|------|------|------|--------|--------|
| name | 唯一标志符 | string/number | — | — |
| title | 面板标题 | string | — | — |
| disabled | 是否禁用 | boolean | — | false |

### LxCollapseItem Slots

| 插槽名 | 说明 |
|--------|------|
| default | 面板内容 |
| title | 自定义面板标题 |

## 样式定制

组件使用 BEM 命名规范，可以通过以下类名进行样式定制：

- `.lx-collapse` - 折叠组件容器
- `.lx-collapse-item` - 折叠项容器
- `.lx-collapse-item__header` - 折叠项头部
- `.lx-collapse-item__title` - 折叠项标题
- `.lx-collapse-item__arrow` - 折叠项箭头
- `.lx-collapse-item__content` - 折叠项内容区域
- `.lx-collapse-item__body` - 折叠项内容

## 与 Element Plus 的对比

| 功能 | Element Plus | LxCollapse |
|------|-------------|------------|
| 基础折叠功能 | ✅ | ✅ |
| 手风琴模式 | ✅ | ✅ |
| 禁用状态 | ✅ | ✅ |
| 自定义标题 | ✅ | ✅ |
| 展开图标位置 | ✅ | ❌ |
| 动画效果 | ✅ | ✅ |
| 样式定制 | ✅ | ✅ | 
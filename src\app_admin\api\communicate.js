import { getHttp } from "@/js/request.js";
const _http = getHttp();

// 获取标准设置
export function getStandardSetting(params) {
  return _http.post(`api/v1/communication/standard/setting`, params);
}

export function updateStandardSetting(params) {
  return _http.post(`api/v1/communication/standard/setting`, params);
}
// 更新标准设置

// 获取维度列表
export function getDimension(params) {
  return _http.post(`api/v1/communication/dimensions/list`, params);
}
export function createDimension(params) {
  return _http.post(`api/v1/communication/dimensions`, params);
}

export function deleteDimension(id) {
  return _http.delete(`api/v1/communication/dimensions/${id}`);
}

export function updateDimension(params) {
  return _http.put(`api/v1/communication/dimensions/${params.id}`, params);
}

export function getModels(params) {
  return _http.post(`api/v1/communication/models/list`, params);
}

export function updateModel(params) {
  return _http.put(`api/v1/communication/models/${params.id}`, params);
}

export function createModel(params) {
  return _http.post(`api/v1/communication/models`, params);
}

export function deleteModel(id) {
  return _http.delete(`api/v1/communication/models/${id}`);
}

// 获取任务项列表

export function deleteTask(id) {
  return _http.delete(`api/v1/communication/tasks/${id}`);
}
export function updateTask(params) {
  return _http.put(`api/v1/communication/tasks/${params.id}`, params);
}
export function createTask(params) {
  return _http.post(`api/v1/communication/tasks`, params);
}

// 沟通总结
export function getSummaryList(params) {
  return _http.post(`api/v1/communication/summaries/list`, params);
}

// 获取维度列表
export const getDimensionListData = (params) => {
  // 模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 0,
        data: {
          datas: [
            {
              id: 1,
              name: "会议记录",
              tag: "系统",
              tagType: "primary",
            },
            {
              id: 2,
              name: "分级摘要",
              tag: "系统",
              tagType: "primary",
            },
            {
              id: 3,
              name: "问答回顾",
              tag: "系统",
              tagType: "primary",
            },
            {
              id: 4,
              name: "思维导图",
              tag: "系统",
              tagType: "primary",
            },
          ],
          totalNum: 4,
        },
      });
    }, 500);
  });
};

// 获取模板列表
export const getTemplateListData = ({ id }) => {
  // 模拟数据
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 0,
        data: {
          datas: [
            {
              id: 1,
              templateName: "通用模板",
              dimensions: "1. 会议记录1\n2. 思维导图\n3. 分级摘要",
              creator: "李小明",
              createTime: "2025-07-21 10:30",
              status: 1,
            },
          ],
          totalNum: 1,
        },
      });
    }, 500);
    // _http.get(`api/v1/communication/models/${id}`).then((resp) => {
    //     resolve(resp);
    // });
  });
};

// 删除模板
export const deleteTemplate = (id) => {
  return new Promise((resolve) => {
    _http.delete(`api/v1/communication/models/${id}`).then((resp) => {
      resolve(resp);
    });
  });
};

/// --------------------------------------- 分析维度 start-------------------------------
// 沟通分析-维度 获取列表
export const getAnalysisDimensionListData = (params) => {
  // 模拟数据
  return new Promise((resolve) => {
    _http.post("api/v1/communication/dimensions/list", params).then((resp) => {
      resolve(resp);
    });
  });
};

// 沟通分析-维度 获取详情
export const getAnalysisDimension = (id) => {
  return _http.get(`api/v1/communication/dimensions/${id}`);
};

// 沟通分析-维度 创建
export const addAnalysisDimension = (params) => {
  return _http.post("api/v1/communication/dimensions", params);
};
// 沟通分析-维度 更新
export const updateAnalysisDimension = (id, params) => {
  return _http.put(`api/v1/communication/dimensions/${id}`, params);
};
// 沟通分析-维度 删除
export const deleteAnalysisDimension = (id) => {
  return _http.delete(`api/v1/communication/dimensions/${id}`);
};
/// --------------------------------------- 分析维度 end-------------------------------

/// --------------------------------------- 分析模板 start-------------------------------
// 获取分析模板列表
export const getAnalysisModelList = (params) => {
  // 模拟数据
  return new Promise((resolve) => {
    _http.post("api/v1/communication/models/list", params).then((resp) => {
      resolve(resp);
    });
  });
};

// 删除分析模板
export const deleteAnalysisModel = (id) => {
  return new Promise((resolve) => {
    _http.delete(`api/v1/communication/models/${id}`).then((resp) => {
      resolve(resp);
    });
  });
};

// 添加分析模板
export const addAnalysisModel = (params) => {
  return _http.post("api/v1/communication/models", params);
};

// 更新分析模板
export const updateAnalysisModel = (id, params) => {
  return _http.put(`api/v1/communication/models/${id}`, params);
};

/// --------------------------------------- 分析模板 end-------------------------------

// 删除流程
export const deleteCommunicationFlow = (id) => {
  return _http.delete(`api/v1/communication/flows/${id}`);
};

export function pageCommunicationFlow(params) {
  return _http.post(`api/v1/communication/flows/list`, params);
}
export const updateCommunicationFlow = (params) => {
  return _http.put(`api/v1/communication/flows/${params.id}`, params);
};
export const createCommunicationFlow = (params) => {
  return _http.post(`api/v1/communication/flows`, params);
};

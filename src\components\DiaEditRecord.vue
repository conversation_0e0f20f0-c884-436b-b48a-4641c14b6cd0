<template>
  <el-dialog v-model="dialogVisible" title="修改沟通信息" width="500" :before-close="handleClose" class="vis_batch_edit_dia">
    <div class="bve_main">
      <FormArrange v-model:formdata="param" @customerTypeChange="onCustomerTypeChange" @tagChange="onTagChange"
        ref="refFormArrange" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { updateRecordInfo } from "@/js/api.js"
import FormArrange from "@/components/ArrangeVisitCore/FormArrange.vue";
import { trimObject } from "@/js/utils.js"
import { defaultParam } from "@/components/ArrangeVisitCore/misc.js"

const refFormArrange = ref()
const hasInit = ref(false)

const param = ref({
  ...defaultParam(),
  conferenceIds: []
})

const emit = defineEmits(["callback"]);
const dialogVisible = ref();

const show = (p) => {
  let conferenceIds = p.conferenceIds;
  if (!Array.isArray(conferenceIds)) {
    conferenceIds = conferenceIds.split(',');
  }

  param.value = {
    ...defaultParam(),
    conferenceIds,
    salesMateCustomerName: p.salesMateCustomerName || '',
    customerId: p.customerId || '',
    salesRelatedType: p.salesRelatedType || 1,
    salesMateTags: p.salesMateTags || '',
    salesGoodsCategories: p.salesGoodsCategories || '',
    salesMateType: p.salesMateType || 2,
    salesGoal: p.salesGoal || '',
    subject: p.subject || '',
    stageId: p.stageId || '',
  };
  dialogVisible.value = true;

  nextTick(() => {
    refFormArrange.value.refTagPicker.init(toRaw(param.value))
    nextTick(() => {
      hasInit.value = true
    })
  })
}

const onCustomerTypeChange = (type) => {
  // Handle customer type change if needed
}

const onTagChange = (item) => {
  // Handle tag change if needed
}

const handleClose = () => {
  dialogVisible.value = false;
};

const onConfirm = () => {
  let data = toRaw(param.value);

  data = trimObject(data);

  const { salesMateCustomerName, salesGoal, subject, stageId } = data;

  if (!salesGoal) {
    ElMessage.error(`请输入沟通目标`)
    return
  }
  if (!salesMateCustomerName) {
    ElMessage.error(`请输入沟通关联客户`)
    return
  }
  if (!subject) {
    ElMessage.error(`请输入沟通主题`)
    return
  }
  if (!stageId) {
    ElMessage.error(`请选择沟通环节`)
    return
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在提交公司信息...',
    background: 'rgba(255,255,255, 0.7)',
  })

  updateRecordInfo(data).then(resp => {
    if (resp.code == 0) {
      // 不要问为什么，问后台开发人员
      setTimeout(() => {
        ElMessage.success("更新成功");
        emit('callback', 'reload')
        loading.close()
        handleClose()
      }, 2000)
    } else {
      ElMessage.error(resp.message || '更新失败');
      loading.close()
    }
  })
}

defineExpose({
  handleClose, dialogVisible, onConfirm, show, param, refFormArrange
});
</script>

<style lang="scss">
.vis_batch_edit_dia {
  .vbp_box {
    margin-bottom: 12px;

    .vbp_title {
      margin-bottom: 12px;

      span {
        color: #F56C6C;
      }
    }

    .vbp_value {
      margin-bottom: 12px;
    }
  }
}
</style>

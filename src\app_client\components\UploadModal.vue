<template>
    <el-dialog v-model="isShow" title="上传文件" width="960px" :before-close="handleClose" class="upload_diglog">
        <div :class="['left', refFiles.length > 0 ? 'has_file' : 'no_file']" @click="onLeft" ref="refLeftbox">
            <addFile />
            <div v-if="refFiles.length > 0" class="note">
                {{ refFiles[0].name }}
            </div>
            <div v-else class="note">
                将文件拖到此处，或<div class="upload_txt">点击上传</div>
            </div>
            <div class="subnote" v-if="refFiles.length == 0">{{ subHint }}</div>
            <div class="f_btn flex-row" v-else>
                <div class="f_btn_item" @click="reChoose">重新上传</div>
                <div class="f_btn_item" @click="deleteFile">删除</div>
            </div>
        </div>
        <div class="ud_right">
            <slot name="right" />
        </div>
        <input class="fileInput" type="file" ref="fileInt" @change="changeHandle" @click="fileUploadCheck" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onOk">
                    上传
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { formatFileSize } from "@/js/utils.js"
import addFile from "@/app_client/icons/addFile.vue"

const isShow = ref(false);
const fileInt = ref();
const refLeftbox = ref();
const op_hint = ref('')
const refFiles = ref([])
const config = ref({})
const subject = ref('')
const loading = ref(false)
const subHint = ref('')
const deleteFile = (e) => {
    e.stopPropagation()
    refFiles.value = []
}

const reChoose = (e) => {
    e.stopPropagation()
    refFiles.value = []
    fileInt.value.click()
}

const onLeft = (e) => {
    if (refFiles.value.length > 0) {
        e.stopPropagation()
        return
    }
    fileInt.value.click()
}

const addDragListener = () => {
    var dropzone = refLeftbox.value;
    let i = 0;
    let timer = null
    dropzone.addEventListener('dragover', function (e) {
        i = 0;
        op_hint.value = '松开上传';
        e.preventDefault(); // 阻止默认行为
    });
    dropzone.addEventListener('dragleave', function (e) {
        i += 1;
        timer && clearTimeout(timer)
        timer = setTimeout(() => {
            if (i > 0) {
                op_hint.value = '点击 、拖拽 本地音视频文件到这里';
            }
        })
        e.preventDefault(); // 阻止默认行为
    });

    dropzone.addEventListener('drop', function (e) {
        i = 0;
        op_hint.value = '点击 、拖拽 本地音视频文件到这里';
        e.preventDefault(); // 阻止默认行为
        // 获取拖拽的文件
        _changeHandle(e.dataTransfer.files);
    });
}
const changeHandle = () => {
    _changeHandle(fileInt.value.files)
}

const _changeHandle = (files) => {
    if (files.length == 0) {
        return
    }
    refFiles.value = []
    subject.value = ''
    const file = files[0];
    const { name, size, type } = file
    if (!type) {
        ElMessage.error(`${name}文件类型不支持，已忽略`)
        return
    }
    if (size > config.value.maxSizeMb * 1048576) {
        ElMessage.error(`文件${name}大于${config.value.maxSizeMb}MB，已忽略`)
        return
    }
    const ext = name.split('.').pop();
    if (!config.value.fileTypes.includes(ext.toLowerCase())) {
        ElMessage.error(`${name}文件类型不支持，已忽略`)
        return
    }
    refFiles.value.push(file)
    const fileName = name.replace(/\.[^/.]+$/, '');
    g.clientFileStore.setFileName(fileName)
    g.emitter.emit('file_setName', fileName)
    fileInt.value.value = null;
}

const rechoose = () => {
    refFiles.value = [];
    subject.value = '';
    op_hint.value = '';
}

const onCancel = () => {
    isShow.value = false;
}

const onOk = () => {
    const { fileIcon, uploadMethod, page, checkParam } = config.value;
    if (refFiles.value.length == 0) {
        ElMessage.error('请先选择文件！')
        return
    }
    if (!checkParam() || loading.value) {
        return
    }

    loading.value = true;
    let hasNewFile = false; // 标记是否有新文件被添加

    for (let i = 0; i < refFiles.value.length; i++) {
        const file = refFiles.value[i];
        const { name, size, type } = file

        // 检查文件是否已经在上传队列中
        const fileSize = formatFileSize(size);
        if (g.clientFileStore.is_file_uploading(name, fileSize)) {
            ElMessage.warning(`文件 ${name} 已在上传队列中，跳过重复添加`);
            continue;
        }

        let param = {
            file, fileIcon, uploadMethod, page,
            startTime: `${new Date().getTime()}_${i}`,
            subject: name,
            size: fileSize,
            status: 'waiting',
        }

        // 使用新的add_file方法，它会返回是否添加成功
        const added = g.clientFileStore.add_file(param);
        if (added) {
            hasNewFile = true;
        }
    }

    if (hasNewFile) {
        g.emitter.emit('file_add_new', '');
    } else {
        ElMessage.info('没有新的文件需要上传');
    }

    loading.value = false;
    isShow.value = false;
}

const handleClose = () => {
    isShow.value = false;
}

const show = (_config) => {
    config.value = _config;
    subHint.value = `支持${_config.fileTypes.join(',')}格式，单个文件不大于${_config.maxSizeMb}Mb`
    rechoose();
    isShow.value = true;
    nextTick(() => {
        addDragListener()
    })
}

defineExpose({
    fileInt, refLeftbox, op_hint, refFiles, config,
    subject, isShow, onOk, show, reChoose, deleteFile
})

</script>

<style lang="scss">
.upload_diglog {
    .el-dialog__header {
        border-bottom: 1px solid #E9E9E9;
        margin-right: 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #E9E9E9;
    }

    .el-dialog__body {
        display: flex;
        flex-direction: row;

        .left {
            display: flex;
            flex-direction: column;
            width: 400px;
            height: 225px;
            background: #FAFAFA;
            border-radius: 4px;
            border: 1px solid #D9D9D9;
            align-items: center;
            justify-content: center;

            .note {
                font-weight: 500;
                color: #262626;
                display: flex;
                flex-direction: row;
                margin-top: 10px;

                .upload_txt {
                    color: var(--el-color-primary);
                }
            }

            .subnote {
                margin: 10px 0;
            }

            .f_btn {
                margin-top: 10px;

                .f_btn_item {
                    width: 48px;
                    font-size: 12px;
                    color: #436BFF;
                    line-height: 20px;
                    text-align: center;
                    cursor: pointer;
                }
            }
        }

        .has_file {}

        .no_file {
            cursor: pointer;
        }

        .ud_right {
            width: 440px;
            padding: 0 8px;
            overflow-y: auto;
            margin-left: 12px;
            max-height: 500px;
            overflow-y: auto;
        }

        .fileInput {
            opacity: 0;
            position: absolute;
            top: 80px;
        }
    }
}
</style>

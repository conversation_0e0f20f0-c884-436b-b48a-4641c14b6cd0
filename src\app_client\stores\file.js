import { defineStore } from 'pinia'

// upload_files:{ "file": {}, "fileIcon": { "name": "ClearIcon", "__hmrId": "55d25697", "__file": "C:/projects/xmate-web-client/src/views/visit/BtnUpload/videoIcon.vue" }, "startTime": "1724143853278_0", "subject": "202403201256.mp4", "size": "423.72 MB", "status": "waiting" }
export default defineStore('client_file', {
    state: () => ({
        fileName: '',
        upload_files: [],
    }),
    actions: {
        setFileName(name) {
            this.fileName = name;
        },
        add_file(param) {
            // status:waiting,uploading,uploaded,delete,error
            // 检查是否已经存在相同的文件（基于文件名和大小）
            const existingFile = this.upload_files.find(file =>
                file.subject === param.subject &&
                file.size === param.size &&
                ['waiting', 'uploading'].includes(file.status)
            );

            if (existingFile) {
                console.warn('文件已在上传队列中:', param.subject);
                return false; // 返回false表示添加失败
            }

            this.upload_files.push(param);
            return true; // 返回true表示添加成功
        },
        update_file_status(startTime, new_status) {
            for (let i = 0; i < this.upload_files.length; i++) {
                if (startTime == this.upload_files[i].startTime) {
                    this.upload_files[i]['status'] = new_status;
                }
            }
        },
        get_files() {
            return this.upload_files
                .filter((x) => ['waiting', 'uploading'].includes(x.status));
        },
        // 新增：检查文件是否正在上传
        is_file_uploading(fileName, fileSize) {
            return this.upload_files.some(file =>
                file.subject === fileName &&
                file.size === fileSize &&
                ['waiting', 'uploading'].includes(file.status)
            );
        },
        // 新增：清理已完成的上传记录
        cleanup_completed_files() {
            this.upload_files = this.upload_files.filter(file =>
                !['uploaded', 'delete', 'error'].includes(file.status)
            );
        },
        // 新增：获取正在上传的文件数量
        get_uploading_count() {
            return this.upload_files.filter(file =>
                ['waiting', 'uploading'].includes(file.status)
            ).length;
        }
    }
})


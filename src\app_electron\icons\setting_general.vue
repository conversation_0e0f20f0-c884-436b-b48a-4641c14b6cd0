<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="设置/关于" transform="translate(-24.000000, -61.000000)" fill="currentColor" fill-rule="nonzero">
                <g id="设置图标/通用" transform="translate(24.000000, 61.000000)">
                    <g id="编组" transform="translate(3.000000, 2.000000)">
                        <path
                            d="M6.27536231,0.197649129 C6.72376967,-0.0658830429 7.27623033,-0.0658830429 7.72463769,0.197649129 L13.2753623,3.4602157 C13.7237583,3.72374117 14,4.21075131 14,4.73780226 L14,11.2621977 C14,11.7892487 13.7237583,12.2762588 13.2753623,12.5397843 L7.72463769,15.8023509 C7.27623033,16.065883 6.72376967,16.065883 6.27536231,15.8023509 L0.724637687,12.5397843 C0.276241715,12.2762588 0,11.7892487 0,11.2621977 L0,4.73780226 C0,4.21075131 0.276241715,3.72374117 0.724637687,3.4602157 L6.27536231,0.197649129 Z M7,1.47523568 L1.44927536,4.73780226 L1.44927536,11.2621977 L7,14.5247643 L12.5507246,11.2621977 L12.5507246,4.73780226 L7,1.47523568 Z M7,5.0494537 C8.60082536,5.0494537 9.89855073,6.37045827 9.89855073,8 C9.89855073,9.62954173 8.60082536,10.9505463 7,10.9505463 C5.39917464,10.9505463 4.10144927,9.62954173 4.10144927,8 C4.10144927,6.37045827 5.39917464,5.0494537 7,5.0494537 Z M7,6.52472686 C6.48222377,6.52472686 6.00377881,6.80591231 5.74489069,7.26236342 C5.48600257,7.71881452 5.48600257,8.28118548 5.74489069,8.73763658 C6.00377881,9.19408769 6.48222377,9.47527314 7,9.47527314 C7.80041266,9.47527314 8.44927531,8.81477084 8.44927531,8 C8.44927531,7.18522916 7.80041266,6.52472686 7,6.52472686 L7,6.52472686 Z"
                            id="形状"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'PauseIcon',
}
</script>

<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="700px" class="competitor-drawer-wrap"
    :close-on-click-modal="false">
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">{{ drawerTitle }}</span>
      </div>
    </template>


    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" :rules="rules"
      require-asterisk-position="right">
      <el-form-item label="名称" prop="commonName">
        <el-input v-model.trim="formData.commonName" maxlength="50" show-word-limit placeholder="请输入竞争对手常用称呼" />
      </el-form-item>

      <el-form-item label="公司名称" prop="companyName">
        <el-autocomplete v-model="formData.companyName" :fetch-suggestions="querySearchAsync" placeholder="请输入并选择公司名称"
          maxlength="50" :debounce="1000" />
      </el-form-item>

      <el-form-item label="别称" prop="alternativeName">
        <el-input v-model="new_tag" maxlength="50" show-word-limit placeholder="添加竞争对手其他称呼，输入后，按回车创建"
          @keyup.enter="onNewTag" :disabled="tags.length >= 20" />
        <div class="tags-container" v-if="tags.length > 0">
          <el-tag v-for="tag in tags" :key="tag" closable type="info" @close="onRemoveTag(tag)" class="tag-item">
            {{ tag }}
          </el-tag>
        </div>
      </el-form-item>
    </el-form>


    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="btnCancel">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="btnOK">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { nextTick, ref, reactive, computed } from "vue";
import { ElMessage } from "element-plus";
import { searchCompanyAPI } from "@/js/api.js";
import { competitorApi } from "@/app_admin/api";

const emit = defineEmits(["callback"]);
const refForm = ref();
const new_tag = ref("");
const tags = ref([]);
let lastKeyword = "";
let lastList = [];

const drawerVisible = ref(false);
const isEditMode = ref(false);
const drawerTitle = computed(() => (isEditMode.value ? "编辑" : "新建"));

const defaultForm = {
  commonName: "",
  companyName: "",
  alternativeName: "",
};

const formData = reactive({ ...defaultForm });

const rules = {
  commonName: [
    { required: true, message: '请输入竞争对手名称', trigger: 'blur' },
  ]
};

const _resetForm = () => {
  Object.assign(formData, defaultForm);
  tags.value = [];
  new_tag.value = "";
};

const querySearchAsync = (keyword, cb) => {
  if (keyword) {
    if (keyword == lastKeyword) {
      cb(lastList);
    } else {
      searchCompanyAPI(keyword).then((resp) => {
        if (resp.hasOwnProperty("data") && resp.data.length > 0) {
          lastKeyword = keyword;
          lastList = resp.data.map((x) => ({ value: x.name }));
          cb(lastList);
        } else {
          cb([]);
        }
      });
    }
  } else {
    cb([]);
  }
};

const onNewTag = () => {
  if (new_tag.value.trim() && !tags.value.includes(new_tag.value.trim())) {
    tags.value.push(new_tag.value.trim());
    new_tag.value = "";
  }
};

const onRemoveTag = (tag) => {
  tags.value = tags.value.filter((x) => x != tag);
};

// 对外暴露：新增
const show_add = () => {
  _resetForm();
  isEditMode.value = false;
  drawerVisible.value = true;
  nextTick(() => {
    refForm.value?.resetFields?.();
  });
};

// 对外暴露：编辑
const show_edit = (data) => {
  Object.assign(formData, data || {});
  isEditMode.value = true;
  tags.value = data && data["alternativeName"] ? data["alternativeName"].split(",").filter((x) => !!x) : [];
  drawerVisible.value = true;
};

const handleClose = (done) => {
  btnCancel();
  done();
};

const btnCancel = () => {
  _resetForm();
  drawerVisible.value = false;
};

const btnOK = () => {
  refForm.value.validate((valid) => {
    if (valid) {
      const param = {
        ...formData,
        alternativeName: tags.value.join(","),
      };

      if (formData.id) {
        // 编辑
        competitorApi.updateCompetitor(formData.id, param).then((resp) => {
          if (resp.code == 0) {
            ElMessage.success("修改成功");
            drawerVisible.value = false;
            emit("callback", "reload");
          } else {
            ElMessage.error(resp.message || "修改失败");
          }
        });
      } else {
        // 新增
        competitorApi.createCompetitor(param).then((resp) => {
          if (resp.code == 0) {
            ElMessage.success("添加成功");
            drawerVisible.value = false;
            emit("callback", "reload");
          } else {
            ElMessage.error(resp.message || "添加失败");
          }
        });
      }
    }
  });
};

defineExpose({
  show_add,
  show_edit,
});
</script>

<style lang="scss">
.competitor-drawer-wrap {
  :deep(.el-drawer__header) {
    padding: 0;
    margin-bottom: 0;
    border-bottom: 1px solid #E9E9E9;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }
}

.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}


.tags-container {
  margin-top: 8px;

  .tag-item {
    margin-right: 8px;
    margin-bottom: 8px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 5px 16px;
    border-radius: 6px;
  }

  .cancel-btn {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
  }

  .confirm-btn {
    background: #436BFF;
    border-color: #436BFF;
  }
}
</style>
# DateTimePicker 日期时间选择器组件

一个基于 Element Plus 封装的日期时间选择器组件，支持同时选择日期和时间。

## 功能特性

- 🗓️ 完整的日期选择功能（年、月、日）
- ⏰ 精确的时间选择功能（时、分）
- 🎨 美观的界面设计，符合 Element Plus 设计规范
- 📱 响应式设计，支持移动端
- 🔧 高度可定制，支持多种配置选项
- 📅 支持快捷操作（今天、清空等）

## 基础用法

```vue
<template>
  <DateTimePicker v-model="dateTime" />
</template>

<script setup>
import { ref } from 'vue'
import DateTimePicker from '@/components/DateTimePicker.vue'

const dateTime = ref(null)
</script>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| modelValue | Date/String | null | 绑定值，支持 Date 对象或日期字符串 |
| placeholder | String | '请选择日期时间' | 输入框占位文本 |
| clearable | Boolean | true | 是否可清空 |
| format | String | 'YYYY-MM-DD HH:mm' | 显示格式，支持 dayjs 格式 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:modelValue | (value: Date) | 值更新时触发 |
| change | (value: Date) | 值变更时触发 |

## 高级用法

### 自定义格式

```vue
<DateTimePicker 
  v-model="dateTime" 
  format="YYYY年MM月DD日 HH时mm分"
  placeholder="请选择日期时间"
/>
```

### 不可清空

```vue
<DateTimePicker 
  v-model="dateTime" 
  :clearable="false"
/>
```

### 事件监听

```vue
<template>
  <DateTimePicker 
    v-model="dateTime" 
    @change="handleChange"
  />
</template>

<script setup>
const handleChange = (value) => {
  console.log('选择的日期时间:', value)
}
</script>
```

## 组件结构

```
DateTimePicker
├── el-popover (弹出层容器)
│   ├── el-input (输入框)
│   └── date-time-panel (选择面板)
│       ├── date-section (日期选择区域)
│       │   ├── date-header (年月导航)
│       │   ├── weekdays (星期标题)
│       │   └── calendar-grid (日历网格)
│       ├── divider (分隔线)
│       ├── time-section (时间选择区域)
│       │   ├── time-display (时间显示)
│       │   └── time-selector (时间选择器)
│       └── footer (底部按钮)
│           ├── 今天按钮
│           └── 确定按钮
```

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

1. 使用 CSS 变量覆盖主题色
2. 通过 `popper-class` 属性添加自定义类名
3. 直接修改组件源码中的样式

## 注意事项

1. 组件依赖 Element Plus 和 dayjs，请确保项目中已安装
2. 时间选择范围为 00:00 - 23:59
3. 日期选择支持跨月、跨年导航
4. 组件会自动处理时区问题

## 更新日志

### v1.0.0
- 初始版本发布
- 支持日期和时间选择
- 支持自定义格式
- 支持事件监听 
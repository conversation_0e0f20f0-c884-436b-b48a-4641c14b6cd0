<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
        <g id="客户档案管理" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="客户字段设置备份-拖选分割线展示" transform="translate(-636.000000, -201.000000)">
                <g id="编组-18" transform="translate(612.000000, 184.000000)">
                    <g id="编组-20" transform="translate(24.000000, 17.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <g id="拖动排序" fill="currentColor">
                            <path
                                d="M6,4 C6.55228475,4 7,3.55228475 7,3 C7,2.44771525 6.55228475,2 6,2 C5.44771525,2 5,2.44771525 5,3 C5,3.55228475 5.44771525,4 6,4 Z M6,9 C6.55228475,9 7,8.55228475 7,8 C7,7.44771525 6.55228475,7 6,7 C5.44771525,7 5,7.44771525 5,8 C5,8.55228475 5.44771525,9 6,9 Z M6,14 C6.55228475,14 7,13.5522847 7,13 C7,12.4477153 6.55228475,12 6,12 C5.44771525,12 5,12.4477153 5,13 C5,13.5522847 5.44771525,14 6,14 Z M10,4 C10.5522847,4 11,3.55228475 11,3 C11,2.44771525 10.5522847,2 10,2 C9.44771525,2 9,2.44771525 9,3 C9,3.55228475 9.44771525,4 10,4 Z M10,9 C10.5522847,9 11,8.55228475 11,8 C11,7.44771525 10.5522847,7 10,7 C9.44771525,7 9,7.44771525 9,8 C9,8.55228475 9.44771525,9 10,9 Z M10,14 C10.5522847,14 11,13.5522847 11,13 C11,12.4477153 10.5522847,12 10,12 C9.44771525,12 9,12.4477153 9,13 C9,13.5522847 9.44771525,14 10,14 Z"
                                id="形状结合"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<template>
  <div class="client_left_slider_wrapper" @mouseenter="updateIsCollapseDelay(false)"
    @mouseleave="updateIsCollapseDelay(true)">
    <div :class="['logo', isCollapse ? 'logo_mini' : 'logo_client']" @click="go_admin">
      <img :src="getImageUrl(isCollapse)" />
    </div>
    <el-menu class="client_left_slider" :default-active="currActive" :default-openeds="defaultOpeneds"
      @select="handleSelect" :collapse="isCollapse" :collapse-transition="false" mode="vertical">
      <template v-for="menu in menuList" :key="menu.index">
        <el-menu-item v-if="!menu.children || menu.children.length == 0" :index="menu.index">
          <div class="menu_icon">
            <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
          </div>
          <span>{{ menu.name }}</span>
        </el-menu-item>

        <el-sub-menu v-else :index="menu.index">
          <template #title>
            <div class="menu_icon">
              <InlineSvg :src="menu.icon" :alt="menu.name" preserveAspectRatio="xMidYMid meet" />
            </div>
            <span>{{ menu.name }}</span>
          </template>
          <el-menu-item v-for="child in menu.children" :key="child.index" :index="child.index">
            {{ child.name }}
          </el-menu-item>
        </el-sub-menu>
      </template>
    </el-menu>

    <div :class="['slider_footer', isCollapse ? 'sf-mini' : 'sf-full']">
      <div :class="['user-info', 'down_menu', { 'is_collapse': isCollapse }]" @click="handleHelp">
        <div class="user-icon">
          <el-icon class="nav_help">
            <NavHelp />
          </el-icon>
          <span class="name" v-if="!isCollapse">帮助中心</span>
        </div>
      </div>

      <div :class="['user-info', 'down_menu', { 'is_collapse': isCollapse }]" @click="handleDownload">
        <div class="user-icon">
          <el-icon class="nav_download">
            <NavDownload />
          </el-icon>
          <span class="name" v-if="!isCollapse">下载中心</span>
        </div>
      </div>

      <el-popover placement="right" :width="200" trigger="click">
        <template #reference>
          <div class="user-info" @click="onMagicClick('username')">
            <userIcon ref="refUserIcon" :name="user.name" :photo="user.photo" :showname="false"></userIcon>
          </div>
        </template>
        <ul class="user-info-popover">
          <li @click="go_admin()" v-if="isAdmin">前往管理后台</li>
          <!-- <li>显示语言</li> -->
          <li @click="handleLogout">退出登录</li>
        </ul>
      </el-popover>

      <div :class="['slider-footer-btn', 'flex-row', isCollapse ? 'is_collapse' : 'not_collapse']" @click="handlePin">
        <div class="sicon">
          <el-icon>
            <NavExpand v-if="isCollapse" />
            <NavFold v-else-if="isPin" />
            <NavPin v-else />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getOssUrl, jsOpenNewWindow, debounce } from "@/js/utils.js";
import userIcon from "@/components/userIcon.vue";
import NavHelp from "@/app_client/icons/nav_help.vue";
import NavDownload from "@/app_client/icons/nav_download.vue";
import NavFold from "@/app_client/icons/navFold.vue";
import NavPin from "@/app_client/icons/navPin.vue";
import NavExpand from "@/app_client/icons/navExpand.vue";
import InlineSvg from "vue-inline-svg";
// https://element-plus.org/zh-CN/component/icon.html#icon-collection
import { Grid, Edit, Position } from "@element-plus/icons-vue";

const currActive = ref(location.hash.replace("#", ""));
const user = ref({});
const isCollapse = ref(true); // 默认改为收起状态
const refUserIcon = ref(null);
const isAdmin = ref(false);
const menuList = ref([]);
const emit = defineEmits(["collapse"]);
const route = useRoute();
const isPin = ref(false);
const forceCollapse = ref(false);
const defaultOpeneds = ref(g.appStore.getStore(g.cv.keyClinetDefaultOpeneds, ['']));

watch(
  () => route.path,
  (newPath) => {
    currActive.value = newPath;
  },
  { immediate: true }
);

const handleSelect = (path) => {
  g.emitter.emit('close_popover')
  g.router.push({ path });
};

const handleLogout = () => {
  g.appStore.logout();
};

const handlePin = () => {
  isPin.value = !isPin.value;
  g.appStore.setStore(g.cv.keyClientPinStatus, isPin.value);
  if (!isPin.value) {
    forceCollapse.value = true;
  }
  _updateIsCollapse(isCollapse.value);
};

const _updateIsCollapse = (toClose) => {
  if (isPin.value && toClose) {
    return;
  }
  const target = forceCollapse.value || toClose;

  if (isCollapse.value === target) {
    return;
  }
  isCollapse.value = target;
  if (forceCollapse.value) {
    setTimeout(() => {
      forceCollapse.value = false;
    }, 300);
  }
  emit("collapse", isCollapse.value);
  if (refUserIcon.value) {
    refUserIcon.value.showName(!isCollapse.value);
  }
};

// 鼠标悬停处理函数 - 添加状态检查避免重复触发
const updateIsCollapseDelay = debounce(_updateIsCollapse, 20);

const go_admin = () => {
  if (isAdmin.value) {
    let tourl = ''
    const adminMenu = g.cacheStore.userMenu["admin"];
    if (adminMenu[0].children[0].children.length > 0) {
      tourl = adminMenu[0].children[0].children[0].index;
    } else if (adminMenu[0].children.length > 0) {
      tourl = adminMenu[0].children[0].index;
    } else if (adminMenu.length > 0) {
      tourl = adminMenu[0].index;
    }

    if (tourl) {
      g.router.push({ path: tourl });
      g.emitter.emit("switch_admin_page", 0);
    }
  }
};

const getImageUrl = (isCollapse) => {
  const imageName = isCollapse ? "logo_mini.png" : "logo_client2.png";
  return getOssUrl(imageName);
};

const handleDownload = () => {
  jsOpenNewWindow("/#/download");
};

const handleHelp = () => {
  jsOpenNewWindow("/help/index.html");
};


const clickCount = ref(0)
const clickTimer = ref(null)
const onMagicClick = (type) => {
  clickCount.value++

  // 清除之前的定时器
  if (clickTimer.value) {
    clearTimeout(clickTimer.value)
  }

  // 设置新的定时器，1秒后重置点击次数
  clickTimer.value = setTimeout(() => {
    clickCount.value = 0
  }, 1000)

  // 判断是否达到5次点击
  if (clickCount.value === 5) {
    if (type === 'username') {
      if (g.config.apiEnv == 'prod') {
        const isOldBlue = localStorage.getItem("isBlue") == "true";
        localStorage.setItem("isBlue", !isOldBlue);
        ElMessage.success(`已切换成${isOldBlue ? '正式' : 'Blue'}环境`)
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      }
    }
    clickCount.value = 0
  }
}

const demoMenuList = [
  {
    "id": "2",
    "name": "商机",
    "index": "/client/opportunity",
    "icon": "https://picobd.yunxuetang.cn/orgsv2/xxop/915864b2-6aad-4059-8a2e-8723ce4d032b/navmgmt/images/202412/b8e889adc4fe4fe2a0410eff43c9365e.svg",
    "code": "opportunity",
    "orderIndex": 5,
    "showed": 1,
    "children": []
  },
]

onMounted(() => {
  user.value = g.appStore.user;
  g.cacheStore.getUserMenu("client").then((list) => {
    if (!g.appStore.user.manager) {
      list = list.filter((x) => x.code != "my_team");
    }
    menuList.value = [...list, ...demoMenuList];

    if (list.length > 0 && list[0].children && list[0].children.length > 0) {
      defaultOpeneds.value = [list[0].index];
      g.appStore.setStore(g.cv.keyClinetDefaultOpeneds, defaultOpeneds.value);
    }


    const permissions = g.appStore.getStore(g.cv.keyMenuPermission);
    const p2 = g.cacheStore.userMenu["admin"].length > 0;
    isAdmin.value =
      p2 && !!permissions && permissions.filter((x) => x.showed == 1).length > 0;

    const isPinStatus = g.appStore.getStore(g.cv.keyClientPinStatus) == 'true';
    if (isPinStatus) {
      _updateIsCollapse(false);
      setTimeout(() => {
        handlePin();
      }, 300)
    }
  });
});

defineExpose({
  isCollapse: Grid,
  Edit,
  Position,
  currActive,
  handleSelect,
  go_admin,
  menuList,
  isAdmin,
  user,
  refUserIcon,
  InlineSvg,
});
</script>

<style lang="scss">
.client_left_slider_wrapper {
  --el-menu-bg-color: #161e54;
  --el-menu-text-color: #d0d2dc;
  --el-menu-hover-bg-color: #2e3565;

  width: 100%;
  transition: all 0.3s ease-in-out;

  .logo {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease-in-out;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      transition: all 0.3s ease-in-out;
    }
  }

  .logo_mini {
    width: 30px;
    height: 30px;
    margin: 26px 17px;
  }

  .logo_client {
    width: 148px;
    height: 30px;
    margin: 26px 32px;
  }

  .client_left_slider {
    border: none;
    height: calc(100vh - 255px);
    overflow-y: auto;
    padding: 0 10px;

    // 移动这些样式到 ::-webkit-scrollbar 选择器之前
    -ms-overflow-style: none;
    /* IE and Edge */
    scrollbar-width: none;
    /* Firefox */

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }

    .is-active:not(.is-opened) {
      background: #0460f4 !important;
      border-radius: 8px;
      color: #fff;
    }

    .el-menu-item,
    .el-sub-menu__title {
      height: 44px;
    }

    .el-menu-item,
    .el-sub-menu {
      margin: 8px 0;
      border-radius: 10px;
      transition: all 0.3s ease-in-out;
    }

    .el-sub-menu__title,
    .el-sub-menu__title:hover {
      border-radius: 10px;
    }

    .el-sub-menu:hover {
      background: #2e3565;
    }

    .el-menu-item:hover {
      background: #2e3565;
    }

    .el-menu-item * {
      vertical-align: top;
    }

    .menu_icon {
      width: 20px;
      height: 20px;
      display: flex;
      margin-right: 8px;
      align-items: center;
      justify-content: center;
      transition: margin-right 0.3s ease-in-out;

      svg {
        width: 100%;
        height: 100%;
        color: currentColor;
      }
    }

    // 菜单文字的动画效果
    span {
      transition: opacity 0.3s ease-in-out;
    }
  }

  .el-menu--collapse {

    .el-menu-item,
    .el-sub-menu__title {
      justify-content: center;
    }

    .menu_icon {
      margin-right: 0;
    }
  }

  .slider_footer {
    bottom: 0;
    position: fixed;
    transition: all 0.3s ease-in-out;

    .down_menu {
      background: #161e54;
      padding: 10px 24px;

      .user-icon {
        display: flex;
        align-items: center;
        color: #fff;

        .el-icon {
          margin-right: 10px;
          font-size: 20px;
          margin-top: 2px;
          margin-left: 5px;
        }
      }

      .name {
        margin-left: 0;
      }
    }

    .is_collapse {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px 17px;
    }

    .user-info {
      color: #fff;
      cursor: pointer;
      padding: 10px 24px;
      transition: all 0.3s ease-in-out;
      height: 44px; // 设置统一高度
      display: flex;
      align-items: center;
      box-sizing: border-box;

      .user-icon {
        color: #fff;
        display: flex;
        align-items: center;
        transition: all 0.3s ease-in-out;

        .el-icon {
          transition: all 0.3s ease-in-out;
        }
      }

      .name {
        max-width: 129px;
        font-size: 14px;
        color: #d0d2dc;
        transition: opacity 0.3s ease-in-out;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .user-info:hover,
    .slider-footer-btn:hover {
      background: #2e3565;
      border-radius: 10px;
    }

    .slider-footer-btn {
      color: #fff;
      padding: 10px 29px;
      border-top: 1px solid #2e3565;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease-in-out;
      height: 44px; // 设置统一高度
      display: flex;
      align-items: center;
      box-sizing: border-box;

      .sicon {
        display: flex;
        align-items: center;
        font-size: 20px;
        transition: all 0.3s ease-in-out;
      }

      .stext {
        margin-left: 10px;
        transition: opacity 0.3s ease-in-out;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .download-item {
      cursor: pointer;
      display: flex;
      align-items: center;
      border-radius: 8px;
      background-color: #2e3565;
      margin-left: 8px;
      padding-left: 20px;

      .download-icon {
        color: #fff;
        width: 20px;
        height: 20px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .download-content {
        margin-left: 10px;
        line-height: 1.4;

        .download-text {
          font-size: 14px;
          width: 80px;
          height: 24px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 500;
          font-size: 16px;
          color: #2bc3ff;
          line-height: 24px;
          text-align: left;
          font-style: normal;
        }

        .secondary-text {
          font-size: 12px;
          color: #bbb;
          margin-left: 4px;
        }
      }
    }
  }

  .sf-mini {
    width: 64px;

    .user-info {
      padding: 10px 16px;
    }

    .slider-footer-btn {
      padding: 10px 19px; // 修改为与统一高度匹配的padding
    }

    .download-item {
      height: 47px;
      width: 47px;
      margin: 8px;
      padding: 0;
      justify-content: center;

      .download-icon {
        margin: 0;
      }
    }
  }

  .sf-full {
    width: 201px;

    .user-info {
      padding: 10px 0px 10px 24px;
    }

    .slider-footer-btn {
      padding: 12px 29px;
      display: flex;
    }

    .is_collapse {
      flex-direction: row;
    }

    .not_collapse {

      flex-direction: row-reverse;
    }

    .download-item {
      height: 72px;
      width: 169px;
    }
  }
}

.user-info-popover {
  li {
    height: 24px;
    padding: 10px 24px;
    border-radius: 4px;
    cursor: pointer;
  }

  li:hover {
    background: #f5f5f5;
  }
}

.client_left_slider_wrapper {
  .client_left_slider .menu_icon svg {
    width: 22px;
    height: 22px;
    color: currentColor;
  }
}

.sf-mini {
  .menu-item {
    padding: 14px 20px;

    svg {
      width: 22px;
      height: 22px;
      display: block;
    }
  }

  &.active {
    background-color: var(--el-color-primary);

    svg {
      color: #fff;
    }
  }
}

// 确保 SVG 图标的基础样式
.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}
</style>

<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="template-table-drawer"
    :close-on-click-modal="false">
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">编辑模板</span>
      </div>
    </template>

    <div class="drawer-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" require-asterisk-position="right">
        <div style="margin-bottom: 24px;">
          <el-form-item label="模版名称" prop="name">
            <el-input v-model="form.name" show-word-limit maxlength="50" />
          </el-form-item>
        </div>

        <div class="form-section">
          <el-form-item label="关联维度" :rules="behaviorRules">
            <div v-sortable @end.prevent="handleDragEnd" class="behavior-list">
              <div v-for="(behaviorText, index) in itemsArrList" :key="index" class="behavior-item">
                <div class="drag-handle">
                  <img style="width: 16px; height: 16px;" :src="getOssUrl('icon-sort.png', 3)" alt="">
                  <p class="behavior-text single-line-ellipsis ">{{ behaviorText.label }}</p>
                  <el-icon>
                    <Delete @click="removeBehavior(index)" />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>

        </div>
        <LxMultiButtonSelect v-model="form.itemsArr" :options="options" :min="1">
          <template #label="{ toggleDropdown }">
            <div class="add-behavior-section">
              <el-button class="add-behavior-btn" text @click="toggleDropdown">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>添加关联维度</span>
              </el-button>
            </div>
          </template>
        </LxMultiButtonSelect>

      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete, Menu } from '@element-plus/icons-vue'
import LxMultiButtonSelect from '@/components/LxMultiButtonSelect/index.js'
import { getOssUrl } from "@/js/utils"

const props = defineProps({
  dimensions: {
    type: Array,
    default: () => ([])
  }
})
const emit = defineEmits(['success', 'close'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)


const options = computed(() => {
  return props.dimensions.map(item => ({
    label: item.name,
    value: item.id
  }))
})

const itemsArrList = computed(() => {
  return [...form.itemsArr].map(v => options.value.find(item => item.value == v))
})


const form = reactive({
  name: '',
  items: [],
  itemsArr: []
})

const rules = {
  name: [
    { required: true, message: '请输入模板名称', trigger: 'blur' },
  ]
}

const behaviorRules = [
  { required: true, message: '', trigger: 'blur' },
]

const removeBehavior = (index) => {
  form.itemsArr.splice(index, 1)
}

const openDrawer = (data = null) => {
  drawerVisible.value = true
  form.itemsArr = []
  itemsArrList.value = []
  form.items = []

  if (data) {
    // 编辑模式
    isEditMode.value = true
    currentId.value = data.id
    form.name = data.name
    form.items = [...data.items]
    form.itemsArr = data.items && data.items.length > 0 ? data.items.map(item => item.dimensionId) : []
  }
}

const closeDrawer = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const handleDragEnd = (evt) => {
  // 交换数组元素
  const movedItem = itemsArrList.value.splice(evt.oldIndex, 1)[0]
  itemsArrList.value.splice(evt.newIndex, 0, movedItem)
}

const submitForm = () => {
  if (form.itemsArr.length < 1) {
    ElMessage.warning({ message: '至少保留1个维度', grouping: true })
    return
  }
  formRef.value.validate((valid) => {
    if (valid) {
      if (form.itemsArr.length === 0) {
        ElMessage.error('请选择关联维度')
        return false
      }
      // 处理表单提交
      const formData = {
        id: currentId.value,
        name: form.name,
        items: itemsArrList.value.map(item => ({
          dimensionId: item.value,
          id: form.items.find(i => i.dimensionId == item.value)?.id || ''
        }))
      }

      emit('success', {
        mode: isEditMode.value ? 'edit' : 'add',
        data: formData
      })
      closeDrawer()
    } else {
      ElMessage.error('请检查输入内容')
      return false
    }
  })
}

const handleClose = (done) => {
  closeDrawer()
  done()
  emit('close')
}

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer
})
</script>

<style scoped lang="scss">
p {
  margin: 0;
  padding: 0;
}


.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;


  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.behavior-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

.behavior-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;



  .drag-handle {
    cursor: move;
    padding: 12px;
    display: flex;
    align-items: center;
    color: #909399;
    background: #F9FAFC;
    border-radius: 8px 8px 8px 8px;
    width: 100%;
    gap: 12px;
    margin-bottom: 4px;
    box-sizing: border-box;

    .behavior-text {
      display: flex;
      flex: 1;
      font-weight: 400;
      font-size: 14px;
      color: #262626;
      line-height: 22px;
      text-align: left;
      display: block;
    }
  }


  .char-count {
    position: absolute;
    right: 12px;
    bottom: 12px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }

}

.add-behavior-btn {
  width: 100%;
  margin-top: 12px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8px 8px 8px 8px;
  border: 1px dashed #436BFF;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #436BFF;
  line-height: 22px;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 5px 16px;
    border-radius: 6px;
  }

  .cancel-btn {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
  }

  .confirm-btn {
    background: #436BFF;
    border-color: #436BFF;
  }
}
</style>
<style lang="scss">
.template-table-drawer {
  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9 !important;
  }

  .el-drawer__body {
    padding: 24px;
    box-sizing: border-box;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }

  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
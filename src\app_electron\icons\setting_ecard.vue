<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
        <g id="客户端新增消息中心" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="客户端-设置" transform="translate(-24.000000, -237.000000)">
                <g id="编组-5备份-7" transform="translate(24.000000, 237.000000)">
                    <g id="1.基础-/-2.Icon-图标-/-16px-/-拖动排序备份-5">
                        <g id="编组-17">
                            <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                            <path
                                d="M15,2.5 L5,2.5 C3.96446609,2.5 3.125,3.33946609 3.125,4.375 L3.125,15.625 C3.125,16.6605339 3.96446609,17.5 5,17.5 L15,17.5 C16.0355339,17.5 16.875,16.6605339 16.875,15.625 L16.875,4.375 C16.875,3.33946609 16.0355339,2.5 15,2.5 Z <PERSON>5,3.75 L15,3.75 C15.345178,3.75 15.625,4.02982203 15.625,4.375 L15.625,15.625 C15.625,15.970178 15.345178,16.25 15,16.25 L5,16.25 C4.65482203,16.25 4.375,15.970178 4.375,15.625 L4.375,4.375 C4.375,4.02982203 4.65482203,3.75 5,3.75 Z"
                                id="形状结合" fill="currentColor"></path>
                            <ellipse id="椭圆形" stroke="currentColor" stroke-width="1.13636364" cx="9.99876033"
                                cy="8.97306397" rx="1.58057851" ry="1.58670034"></ellipse>
                            <path
                                d="M12.9545455,14.0909091 C12.9545455,12.1569827 11.6317504,10.5892256 10,10.5892256 C8.3682496,10.5892256 7.04545455,12.1569827 7.04545455,14.0909091"
                                id="椭圆形" stroke="currentColor" stroke-width="1.13636364" stroke-linecap="round"></path>
                            <path
                                d="M11.2568182,4.54545455 C11.6019962,4.54545455 11.8818182,4.79983821 11.8818182,5.11363636 C11.8818182,5.42743452 11.6019962,5.68181818 11.2568182,5.68181818 L8.75681818,5.68181818 C8.41164021,5.68181818 8.13181818,5.42743452 8.13181818,5.11363636 C8.13181818,4.79983821 8.41164021,4.54545455 8.75681818,4.54545455 L11.2568182,4.54545455 Z"
                                id="矩形备份-9" fill="currentColor" fill-rule="nonzero"></path>
                        </g>
                        <g id="编组" transform="translate(3.125000, 2.500000)"></g>
                    </g>
                    <g id="编组-16" transform="translate(7.045455, 6.818182)"></g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'PauseIcon',
}
</script>

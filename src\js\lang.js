export default (key, pre = "") => {
  const messages = {
    name: "名称",
    riskCount: "预警",
    operation: "操作",
    date: "日期",
    time: "时间",
    type: "类型",
    VisitNews: "沟通动态",
    meetCount: "沟通次数",
    todosCount: "待办",
    attendeeCount: "客户参会人",
    competitorNames: "竞争对手",
    subject: "沟通主题",
    recordDurationSeconds: "持续时间",
    salesMateTags: "主题标签",
    salesMateCustomerName: "客户名称",
    syncStatus: "CRM同步状态",
    status: "状态",
    reportStatus: "处理状态",
    completeStatus: "完成状态",
    tag: "客户标签",
    spokesman: "发言人",
    hostNames: "销售负责人",
    hostDeptNames: "部门",
    deptName: "部门",
    hostName: "销售姓名",
    managerViewed: "部门经理是否查看",
    createdTime: "创建时间",
    totalVisitPlanCount: "沟通计划总数",
    visitPlanCustomerCount: "沟通计划覆盖客户数",
    visitPlanOpportunityCount: "沟通计划覆盖商机数",
    visitPlanCompletionRate: "沟通计划完成率",
    totalVisitCount: "沟通总次数",
    performanceExpectationsRate: "销售能力评估及格率",
    performanceExpectationsRate: "销售表现符合预期率",
    averageVisitPlanPerPerson: "人均沟通计划数",
    averageVisitsPerPerson: "人均沟通次数",
    averageVisitDurationPerPerson: "人均沟通时长(小时)",
    completedVisitPlanCount: "沟通计划完成数",
    salesAbilityPassCount: "能力评估达标数",
    salesAbilityPassRate: "能力评估达标率",
    teamMemberCount: "团队人数",
    visitTargetAchievedCount: "任务达成达标数",
    visitTargetAchievementRate: "沟通目标达成率",
    visitedCustomerCount: "沟通覆盖客户数",
    orgName: "组织名称",
    managerName: "部门经理",
    totalVisitDuration: "沟通总时长(小时)",
    taskCompletePassRate: "任务完成达标率",
    userAccount: "用户账号",
    userName: "用户姓名",
    ongoing: "进行中",
    completed: "已完成",
    notCompleted: "未完成",
    notStarted: "待开始",
    hasPaused: "已暂停",
    scheduleStartTime: "计划时间",
    viewRecordLink: "沟通记录链接",
    planDuration: "计划持续时间",
    customer_name: "客户名称",
    ability_name: "能力名称",
    task_name: "任务名称",
    address: "地址",
    operation: "操作",
    date: "日期",
    time: "时间",
    solutionName: "方案名称",
    address: "地址",
    operation: "操作",
    usage: "使用情况",
    edit: "编辑",
    template: "模板管理",
    category: "分类管理",
    doc: "文件管理",
    use_records: "使用记录",
    categoryName: "分类名称",
    downloadPath: "下载路径",
    fileName: "文件名",
    fileSize: "文件大小",
    slideSize: "页数",
    orgId: "租户 Id",
    parseStatus: "解析状态",
    createdTime: "创建时间",
    createdUserName: "创建人",
    fileCount: "文件数量",
    assemblyMethod: "组合方式",
    label: "大纲设置项的标签",
    level: "对应层级",
    orderId: "排序 id",
    parentId: "母节点 id",
    templateId: "模板 id",
    updatedTime: "更新时间",
    DO_NOTHING: "未解析",
    IN_PARSE_PROCESS: "解析中",
    PARSE_SUCCESS: "解析成功",
    PARSE_FAIL: "解析失败",
    IN_VECTOR_PROCESS: "解析中",
    VECTOR_SUCCESS: "解析成功",
    VECTOR_FAIL: "解析失败",
    createdPptCount: "本月引用次数",
    departmentName: "所属部门",
    scenario: "应用场景",
    description: "场景说明",
    ct_description: "描述",
    companyName: "客户名称",
    accessCount: "访问次数",
    appName: "应用名称",
    deptName: "部门",
    headerCount: "访问人数",
    userName: "姓名",
    userAccount: "账号",
    position: "岗位",
    interview_assistant: "面试助手",
    commodity_management: "商品管理",
    listName: "商品清单名称",
    onShelf: "状态",
    richness: "内容丰富度",
    supplements: "推荐补充点",
    templateName: "关联沟通模板",
    SalesMeetingAssistant: "销售沟通助手",
    ThemeManagement: "主题管理",
    PlanCreationAdmin: "方案创作管理",
    OverviewUsage: "使用总览",
    investigation: "企业信息侦查机",
    behavior: "行为表现",
    fullname: "姓名",
    username: "账号",
    positionName: "岗位",
    meddic: "MEDDIC",
    visitDetail: "沟通详情",
    salesGoal: "沟通目标",
    salesAchievementStatus: "目标达成情况",
    salesAbilityScore: "能力评估得分",
    taskCompleteRatio: "任务达成率",
    report_name: "名称",
    created_periods: "统计周期",
    confCount: "沟通次数",
    customerName: "客户名称",
    deptName: "部门",
    salesName: "销售负责人",
    customer_type_name: "客户类型名称",
    fieldName: "字段名称",
    fieldStatus: "字段状态",
    fieldType: "字段类型",
    isRequired: "是否必填",
    String: "单行文本",
    TextArea: "多行文本",
    DateTime: "日期时间",
    Date: "日期",
    Integer: "整数",
    Select: "单选列表",
    MultiSelect: "多选列表",
    page_name: "页面名称",
    typeName: "客户类型",
    salesSsoUserNames: "负责人",
    hostUserName: "创建人",
    user_requirement: "用户需求",
    belonging_dimension: "所属维度",
    number_of_mentions: "提及次数",
    suggestion: "建议",
    priority: "优先级",
    dimension: "维度",
    modelName: "模型名称",
    testScore: "达标分值",
    relatedAbilities: "关联能力项",
    updater: "更新人",
    updateTime: "更新时间",
    processName: "流程名称",
    communicationSteps: "沟通环节",
    relatedName: "关联",
    relatedTaskItems: "关联任务项",
    relatedDimension: "关联维度",
    templateNameForSummary: "模板名称",
    followSsoUserNames: '协同跟进人',
    sortOrder: '排序值',
    opportunity_name: '商机名称',
    opportunity_status: '商机状态',
    communication_dynamics: '沟通动态',
    communicationCount: '沟通次数',
    relatedCustomer: '关联客户'
  };
  const pre_key = `${pre}_${key}`;
  if (pre_key in messages) {
    return messages[pre_key];
  } else if (key in messages) {
    return messages[key];
  } else {
    return key;
  }
};

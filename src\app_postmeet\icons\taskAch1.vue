<template>
    <svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1">
        <defs>
            <linearGradient x1="86.2250166%" y1="100%" x2="0.463618154%" y2="3.87081577%" id="linearGradient-1">
                <stop stop-color="#4CD2F4" offset="0%"></stop>
                <stop stop-color="#7A1EFF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="辅导_任务达成" transform="translate(-52, -335)">
                <g id="编组-7" transform="translate(32, 207)">
                    <g id="编组-2" transform="translate(20, 124)">
                        <g id="icon/错" transform="translate(0, 4)">
                            <polygon id="路径" points="0 0 16 0 16 16 0 16"></polygon>
                            <path
                                d="M7.03590175,11.850062 C6.81096842,12.3651287 6.09814175,12.3651287 5.87323508,11.850062 L5.28813508,10.5099287 C4.76744175,9.31739533 3.83022842,8.368062 2.66116842,7.84912867 L1.05068842,7.134262 C0.538659083,6.90699533 0.53865975,6.16211533 1.05068842,5.93483533 L2.61086842,5.24228867 C3.80998842,4.71000867 4.76401508,3.72553533 5.27581508,2.49227533 L5.86850175,1.06414867 C6.08842842,0.534173333 6.82070175,0.534172 7.04063508,1.06414867 L7.63330175,2.49228867 C8.14510175,3.72553533 9.09910175,4.71000867 10.2982351,5.24228867 L11.8584351,5.93483533 C12.3704351,6.16211533 12.3704351,6.90699533 11.8584351,7.134262 L10.2479684,7.84912867 C9.07890175,8.368062 8.14170175,9.31739533 7.62096842,10.5099287 L7.03590175,11.850062 Z M2.98600842,6.53455533 C4.50990175,7.21099533 5.74898175,8.322862 6.45455508,9.85272867 C7.16016842,8.322862 8.39923508,7.21099533 9.92310175,6.53455533 C8.38103508,5.850042 7.13803508,4.68397533 6.45456175,3.12691533 C5.77109508,4.683982 4.52808175,5.850042 2.98600842,6.53455533 Z M12.8939018,15.1129287 L13.0584351,14.7357953 C13.3517684,14.0633953 13.8801018,13.5279953 14.5393018,13.2351287 L15.0462351,13.009862 C15.3204351,12.888062 15.3204351,12.4895953 15.0462351,12.3677953 L14.5677018,12.1551287 C13.8915018,11.8547287 13.3537018,11.2995287 13.0653684,10.604262 L12.8964351,10.1967287 C12.7786351,9.91272867 12.3859684,9.91272867 12.2681684,10.1967287 L12.0992351,10.604262 C11.8109684,11.2995287 11.2731684,11.8547287 10.5969684,12.1551287 L10.1183684,12.3677953 C9.84423508,12.4895953 9.84423508,12.888062 10.1183684,13.009862 L10.6253018,13.2351287 C11.2845684,13.5279953 11.8128351,14.0633953 12.1061684,14.7357953 L12.2707684,15.1129287 C12.3911684,15.3889953 12.7734351,15.3889953 12.8939018,15.1129287 Z M12.2093018,12.684262 L12.5843018,12.311862 L12.9515018,12.684262 L12.5843018,13.0461953 L12.2093018,12.684262 Z"
                                id="形状" fill="url(#linearGradient-1)" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>

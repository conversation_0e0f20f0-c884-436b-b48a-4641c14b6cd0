<template>
    <div class="opportunity_wrap">
        <el-card class="opportunity_card card_no_border" shadow="never">
            <OpportunityCategoryTabs @tab-change="onCategoryChange" />
            <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
                <template #_header_left>
                    <el-select v-model="datas.param.status" placeholder="进行中" clearable @change="onSearch"
                        style="width: 120px; margin-right: 16px;">
                        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </template>

                <template #header_communication_dynamics>
                    <CommunicationDynamicsHeader />
                </template>

                <template #header_meddic>
                    <MeddicHeader />
                </template>

                <!-- <template #col_relatedCustomer="{ row }">
                    <div style="width: 80px;"> - </div>
                </template> -->


                <template #col_opportunity_name="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'competition')">
                        {{ row.name }}
                    </div>
                </template>

                <template #col_opportunity_status="{ row }">
                    <OpportunityStatus :row="row" style="width: 80px;" />
                </template>

                <template #col_riskCount="{ row }">
                    <ColRiskCount :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_communication_dynamics="{ row }">
                    <CommunicationDynamics :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_meddic="{ row }">
                    <Meddic :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_todosCount="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'todo')">
                        {{ formatCustNum(row.todosCount) }}
                    </div>
                </template>

                <template #col_attendeeCount="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'customerAttend')">
                        {{ formatCustNum(row.attendeeCount) }}
                    </div>
                </template>

                <template #col_competitorNames="{ row }">
                    <ColCompetitor :row="row" @callback="onShowOpportunity" />
                </template>

                <template #col_communicationCount="{ row }">
                    <div class="link" @click="onShowOpportunity(row, 'communication')">
                        {{ formatCustNum(row.communicationCount) || '-' }}
                    </div>
                </template>
            </MyTable>
        </el-card>
        <drawerCustomer ref="refDrawer" @callback="onCustomerDrawer" :team="false" />
    </div>
</template>

<script setup>
import { apiOpportunity } from "@/app_client/api";
import { getColumns, column_widths } from "./misc.js";
import { getFormFields } from "@/js/api.js"
import { formatCustNum } from "@/app_client/tools/utils.js";
import MyTable from "@/components/Table.vue";
import ColRiskCount from "@/app_client/views/customer/ColRiskCount.vue";
import Meddic from "@/app_client/views/customer/Meddic.vue";
import MeddicHeader from "@/app_client/views/customer/MeddicHeader.vue";
import drawerCustomer from "@/components/drawerCustomer.vue";
import OpportunityStatus from "./OpportunityStatus.vue";
import CommunicationDynamics from "./CommunicationDynamics.vue";
import CommunicationDynamicsHeader from "./CommunicationDynamicsHeader.vue";
import ColCompetitor from "./ColCompetitor.vue";
import OpportunityCategoryTabs from "./OpportunityCategoryTabs.vue";

const refTable = ref();
const refDrawer = ref();
const statusOptions = ref([]);

const columns = getColumns();

const datas = reactive({
    tableid: "opportunity_client_1",
    param: {
        status: "进行中",
        searchKey: "",
        pageNumber: 1,
        pageSize: 20
    },
    need_init_load: true,
    show_search: true,
    need_header: true,
    form: {},
    fixed_column: "opportunity_name",
    sortable: "custom",
    modal_type: "link",
    search_ph: "请输入  ",
    delete_hint_column: "opportunity_name",
    show_link_column: false,
    columns: columns,
    always_show_columns: ["opportunity_name"],
    sortables: ["riskCount", "communicationCount", "todosCount", "attendeeCount"],
    template: columns,
    template_header: ["communication_dynamics", "meddic"],
    column_widths,
    urlGet: apiOpportunity.getOpportunityList,
});

const cbDatas = (action, data) => {
    if (action === "after_search") {
        // 处理搜索后的数据
    }
};

const onSearch = () => {
    refTable.value.search();
};

const onCustomerDrawer = (action, data) => {
    if (action == "reload") {
        onSearch()
    }
}

const onCategoryChange = (category) => {
    console.log('分类切换:', category);
    // 这里可以添加分类切换后的逻辑
    // 例如：重新加载数据、更新筛选条件等
};

const onShowOpportunity = (row, page = "") => {
    // 处理商机详情查看
    console.log('查看商机:', row, page);
    const test_row = { "attendeeCount": 0, "companyId": "1721462860900532224", "companyLogoUrl": "https://image.qcc.com/logo/d604631f820ea565a31350846cd0d362.jpg", "competitorCount": 0, "customerTypeId": "a0f71c39-1a5f-11f0-88dd-1070fd9360d0", "customerTypeName": "企业类型-2", "enableBusinessRule": false, "hostDeptNames": "苏州N01,wumx-xmate,Xmate-测试11,Xmate-测试-2级部门", "hostNames": "靳建锋,陈晨,wumx_xmate02,admin王伟,wumx_xmate01,liza103_xm,吴香香,wumx0307_01", "id": "1858330691603832832", "latestMeetTime": "2025-07-27 20:22:52", "meddicList": [{ "content": "客户面临的主要痛点包括勋章管理功能目前为手动操作，导致操作繁琐且耗时；积分榜和经验值功能缺乏游戏化元素，无法有效激励员工；子学院与主学院数据同步存在问题，特别是排行榜的关联性；讲师认证的评估标准不够明确，希望优化评估流程。这些问题对员工参与度和系统效率影响较大。", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "FULL", "meddicType": "IDENTIFY_PAIN" }, { "content": "", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "NOT_FILLED", "meddicType": "CHAMPION" }, { "content": "客户评估解决方案的标准包括讲师认证的评估（公开课数量占50%、课件质量占25%、满意度评分占25%）、积分榜和经验值的游戏化元素结合、勋章管理的自动化功能，以及子学院与主学院数据同步的需求，特别是排行榜的关联性。这些标准反映了客户对功能自动化、数据整合和员工激励的重视。", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "FULL", "meddicType": "DECISION_CRITERIA" }, { "content": "客户计划在Q4优化积分榜和经验值功能，并希望在Q1完成手动毕业功能的优化。讲师认证预计在过年后安排，PPT优化和反馈时间希望尽快完成。目前未提及明确的审批流程或决策步骤。", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "INSUFFICIENT", "meddicType": "DECISION_PROCESS" }, { "content": "客户的关键绩效指标（KPIs）包括学习人数、课程数量、考试通过率（92%）、讲师认证的满意度评分（25%）、课件质量（25%）、公开课数量（50%）等。客户希望通过解决方案优化积分榜、经验值、勋章管理等功能，提升员工的学习参与度和积极性。此外，客户关注子学院与主学院的数据同步，特别是学分积分的关联性。讲师认证的评估标准包括培训、授课、PPT制作、满意度评分等。解决方案将帮助客户实现数据同步、提升员工参与度，并优化讲师认证流程。", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "FULL", "meddicType": "METRICS" }, { "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "NOT_FILLED", "meddicType": "OPPORTUNITY_SCORE" }, { "content": "", "contentLocked": 0, "customerId": 1858330691603832800, "meddicStatus": "NOT_FILLED", "meddicType": "ECONOMIC_BUYER" }], "meetCount": 2, "meets": [{ "id": "1944704381475622912", "recognitionPath": "cos://ali-static-meet-1328436710/tingwu/twjob/2025/07/14/1944704381475622912_custom.json", "startTime": "2025-07-14 18:23:50", "tag": "方法论测试", "date": "2025-07-14", "process": 25.05339943749979, "color": "#5B8FF9" }, { "id": "1949445381389660160", "recognitionPath": "cos://ali-static-meet-1328436710/tingwu/twjob/2025/07/27/1949445381389660160_custom.json", "startTime": "2025-07-27 20:22:52", "tag": "方法论测试", "date": "2025-07-27", "process": 67.25555079732332, "color": "#5B8FF9" }], "name": "云学堂信息科技（江苏）有限公司", "riskCount": 2, "tag": [" 测试2", "测试3asd"], "tags": " 测试2,测试3asd", "todosCount": 0, "_index": 1 };
    const testparam = { "categoryIds": [], "startTime": "2025-07-07 00:00:00", "endTime": "2025-08-06 23:59:59", "tags": [], "customerTypeId": "", "dptIds": [], "userIds": [] }
    refDrawer.value.show(test_row, page, testparam, false);
};

// 获取状态选项
const loadStatusOptions = async () => {
    try {
        const response = await apiOpportunity.getOpportunityStatusList();
        if (response.code === 0) {
            statusOptions.value = response.data;
        }
    } catch (error) {
        console.error('加载状态选项失败:', error);
    }
};
const getExtFiledLanguage = () => {
    return new Promise(async (resolve) => {
        const res = await getFormFields('CUSTOMER_TPL');
        const langData = {};
        res.data.forEach(x => {
            langData[x.id] = x.fieldName;
        })
        g.appStore.addExtFiledLanguage(langData);
        resolve(true);
    }).catch(err => {
        console.log(err);
        resolve(false);
    });
}

onMounted(async () => {
    loadStatusOptions();
    await getExtFiledLanguage();
});

</script>

<style lang="scss" scoped>
.opportunity_wrap {
    background: #f7f9fe;
    padding: 24px;

    .table_wrap {
        margin: 4px;
    }

    :deep(.search_box) {
        .search_input {
            width: 215px !important;
        }
    }

    :deep(.table_class) {
        height: calc(100vh - 232px);

        .el-scrollbar {
            overflow: auto;
        }

        table {
            border-collapse: collapse;

            :deep(td:first-child) {
                border-right: 2px solid black;
            }

            .link {
                cursor: pointer;
            }
        }
    }

    :deep(table) {
        min-width: 1400px !important;
    }
}
</style>
<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
        <defs>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="lg_todo_1">
                <stop stop-color="#78A4FF" offset="0%"></stop>
                <stop stop-color="#436BFF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="沟通过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="对话分析--icon集合" transform="translate(-520.000000, -311.000000)" fill="url(#lg_todo_1)"
                fill-rule="nonzero">
                <g id="编组-18备份-6" transform="translate(500.000000, 255.000000)">
                    <g id="编组-2" transform="translate(20.000000, 56.000000)">
                        <g id="编组" transform="translate(1.000000, 1.000000)">
                            <path
                                d="M13.5,0 C14.6307728,0 15.6641325,0.41707482 16.454637,1.10578224 L8.63865289,8.92256445 L4.99470535,5.27861691 L3.40371509,6.86960716 L8.63865289,12.104545 L17.7439608,3.00005485 C17.9097757,3.46920773 18,3.97406736 18,4.5 L18,13.5 C18,15.9852814 15.9852814,18 13.5,18 L4.5,18 C2.01471863,18 0,15.9852814 0,13.5 L0,4.5 C0,2.01471863 2.01471863,0 4.5,0 L13.5,0 Z"
                                id="路径"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>

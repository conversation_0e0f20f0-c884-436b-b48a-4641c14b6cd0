<template>
    <div class="visit_btn_upload">
        <el-button type="primary" @click="onShowUpload">上传</el-button>
        <UploadModal ref="refUpload" @begin_upload="onBeginUpload">
            <template #right>
                <div class="form-container">
                    <FormArrange v-model:formdata="formdata" ref="refFormArrange" />
                </div>
            </template>
        </UploadModal>
    </div>
</template>

<script setup>
import UploadModal from "@/app_client/components/UploadModalFileId.vue";
import formRoleNum from "./formRoleNum.vue";
import { onMounted } from "vue";
import { uploadVideo } from "@/app_client/tools/api.js"
import FormArrange from "@/components/ArrangeVisitCore/FormArrange.vue";

const refChatWrap = ref();
const refUpload = ref();
const refFormArrange = ref();

const defValue = {
    roleNum: 0,
    fileName: '',
    subject: '',
    stageId: '', //沟通环节Id:
    customerId: '',
    opportunityId: '', //商机Id:商机ID和客户Id必须填写一个
    fileId: '' //上传文件后的fileId
}

const formdata = ref({
    ...defValue
})

const _checkParam = () => {
    const { subject, stageId, opportunityId, customerId } = formdata.value;
    if (!opportunityId && !customerId) {
        if (!customerId) {
            ElMessage.error('沟通关联客户不可以为空');
        }
        if (!opportunityId) {
            ElMessage.error('沟通关联商机不可以为空');
        }
        return false;
    }

    if (!subject) {
        ElMessage.error('沟通主题不可以为空');
        return false;
    }

    if (!stageId) {
        ElMessage.error('沟通环节不可以为空');
        return false;
    }

    return true;
}

const onBeginUpload = async (fileId) => {
    const formData = {
        fileId: fileId,
        ...formdata.value
    }

    const res = await uploadVideo(formData);
    console.log('uploadVideo res', res)
    if (res.code != 0) {
        ElMessage.error(res.message)
        return
    }
    ElMessage.success('上传成功')
    refUpload.value.handleClose()
    formdata.value = { ...defValue };
    refFormArrange.value.refTagPicker.init({ ...defValue })
}

const onShowUpload = () => {
    const cfg = {
        page: 'visit',
        fileTypes: ["mp4", "mp3", "m4a", "aac", "wav"],
        maxSizeMb: 1024,
        fileIcon: 'video',
        checkParam: _checkParam
    }
    refUpload.value.show(cfg);
    nextTick(() => {
        formdata.value = { ...defValue };
        refFormArrange.value.refTagPicker.init({ ...defValue })
    })
}

onMounted(() => {
    g.emitter.on('file_setName', (fileName) => {
        formdata.value.fileName = fileName;
    })

    // 监听上传成功事件
    g.emitter.on('upload_success', (uploadResult) => {
        console.log('Upload success:', uploadResult)
        // 将fileId保存到表单数据中
        formdata.value.fileId = uploadResult.fileId
        // 可以在这里添加其他处理逻辑
    })
})

onUnmounted(() => {
    g.emitter.off('file_setName');
    g.emitter.off('upload_success');
});

defineExpose({
    formRoleNum, refChatWrap, UploadModal, refUpload, onShowUpload, refFormArrange
})
</script>

<style lang="scss">
.visit_btn_upload {
    .el-dialog__body {
        padding-top: 12px;

        .form-container {
            .av_item {
                width: 99%;
                display: flex;
                flex-direction: column;
                margin: 10px 0;

                .av_item_value {
                    width: 100%;
                }
            }
        }
    }
}
</style>

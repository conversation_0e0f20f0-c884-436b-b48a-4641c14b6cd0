<template>
    <el-dialog v-model="isShow" title="上传文件" width="960px" :before-close="handleClose" class="upload_diglog">
        <div :class="['left', refFiles.length > 0 ? 'has_file' : 'no_file']" @click="onLeft" ref="refLeftbox">
            <addFile />
            <div v-if="refFiles.length > 0" class="note">
                {{ refFiles[0].name }}
            </div>
            <div v-else class="note">
                将文件拖到此处，或<div class="upload_txt">点击上传</div>
            </div>
            <div class="subnote" v-if="refFiles.length == 0">{{ subHint }}</div>

            <!-- 上传进度显示 -->
            <div v-if="refFiles.length > 0 && uploadStatus" class="upload-progress">
                <div class="upload-status">{{ uploadStatus.text }}</div>
                <el-progress v-if="uploadStatus.showProgress" :percentage="uploadStatus.percentage"
                    :status="uploadStatus.status" :stroke-width="6" />
            </div>

            <div class="f_btn flex-row" v-else-if="refFiles.length > 0">
                <div class="f_btn_item" @click="reChoose">重新上传</div>
                <div class="f_btn_item" @click="deleteFile">删除</div>
            </div>
        </div>
        <div class="ud_right">
            <slot name="right" />
        </div>
        <input class="fileInput" type="file" ref="fileInt" @change="changeHandle" @click="fileUploadCheck" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onOk" :loading="uploading" :disabled="!canConfirm">
                    {{ buttonText }}
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { formatFileSize } from "@/js/utils.js"
import addFile from "@/app_client/icons/addFile.vue"
import { createFile } from "@/js/api.js"

const isShow = ref(false);
const fileInt = ref();
const refLeftbox = ref();
const op_hint = ref('')
const refFiles = ref([])
const config = ref({})
const subject = ref('')
const uploading = ref(false)
const subHint = ref('')
const emit = defineEmits(['begin_upload'])
const uploadUrlInfo = ref({
    fileId: "",
    presignedUrl: ""
})

// 上传状态管理
const uploadStatus = ref(null)

// 计算属性：是否可以确认
const canConfirm = computed(() => {
    return refFiles.value.length > 0 && uploadUrlInfo.value.fileId && !uploading.value
})

// 计算属性：按钮文本
const buttonText = computed(() => {
    if (uploading.value) {
        return '上传中...'
    }
    if (uploadUrlInfo.value.fileId) {
        return '确认'
    }
    return '确认'
})

// 更新上传状态
const updateUploadStatus = (status) => {
    uploadStatus.value = status
}

const deleteFile = (e) => {
    e.stopPropagation()
    refFiles.value = []
    uploadStatus.value = null;
    uploadUrlInfo.value = {
        fileId: "",
        presignedUrl: ""
    };
}

const reChoose = (e) => {
    e.stopPropagation()
    refFiles.value = []
    fileInt.value.click()
}

const onLeft = (e) => {
    if (refFiles.value.length > 0) {
        e.stopPropagation()
        return
    }
    fileInt.value.click()
}

const addDragListener = () => {
    var dropzone = refLeftbox.value;
    let i = 0;
    let timer = null
    dropzone.addEventListener('dragover', function (e) {
        i = 0;
        op_hint.value = '松开上传';
        e.preventDefault(); // 阻止默认行为
    });
    dropzone.addEventListener('dragleave', function (e) {
        i += 1;
        timer && clearTimeout(timer)
        timer = setTimeout(() => {
            if (i > 0) {
                op_hint.value = '点击 、拖拽 本地音视频文件到这里';
            }
        })
        e.preventDefault(); // 阻止默认行为
    });

    dropzone.addEventListener('drop', function (e) {
        i = 0;
        op_hint.value = '点击 、拖拽 本地音视频文件到这里';
        e.preventDefault(); // 阻止默认行为
        // 获取拖拽的文件
        _changeHandle(e.dataTransfer.files);
    });
}
const changeHandle = () => {
    _changeHandle(fileInt.value.files)
}

const _changeHandle = async (files) => {
    if (files.length == 0) {
        return
    }
    refFiles.value = []
    subject.value = ''
    uploadStatus.value = null
    const file = files[0];
    const { name, size, type } = file
    if (!type) {
        ElMessage.error(`${name}文件类型不支持，已忽略`)
        return
    }
    if (size > config.value.maxSizeMb * 1048576) {
        ElMessage.error(`文件${name}大于${config.value.maxSizeMb}MB，已忽略`)
        return
    }
    const ext = name.split('.').pop();
    if (!config.value.fileTypes.includes(ext)) {
        ElMessage.error(`${name}文件类型不支持，已忽略`)
        return
    }

    // 先添加到文件列表，显示文件信息
    refFiles.value.push(file)
    const fileName = name.replace(/\.[^/.]+$/, '');
    g.clientFileStore.setFileName(fileName)
    g.emitter.emit('file_setName', fileName)
    fileInt.value.value = null;

    // 立即开始上传
    uploading.value = true;

    // 更新上传状态 - 开始创建文件
    updateUploadStatus({
        text: '正在创建文件...',
        showProgress: false,
        percentage: 0,
        status: ''
    })

    try {
        // 1. 调用createFile方法，module参数传record_upload
        const param = {
            "module": "record_upload",
            "fileNameExt": name.split('.').pop(),
            "isPrivate": 1,
            "bizId": ""
        }

        const res = await createFile(param)
        console.log('createFile res', JSON.stringify(res))

        if (res.code !== 0) {
            throw new Error(res.message || '创建文件失败')
        }

        uploadUrlInfo.value = res.data

        // 更新上传状态 - 开始上传文件
        updateUploadStatus({
            text: '正在上传文件...',
            showProgress: true,
            percentage: 0,
            status: ''
        })

        // 2. 向presignedUrl上传文件
        await uploadToPresignedUrl(file, uploadUrlInfo.value.presignedUrl)

        // 更新上传状态 - 上传完成
        updateUploadStatus({
            text: '上传成功',
            showProgress: true,
            percentage: 100,
            status: 'success'
        })

        // 3. 上传成功，fileId就是表单字段里需要的值
        ElMessage.success('文件上传成功')

        // 触发成功事件，传递fileId
        g.emitter.emit('upload_success', {
            fileId: uploadUrlInfo.value.fileId,
            fileName: name,
            fileSize: formatFileSize(size)
        })

    } catch (error) {
        console.error('Upload failed:', error)
        ElMessage.error(error.message || '文件上传失败')

        // 更新上传状态 - 上传失败
        updateUploadStatus({
            text: '上传失败',
            showProgress: true,
            percentage: 0,
            status: 'exception'
        })

        // 上传失败时清空文件列表
        refFiles.value = []
    } finally {
        uploading.value = false;
    }
}

const rechoose = () => {
    refFiles.value = [];
    subject.value = '';
    op_hint.value = '';
    uploadStatus.value = null;
    uploadUrlInfo.value = {
        fileId: "",
        presignedUrl: ""
    };
}

const onCancel = () => {
    isShow.value = false;
}

// 直接上传文件到presignedUrl
const uploadToPresignedUrl = async (file, presignedUrl) => {
    try {
        const response = await fetch(presignedUrl, {
            method: 'PUT',
            body: file,
            headers: {
                'Content-Type': 'application/octet-stream'
            }
        })

        if (!response.ok) {
            throw new Error('上传失败')
        }

        return { success: true }
    } catch (error) {
        console.error('Upload error:', error)
        throw error
    }
}

const onOk = async () => {
    const { checkParam } = config.value;

    // 检查是否有上传的文件
    if (refFiles.value.length == 0) {
        ElMessage.error('请先选择文件！')
        return
    }

    // 检查是否有fileId（表示上传成功）
    if (!uploadUrlInfo.value.fileId) {
        ElMessage.error('文件上传中，请稍候...')
        return
    }

    // 检查表单参数
    if (!checkParam()) {
        return
    }
    emit('begin_upload', uploadUrlInfo.value.fileId)
}

const handleClose = () => {
    isShow.value = false;
}

const show = (_config) => {
    config.value = _config;
    subHint.value = `支持${_config.fileTypes.join(',')}格式，单个文件不大于${_config.maxSizeMb}Mb`
    rechoose();
    isShow.value = true;
    nextTick(() => {
        addDragListener()
    })
}

defineExpose({
    fileInt, refLeftbox, op_hint, refFiles, config, handleClose,
    subject, isShow, onOk, show, reChoose, deleteFile
})

</script>

<style lang="scss">
.upload_diglog {
    .el-dialog__header {
        border-bottom: 1px solid #E9E9E9;
        margin-right: 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #E9E9E9;
    }

    .el-dialog__body {
        display: flex;
        flex-direction: row;

        .left {
            display: flex;
            flex-direction: column;
            width: 400px;
            height: 225px;
            background: #FAFAFA;
            border-radius: 4px;
            border: 1px solid #D9D9D9;
            align-items: center;
            justify-content: center;

            .note {
                font-weight: 500;
                color: #262626;
                display: flex;
                flex-direction: row;
                margin-top: 10px;

                .upload_txt {
                    color: var(--el-color-primary);
                }
            }

            .subnote {
                margin: 10px 0;
            }

            .f_btn {
                margin-top: 10px;

                .f_btn_item {
                    width: 48px;
                    font-size: 12px;
                    color: #436BFF;
                    line-height: 20px;
                    text-align: center;
                    cursor: pointer;
                }
            }

            .upload-progress {
                margin-top: 15px;
                width: 100%;
                padding: 0 20px;

                .upload-status {
                    font-size: 12px;
                    color: #666;
                    margin-bottom: 8px;
                    text-align: center;
                }

                :deep(.el-progress) {
                    .el-progress__text {
                        font-size: 12px;
                    }
                }
            }
        }

        .has_file {}

        .no_file {
            cursor: pointer;
        }

        .ud_right {
            width: 440px;
            padding: 0 8px;
            overflow-y: auto;
            margin-left: 12px;
            max-height: 500px;
            overflow-y: auto;
        }

        .fileInput {
            opacity: 0;
            position: absolute;
            top: 80px;
        }
    }
}
</style>

<template>
    <div class="av_item_value tag-picker">
        <el-select placeholder="请选择" v-model="localParam" @change="handleChange">
            <el-option v-for="item in items" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
    </div>
</template>

<script setup>
import { getCommunicationFlowStages } from '@/js/api'

const items = ref([])

const props = defineProps({
    modelValue: {
        type: String,
        required: true,
        default: ""
    },
    type: {
        type: String,
        required: true,
        default: 'customer'
    },
    queryId: {
        type: String,
        required: false,
        default: ""
    }
})
const localParam = ref(props.modelValue)

const emit = defineEmits(['update:modelValue', 'change'])

const handleChange = (val) => {
    localParam.value = val
    updateModelValue()
}


const updateModelValue = () => {
    emit('update:modelValue', localParam.value)
    const item = items.value.find(x => x.id == localParam.value)
    emit('change', item)
}

const init = async (modelValue) => {
    localParam.value = modelValue
    if (!props.queryId) return
    const resp = await getCommunicationFlowStages(props.type, props.queryId)
    if (resp.code == 0) {
        items.value = resp.data.datas
    }
    // updateModelValue()
}

watch(() => [props.type, props.queryId], async () => {
    if (!props.queryId) return
    init()
})


defineExpose({
    localParam, init
})
</script>

<style lang="scss" scoped>
.tag-picker {
    width: 100%;
    align-items: flex-start;

    .av_item_value_goods {
        margin-top: 10px;
        font-size: 12px;
        color: #436BFF;
        cursor: pointer;
    }

    .av_item_value_tags {
        margin-top: 10px;

        .el-tag {
            margin: 0 5px 5px 0;
            max-width: 250px;

            :deep(.el-tag__content) {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: calc(100% - 20px); // 预留关闭按钮的空间
            }
        }
    }
}
</style>

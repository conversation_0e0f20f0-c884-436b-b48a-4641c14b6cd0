<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="template-table-drawer-analysis">
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">{{ drawerTitle }}</span>
      </div>
    </template>

    <div class="drawer-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" require-asterisk-position="right">
        <div style="margin-bottom: 24px;">
          <el-form-item label="模版名称" prop="name">
            <el-input v-model="form.name" show-word-limit maxlength="50" />
          </el-form-item>
        </div>

        <div class="form-section">
          <el-form-item label="关联维度" :rules="behaviorRules">
            <div v-sortable @end.prevent="handleDragEnd" class="behavior-list">
              <div v-for="(row, index) in itemsArrList" :key="index" class="behavior-item">
                <div class="drag-handle">
                  <img style="width: 16px; height: 16px;" :src="getOssUrl('icon-sort.png', 3)" alt="">
                  <p>{{ row.label }}</p>
                  <el-icon>
                    <Close @click="removeBehavior(index)" />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>
        <LxMultiButtonSelect v-model="form.itemsArr" :options="dimensions">
          <template #label="{ toggleDropdown }">
            <div class="add-behavior-section">
              <el-button class="add-behavior-btn" text @click="toggleDropdown">
                <el-icon>
                  <Plus />
                </el-icon>
                <span>添加行为表现</span>
              </el-button>
            </div>
          </template>
        </LxMultiButtonSelect>

      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close, Menu } from '@element-plus/icons-vue'
import LxMultiButtonSelect from '@/components/LxMultiButtonSelect/index.js'
import { communicateApi } from "@/app_admin/api";
import { createModel, updateModel } from "@/app_admin/api/communicate.js";
import { getOssUrl } from "@/js/utils"
const props = defineProps({
  dimensions: {
    type: Array,
    default: () => ([])
  }
})
const emit = defineEmits(['success', 'close'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

const drawerTitle = computed(() => {
  return isEditMode.value ? '编辑模板' : '添加模板'
})

const itemsArrList = computed(() => {
  return [...form.itemsArr].map(v => props.dimensions.find(item => item.value == v))
})


const form = reactive({
  name: '',
  items: [],
  itemsArr: []
})

const rules = {
  name: [
    { required: true, message: '请输入能力名称', trigger: 'blur' }
  ]
}

const behaviorRules = [
  { required: true, message: '', trigger: 'blur' },
]

const show_add = async (data = null) => {
  drawerVisible.value = true
  form.itemsArr = []
  form.items = []
  form.name = ''
}

const show_edit = async (data) => {
  drawerVisible.value = true

  itemsArrList.value = []
  form.itemsArr = []
  form.items = []

  // 编辑模式
  if (data) {
    isEditMode.value = true
    currentId.value = data.id
    form.name = data.name
    form.items = [...data.items]
    form.itemsArr = data.items && data.items.length > 0 ? data.items.map(item => item.dimensionId) : []
  }
}

const closeDrawer = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const removeBehavior = (index) => {
  console.log(form.itemsArr)
  if (form.itemsArr.length <= 1) {
    ElMessage.warning({ message: '至少保留一个关联维度', grouping: true })
    return
  }
  form.itemsArr.splice(index, 1)
}


const handleDragEnd = (evt) => {
  // 交换数组元素
  const movedItem = itemsArrList.value.splice(evt.oldIndex, 1)[0]
  itemsArrList.value.splice(evt.newIndex, 0, movedItem)
}

const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      if (form.itemsArr.length === 0) {
        ElMessage.error('请选择关联维度')
        return false
      }
      // 处理表单提交
      if (isEditMode.value) {
        const formData = {
          id: currentId.value,
          name: form.name,
          type: 'ANALYSIS',
          items: itemsArrList.value.map(item => ({
            dimensionId: item.value,
            id: form.items.find(i => i.dimensionId == item.value)?.id || ''
          }))
        }
        updateModel(formData).then((resp) => {
          if (resp.code == 0) {
            ElMessage.success("编辑成功")
            emit('success')
          }
        })
      } else {
        const formData = {
          name: form.name,
          type: 'ANALYSIS',
          items: itemsArrList.value.map(item => ({
            dimensionId: item.value,
          }))
        }
        createModel(formData).then((resp) => {
          if (resp.code == 0) {
            emit('success')
            ElMessage.success("添加成功")

          }
        })
      }
      closeDrawer()
    } else {
      ElMessage.error('请检查输入内容')
      return false
    }
  })
}

const handleClose = (done) => {
  closeDrawer()
  done()
  emit('close')
}

// 暴露方法给父组件
defineExpose({
  show_edit, show_add, closeDrawer
})
</script>

<style scoped lang="scss">
p {
  margin: 0;
  padding: 0;
}


.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  .close-icon {
    font-size: 16px;
    color: #999;
    cursor: pointer;
    margin-right: 16px;

    &:hover {
      color: #666;
    }
  }

  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.behavior-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

.behavior-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  width: 100%;



  .drag-handle {
    cursor: move;
    padding: 4px 12px;
    display: flex;
    align-items: center;
    color: #909399;
    background: #F9FAFC;
    border-radius: 8px 8px 8px 8px;
    width: 100%;
    gap: 12px;
    margin-bottom: 4px;

    >p {
      display: flex;
      flex: 1;
    }
  }


  .char-count {
    position: absolute;
    right: 12px;
    bottom: 12px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }

}

.add-behavior-btn {
  width: 100%;
  margin-top: 12px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8px 8px 8px 8px;
  border: 1px dashed #436BFF;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #436BFF;
  line-height: 22px;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 5px 16px;
    border-radius: 6px;
  }

  .cancel-btn {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
  }

  .confirm-btn {
    background: #436BFF;
    border-color: #436BFF;
  }
}
</style>
<style lang="scss">
.template-table-drawer-analysis {
  .el-form-item {
    margin-bottom: 0;
  }
}
</style>
<template>
  <Modal ref="refModal" @callback="cbModal" destroy-on-close class="sale_dis_dim_wrap">
    <div v-if="dimensionType == 'SUMMARY'">
      <div class="dim_title">维度选择</div>
      <el-radio-group v-model="formData.systemPreset" class="ml-4 wd_radio" @change="onTypeChange">
        <el-radio :value="false" size="default">自定义维度</el-radio>
        <el-radio :value="true" size="default">从模板库选择</el-radio>
      </el-radio-group>
    </div>
    <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default" :rules="rules"
      v-show="!formData.systemPreset">
      <el-form-item label="分析维度" prop="name">
        <el-input v-model.trim="formData.name" maxlength="50" show-word-limit placeholder="请输入" />
      </el-form-item>
      <el-form-item label="提示词Prompt" prop="content">
        <el-tooltip>
          <template #content>
            您可以自定义提示词，系统将基于以下沟通信息进行分析：<br />
            沟通文字记录、参会人员信息、沟通主题、客户名称、沟通时间、沟通目标等
          </template>
          <div class="promot_question_icon">
            <QuestionIcon />
          </div>
        </el-tooltip>
        <div class="right_top_btn flex-row" @click="onOpenAi" v-if="true">
          <img :src="getOssUrl('yxt_ai_color.png')" alt="summary" />
          <div class="ai_btn">
            <span style="color: #733cfe">A</span>
            <span style="color: #6c5afd">I</span>
            <span style="color: #6578fc">辅</span>
            <span style="color: #5e96fb">助</span>
            <span style="color: #57b4fa">生</span>
            <span style="color: #50d2f9">成</span>
          </div>
        </div>
        <el-input type="textarea" v-model="formData.content" maxlength="5000" :rows="13" show-word-limit
          placeholder="基于沟通过程记录，使用prompt引导AI生成对应内容" />
      </el-form-item>
    </el-form>
    <dimTemplate ref="refTemplate" @callback="cbTemplate" v-show="formData.systemPreset" />
  </Modal>
  <DrawerAiPrompt ref="refDrawerAiPrompt" />
</template>

<script setup>
import { nextTick, reactive, ref, toRaw } from "vue";
import {
  createXmTopicDimension,
  updateTopicDimension,
} from "@/app_admin/tools/api.js";
import Modal from "@/components/Modal.vue";
import dimTemplate from "./dimTemplate.vue";
import QuestionIcon from "@/app_admin/icons/question.vue";
import DrawerAiPrompt from "./DrawerAiPrompt.vue";
import { getOssUrl } from "@/js/utils";
const emit = defineEmits(["callback"]);

const refModal = ref();
const title = ref("");
const refTemplate = ref();
const refForm = ref("");
const currId = ref("");
const dimensionType = ref("");
const refDrawerAiPrompt = ref();
const defaultForm = {
  systemPreset: false, //是否系统内置维度
  systemId: 0, //系统内置维度ID， 如果不是系统内置维度，固定为0
  content: "", //提示词内容
  name: "", //系统内置维度名称
};

const formData = ref({ ...defaultForm });

const cfg = {
  width: "700px",
};

const onOpenAi = () => {
  refDrawerAiPrompt.value.show();
};

const onTypeChange = (value) => {
  formData.value["systemPreset"] = value;
  formData.value["systemId"] = 0; //value ? 1 : 0;
  if (value) {
    nextTick(() => {
      refTemplate.value.setChoosed(formData.value["systemId"]);
    });
  }
};

const _resetForm = () => {
  formData.value = { ...defaultForm };
};

const show_add = (pid) => {
  currId.value = pid;
  dimensionType.value = g.saleStore.dimensionType;
  formData.value = { ...defaultForm };
  cfg["title"] = "添加维度";
  refModal.value.show(cfg);
  nextTick(() => {
    refForm.value.resetFields();
  });
};

const show_edit = (pid, data) => {
  currId.value = pid;
  dimensionType.value = g.saleStore.dimensionType;
  formData.value = { ...data };
  cfg["title"] = "编辑维度";
  refModal.value.show(cfg);
  if (formData.value.systemPreset) {
    nextTick(() => {
      refTemplate.value.setChoosed(formData.value.systemId);
    });
  }
};

const cbModal = (action) => {
  if (action == "confirm") {
    btnOK();
  } else if (action == "cancel") {
    btnCancel();
  }
};

const cbTemplate = (item) => {
  if (!item) {
    return;
  }
  formData.value.systemId = item.id;
  formData.value.name = item.name;
  formData.value.content = "";
};

const btnCancel = () => {
  _resetForm();
  refModal.value.hide();
};

const btnOK = () => {
  if (!refForm.value) return;

  const { systemPreset } = formData.value;
  const fn = () => {
    const data = toRaw(formData.value);
    if (data.systemId === '4') {
      delete data['defaultSalesMethodology']
    }
    if (!data.id) {
      const { dimensionType } = g.saleStore;
      createXmTopicDimension(currId.value, dimensionType, data)
        .then((resp) => {
          if (resp.code == 0) {
            ElMessage.success(`${cfg["title"]}成功`);
            emit("callback", "reload");
            btnCancel();
          } else {
            ElMessage.error(`${resp.message}`);
          }
        })
        .catch((e) => {
          ElMessage.error(`${cfg["title"]}失败`);
        });
    } else {
      updateTopicDimension(currId.value, data.id, data)
        .then((resp) => {
          if (resp.code == 0) {
            ElMessage.success(`${cfg["title"]}成功`);
            emit("callback", "reload");
            btnCancel();
          } else {
            ElMessage.error(`${resp.message}`);
          }
        })
        .catch((e) => {
          ElMessage.error(`${cfg["title"]}失败`);
        });
    }
  };
  if (systemPreset) {
    fn();
  } else {
    refForm.value.validate((valid, fields) => {
      if (valid) {
        fn();
      } else {
        console.log("not valid", fields);
      }
    });
  }
};

const rules = reactive({
  name: [{ required: true, message: "请输入分析维度", trigger: "blur" }],
  content: [{ required: true, message: "请输入提示词Prompt", trigger: "blur" }],
});

onMounted(() => {
  g.emitter.on('update_defaultSalesMethodology', (value) => {
    formData.value.defaultSalesMethodology = value;
  })
})

onUnmounted(() => {
  g.emitter.off("update_defaultSalesMethodology");
});

defineExpose({
  title,
  dimTemplate,
  onTypeChange,
  cbTemplate,
  refTemplate,
  show_add,
  show_edit,
  cbModal,
  formData,
  rules,
  dimensionType,
  QuestionIcon,
  DrawerAiPrompt,
});
</script>

<style lang="scss">
.sale_dis_dim_wrap {
  .dim_title {
    margin: 4px 0;
  }

  .wd_radio {
    margin-bottom: 8px;
  }

  .el-dialog__body {
    padding: 15px 24px 5px 24px;
    max-height: 500px;
    overflow-y: auto;
    overflow-x: hidden;

    .promot_question_icon {
      position: absolute;
      top: -31px;
      left: 107px;
      cursor: pointer;
    }

    .right_top_btn {
      position: absolute;
      top: -28px;
      right: -5px;
      z-index: 3;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
        margin: 0 6px;
      }

      .ai_btn {
        width: 75px;
        height: 22px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        font-size: 14px;
        color: #595959;
        line-height: 22px;
        text-align: left;
        font-style: normal;
      }
    }
  }
}
</style>

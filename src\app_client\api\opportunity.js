import { getHttp } from "@/js/request.js";
const _http = getHttp();

// 获取商机列表
export const getOpportunityList = (params) => {
  // return _http.post(`api/opportunity/myopportunity/list`, params);
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 0,
        data: {
          datas: [
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-08-15 16:52:38",
              enableBusinessRule: false,
              followDeptNames: "wumx-xmate",
              followNames: "wumx_xmate03,wumx_xmate02",
              formDataId: "a157caac-a878-4233-90a5-4f35abf7849c",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  fieldValue: "1",
                  id: "1fcbc479-9753-4894-ba54-b20c0bef42aa",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1956277840974573568",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "李先生",
              riskCount: 0,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-07-02 14:35:02",
              enableBusinessRule: false,
              followDeptNames: "wumx-xmate",
              followNames: "wumx_xmate02",
              formDataId: "23a9d51c-b37e-447d-a93d-864f02eca13d",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "aa6bef6e-79e1-490e-bd56-081f0b93bef5",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1940298148194934784",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "1",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 3,
              competitorCount: 0,
              createdTime: "2025-06-13 16:13:20",
              enableBusinessRule: false,
              followDeptNames:
                "研发部,wumx-xmate,0307_河北,wumx的高级部门,Xmate-测试11",
              followNames:
                "wumx_xmate03,wumx_xmate02,admin王伟,wumx_xmate01,wumx0307_冀_01,wumx0307_冀_02,wumx0307_01,wumx01,wumx02",
              formDataId: "baa7f477-ca60-41be-893e-c5d7e297ee83",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  fieldValue: "",
                  id: "c563d619-35ca-4ca9-9ee8-a216eafb7f6f",
                  order: 1,
                },
              ],
              hostDeptNames: "Xmate-测试-2级部门",
              hostNames: "时东东",
              id: "1933437517076414464",
              latestMeetTime: "2025-08-04 10:19:47",
              meddicList: [
                {
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "OPPORTUNITY_SCORE",
                },
                {
                  content: "",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "DECISION_PROCESS",
                },
                {
                  content: "",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "DECISION_CRITERIA",
                },
                {
                  content:
                    "1. **技术问题**：客户提到钉钉浏览器不支持某些功能（如防切屏、上传作业等），导致用户体验不佳。\n2. **登录流程复杂**：客户对当前登录流程（需输入账号密码或扫码）表示不满，希望优化为无感登录。\n3. **数据同步问题**：客户提到新员工账号同步和临时工账号管理存在技术难题，影响培训记录的有效性。\n4. **权限管理问题**：客户提到管理员权限设置不清晰，可能导致数据误删或泄露。\n5. **交付物确认问题**：客户对交付物的确认流程表示不满，认为需要更清晰的沟通和确认机制。",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "FULL",
                  meddicType: "IDENTIFY_PAIN",
                },
                {
                  content: "",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "ECONOMIC_BUYER",
                },
                {
                  content: "",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "METRICS",
                },
                {
                  content: "",
                  contentLocked: 0,
                  customerId: 1933437517076414500,
                  meddicStatus: "NOT_FILLED",
                  meddicType: "CHAMPION",
                },
              ],
              meetCount: 2,
              meets: [],
              name: "新疆中泰（集团）有限责任公司",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 18:54:42",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "178710a0-74e0-428e-a30a-acfb25b9f38f",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "c081cf0d-44ab-48f6-a514-b2aaa6e16762",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914633957580795904",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "哇哈哈",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 18:52:53",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "bff73311-9999-4d94-98e3-1664761fb139",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "2a8d5033-3b2a-4e8c-ac81-889eb60975e3",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914633499546021888",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "哇哈哈",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 18:13:16",
              enableBusinessRule: false,
              followDeptNames: "wumx-xmate",
              followNames: "wumx_xmate01",
              formDataId: "4dd13134-9948-4968-8e00-4d2e662cd77b",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "e482e072-1617-487a-baed-e14b0c392a1d",
                  order: 1,
                },
              ],
              hostDeptNames: "研发部",
              hostNames: "admin王伟",
              id: "1914623530423574528",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "蜜雪冰城7",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 18:03:33",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "65c0b821-3918-4e4a-9fff-d53430a5640a",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "7885e8fe-0957-4b22-90e7-6c92e72d24cf",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914621082145415168",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "可口可乐",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 18:03:01",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "3ab7dbc4-424e-48fb-a8ae-3bb43d4cb850",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "fca06a95-507e-4f9e-ad75-16eefec90e30",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914620951551565824",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "蜜雪冰城7",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 15:31:16",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "7b547975-5692-4ab9-bad4-145d982732e3",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "47a54c84-5fd6-45fd-afb8-973cde9d1579",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914582761599930368",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "蜜雪冰城4",
              riskCount: 1,
              todosCount: 0,
            },
            {
              attendeeCount: 0,
              competitorCount: 0,
              createdTime: "2025-04-22 15:29:55",
              enableBusinessRule: false,
              followDeptNames: "",
              followNames: "",
              formDataId: "be0b6dc1-c16e-46d4-b024-d8f60c2f5259",
              formFieldValues: [
                {
                  fieldId: "ad67d473-9fdf-4e71-b0c6-7d4549d00b68",
                  fieldName: "区号",
                  id: "86a7b6db-a2bd-4984-9d43-137db114140c",
                  order: 1,
                },
              ],
              hostDeptNames: "wumx-xmate",
              hostNames: "wumx_xmate01",
              id: "1914582418895933440",
              meddicList: [],
              meetCount: 0,
              meets: [],
              name: "蜜雪冰城4",
              riskCount: 1,
              todosCount: 0,
            },
          ],
          totalNum: 23,
        },
      });
    }, 300);
  });
};

// 获取商机状态列表
export const getOpportunityStatusList = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        code: 0,
        data: [
          { value: "进行中", label: "进行中" },
          { value: "赢单", label: "赢单" },
          { value: "输单", label: "输单" },
          { value: "其他", label: "其他" },
        ],
      });
    }, 300);
  });
};

// 获取商机分类数据
export const getOpportunityCategoryData = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      // 模拟数据 - 可以修改这里来测试不同情况
      // 情况1：多条数据（正常显示）
      const data = [
        {
          key: "new",
          label: "新客软件商机",
          count: 12,
        },
        {
          key: "existing",
          label: "新客软件商机",
          count: 6,
        },
      ];

      // 情况2：只有一条数据（不显示tabs）
      // const data = [
      //     {
      //         key: 'new',
      //         label: '新客软件商机',
      //         count: 12
      //     }
      // ];

      resolve({
        code: 0,
        data: data,
      });
    }, 200);
  });
};

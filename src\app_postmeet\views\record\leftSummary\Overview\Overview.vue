<template>
  <div class="overview">
    <div class="overview-content" :class="{ 'is-loading': loading }" v-loading="loading"
      element-loading-text="AI分析中，请稍等...">
      <MdViewer :md="mdBant" v-if="saleAnalyseType == 'BANT' && !!mdBant" :edit="isEdit" />
      <MdViewer :md="mdMeddic" v-else-if="saleAnalyseType == 'MEDDIC' && !!mdMeddic" :edit="isEdit" />
      <MdViewer :md="mdSpiced" v-else-if="saleAnalyseType == 'SPICED' && !!mdSpiced" :edit="isEdit" />
      <MdViewer :md="mdNeat" v-else-if="saleAnalyseType == 'NEAT' && !!mdNeat" :edit="isEdit" />
      <div class="no_data" v-else-if="!loading && !mdNormal && !mdBant && !mdMeddic && !mdSpiced && !mdNeat">
        暂无数据
      </div>
    </div>
  </div>
</template>
<script>
import MdViewer from "@/components/Markdown/MdViewer.vue"
import { getConferenceReportSummary } from '@/app_postmeet/tools/api';

export default {
  components: { MdViewer },
  name: 'Overview',
  data() {
    return {
      loading: false,
      // 1 BANT 2 MEDDIC 3 SPICED 4 NEAT
      saleAnalyseType: '',
      mdNormal: '',
      mdBant: '',
      mdMeddic: '',
      mdSpiced: '',
      mdNeat: '',
      isEdit: true
    }
  },
  mounted() {
    g.emitter.on('onChangeSaleAnalyseType', (type) => {
      this.saleAnalyseType = type;
      this.getAnalyseData();
    })
    g.emitter.on('updateSummary', () => {
      this.getAnalyseData();
    })
  },
  methods: {
    init() {
      // SalesMethodology赋值之后 saleAnalyseType 不读取默认防止被覆盖成初始值
      if (g.postmeetStore.data.SalesMethodology.methodology) return

      const content = g.postmeetStore.data.saleReport.salesSummaryReports.filter(x => x.systemId == 8)[0].report;
      this.saleAnalyseType = g.postmeetStore.data.defaultSalesMethodology;
      this._setValue(this.saleAnalyseType, content);
    },
    _setValue(methodology, content) {
      g.postmeetStore.setValue('summaryMdId', '')
      if (methodology == 'BANT') {
        this.mdBant = content;
      } else if (methodology == 'MEDDIC') {
        this.mdMeddic = content;
      } else if (methodology == 'SPICED') {
        this.mdSpiced = content;
      } else if (methodology == 'NEAT') {
        this.mdNeat = content;
      }
    },
    getAnalyseData() {
      // 暂时关闭 切换tab缓存
      // const cacheKey = this.saleAnalyseType;
      // if (g.postmeetStore.data.methodologyCache[cacheKey]) {
      //   this._setValue(this.saleAnalyseType, g.postmeetStore.data.methodologyCache[cacheKey]);
      //   return;
      // }
      this.loading = true;
      getConferenceReportSummary(g.postmeetStore.data.confId, this.saleAnalyseType).then((resp) => {
        if (resp.code == 0) {
          const { methodology, content } = resp.data;
          g.postmeetStore.setValue('SalesMethodology', {
            methodology,
            content
          });
          g.postmeetStore.data.methodologyCache[methodology] = content;
          this._setValue(methodology, content);
          this.loading = false;
        }
      }).catch(() => {
        this.loading = false;
      });
    },
  }
}
</script>

<style lang='scss'>
@use './style.scss';
</style>

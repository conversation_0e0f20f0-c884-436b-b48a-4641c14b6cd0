<template>
  <div class="radar_chart_wrap">
    <div class="score-legend" v-if="showLegend">
      <div :class="['score-list', { 'down-icon': legendExpand }]" :style="{ width: scoreListWidth }">
        <div class="legend-item" v-for="item in legendList" :key="item.full">
          <span class="dot" :style="{ backgroundColor: deptColorMap[item.full] }"></span>
          <el-tooltip effect="light" :content="item.full" placement="top" popper-class="legend-text-tooltip">
            <span class="legend-text">{{ item.last }}</span>
            <template #content>

              <span class="legend-text-tooltip-dot" :style="{ backgroundColor: deptColorMap[item.full] }"></span>
              <span>{{ item.full
              }}</span>

            </template>
          </el-tooltip>
        </div>
      </div>
      <el-button @click="toggleLegend" class="legend-button" v-if="showLegendButton">
        <el-icon v-if="legendExpand">
          <ArrowUp />
        </el-icon>
        <el-icon v-else>
          <ArrowDown />
        </el-icon>
      </el-button>
    </div>
    <div ref="refChart" class="Chart"></div>
    <p class="other-info" v-if="unqualifiedData.length">

      <el-icon class="other-info-item-icon">
        <Warning />
      </el-icon>
      <span class="other-info-item-text">警示：低分人员共 {{ unqualifiedData.length }} 人</span>
      <span class="other-info-item-click" @click="showOtherInfo">
        查看详情
      </span>
    </p>
  </div>
</template>

<script setup>
import {
  ArrowUp, ArrowDown, Warning
} from '@element-plus/icons-vue'
import { ref, nextTick, computed } from 'vue'
import echarts from "@/js/echarts"
import { colors21 } from "@/app_client/tools/const_value.js"
const emit = defineEmits(['showOtherInfo'])
const hasData = ref(false)
const refChart = ref(null)
const deptColorMap = ref({})

const props = defineProps({
  showLegend: {
    type: Boolean,
    default: true
  }
})

const minAbility = ref(0)
const minTask = ref(0)
const maxAbility = ref(0)
const maxTask = ref(0)
const unqualifiedData = ref([])
const qualifiedData = ref([])

// 图例相关
const legendExpand = ref(false)
const legendList = computed(() => {
  return Object.keys(deptColorMap.value).map(full => ({
    full,
    last: full.split('->').pop()
  }))
})

// 控制按钮显示和列表宽度
const showLegendButton = ref(false)
const scoreListWidth = ref('')

const toggleLegend = () => {
  legendExpand.value = !legendExpand.value
}

// 检查是否需要显示按钮
const checkLegendOverflow = () => {
  nextTick(() => {
    const scoreLegend = document.querySelector('.score-legend')
    const scoreList = document.querySelector('.score-list')
    const legendButton = document.querySelector('.legend-button')

    if (scoreLegend && scoreList) {
      // 临时移除按钮来计算列表实际需要的宽度
      if (legendButton) {
        legendButton.style.display = 'none'
      }

      // // 重置列表样式以获取真实宽度
      // scoreList.style.width = 'auto'
      // scoreList.style.flex = '1'

      const listWidth = scoreList.scrollWidth
      const containerWidth = scoreLegend.clientWidth
      const buttonWidth = 32 // 按钮宽度
      const gap = 20 // gap值

      // 恢复按钮显示
      if (legendButton) {
        legendButton.style.display = ''
      }

      // 判断是否需要显示按钮
      if (listWidth > (containerWidth - buttonWidth - gap)) {
        showLegendButton.value = true
        scoreListWidth.value = `calc(100% - ${buttonWidth + gap}px)`
      } else {
        showLegendButton.value = false
        scoreListWidth.value = '100%'
      }
    }
  })
}

const clear = () => {
  // 清除图表的方法
}

const init = async (data_, Obj) => {
  deptColorMap.value = {}
  hasData.value = true

  // 计算能力评估和任务达成的最小值，向下取整到10的倍数
  const abilityScores = data_.map(item => item.abilityScore)
  const taskCompleteRatios = data_.map(item => item.taskCompleteRatio)

  const minAbilityScore = Math.floor(Math.min(...abilityScores, Obj.abilityScore4Pass) / 10) * 10
  const minTaskCompleteRatio = Math.floor(Math.min(...taskCompleteRatios, Obj.taskRate4Pass) / 10) * 10

  const maxAbilityScore = Math.ceil(Math.max(...abilityScores, Obj.abilityScore4Pass) / 10) * 10
  const maxTaskCompleteRatio = Math.ceil(Math.max(...taskCompleteRatios, Obj.taskRate4Pass) / 10) * 10

  // 计算能力评估轴的最大差值
  const abilityScore4Pass = Obj.abilityScore4Pass || 0
  const abilityDiff1 = Math.abs(maxAbilityScore - abilityScore4Pass)
  const abilityDiff2 = Math.abs(minAbilityScore - abilityScore4Pass)
  const maxAbilityDiff = Math.max(abilityDiff1, abilityDiff2)

  // 计算任务达成轴的最大差值
  const taskRate4Pass = Obj.taskRate4Pass || 0
  const taskDiff1 = Math.abs(maxTaskCompleteRatio - taskRate4Pass)
  const taskDiff2 = Math.abs(minTaskCompleteRatio - taskRate4Pass)
  const maxTaskDiff = Math.max(taskDiff1, taskDiff2)

  minAbility.value = Math.min(abilityScore4Pass - 20 > 0 ? abilityScore4Pass - 20 : 0, minAbilityScore)
  minTask.value = Math.min(taskRate4Pass - 20 > 0 ? taskRate4Pass - 20 : 0, minTaskCompleteRatio)

  maxAbility.value = Math.max(abilityScore4Pass + 20 < 100 ? abilityScore4Pass + 20 : 100, maxAbilityScore)
  maxTask.value = Math.max(taskRate4Pass + 20 < 100 ? taskRate4Pass + 20 : 100, maxTaskCompleteRatio)

  // 计算坐标轴的实际范围
  const xAxisMin = minAbility.value
  const xAxisMax = maxAbility.value
  const yAxisMin = minTask.value
  const yAxisMax = maxTask.value

  // 计算dataZoom的缩放范围（百分比）
  const xStart = Math.max(0, ((xAxisMin - 0) / (100 - 0)) * 100)
  const xEnd = Math.min(100, ((xAxisMax - 0) / (100 - 0)) * 100)
  const yStart = Math.max(0, ((yAxisMin - 0) / (100 - 0)) * 100)
  const yEnd = Math.min(100, ((yAxisMax - 0) / (100 - 0)) * 100)

  // 过滤数据
  unqualifiedData.value = data_.filter(item =>
    item.abilityScore < minAbility.value || item.taskCompleteRatio < minTask.value
  )
  qualifiedData.value = data_.filter(item =>
    item.abilityScore >= minAbility.value && item.taskCompleteRatio >= minTask.value
  )

  // 过滤重复的部门
  const allDepts = [...new Set(qualifiedData.value.map(item => item.deptName))]
  for (let i = 0; i < allDepts.length; i++) {
    const dept = allDepts[i]
    deptColorMap.value[dept] = colors21[i % colors21.length]
  }

  // 只使用达标的数据生成图表
  const dataChart = qualifiedData.value.map(item => {
    return {
      value: [item.abilityScore ? Number(item.abilityScore).toFixed(2) : 0, item.taskCompleteRatio ? Number(item.taskCompleteRatio).toFixed(2) : 0],
      name: item.name,
      deptName: item.deptName, // 全路径
      itemStyle: {
        color: deptColorMap.value[item.deptName]
      }
    }
  })
  checkLegendOverflow()

  const option = {
    xAxis: {
      type: 'value',
      position: 'center',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: "{value}",

        color: '#262626',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#8C8C8C'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#8C8C8C'
        }
      },
      min: xAxisMin,
      max: xAxisMax,
      interval: 5,
    },
    yAxis: {
      type: 'value',
      position: 'center',
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed'
        }
      },
      axisLabel: {
        formatter: "{value}",
        color: '#262626',
        fontSize: 12
      },
      axisLine: {
        lineStyle: {
          color: '#8C8C8C'
        }
      },
      axisTick: {
        lineStyle: {
          color: '#8C8C8C'
        }
      },
      min: yAxisMin,
      max: yAxisMax,
      interval: 5,
    },
    axisLabel: {
      formatter: "{value}",
      color: '#262626',
      fontSize: 12
    },
    // 添加dataZoom组件实现放大缩小和平移功能
    dataZoom: [
      {
        type: 'slider',
        xAxisIndex: 0,
        filterMode: 'filter',
        start: 0, // 初始显示完整范围
        end: 100,
        zoomLock: true,
        height: 16,
        bottom: 10,
        borderColor: 'transparent',
        backgroundColor: '#E6EEFF',
        fillerColor: '#BDD2FF',
        handleStyle: {
          color: '#6B90FF',
          borderColor: '#6B90FF'
        },
        textStyle: {
          color: '#8A8B8E'
        },
        minSpan: 40,  // 最小跨度为 10%
        maxSpan: 100, // 最大跨度为 50%
        moveHandleSize: 0,
        showDataShadow: false, // 隐藏数据阴影
        showDetail: false // 隐藏指示线
      },
      {
        type: 'slider',
        yAxisIndex: 0,
        filterMode: 'filter',
        start: 0, // 初始显示完整范围
        end: 100, // 初始显示完整范围
        minSpan: 40,  // 最小跨度为 10%
        maxSpan: 100, // 最大跨度为 50%
        zoomLock: true,
        width: 16,
        left: 10, // 改为left，放在左边
        borderColor: 'transparent',
        backgroundColor: '#E6EEFF',
        fillerColor: '#BDD2FF',
        handleStyle: {
          color: '#6B90FF',
          borderColor: '#6B90FF'
        },
        textStyle: {
          color: '#8A8B8E'
        },

        moveHandleSize: 0,
        showDataShadow: false, // 隐藏数据阴影
        showDetail: false // 隐藏指示线
      },
      {
        type: 'inside',
        xAxisIndex: 0,
        filterMode: 'filter',
        start: 0, // 初始显示完整范围
        end: 100  // 初始显示完整范围
      },
      {
        type: 'inside',
        yAxisIndex: 0,
        filterMode: 'filter',
        start: 0, // 初始显示完整范围
        end: 100  // 初始显示完整范围
      }
    ],
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        const data = params.data;
        return `
        <div style="padding: 8px 12px;">
          <div style="font-weight: 500; font-size: 14px; color: #262626;">${data.name || ' '}</div>
          <div style="font-size: 12px; color: #8C8C8C; margin-top: 8px;">${data.deptName}</div>
          <div style="display: flex; align-items: center; gap: 4px; margin-top: 8px;">
          <div style="font-size: 12px; color: #8C8C8C; line-height: 20px; ">能力评估</div>
            <div style="font-size: 12px; color: #262626; line-height: 20px;font-weight: 600; margin-right: 24px;">${data.value[0] ? Number(data.value[0]).toFixed(2) : 0}分</div>
            <div style="font-size: 12px; color: #8C8C8C; line-height: 20px;">任务达成</div>
            <div style="font-size: 12px; color: #262626; line-height: 20px;font-weight: 600;">${data.value[1] ? Number(data.value[1]).toFixed(2) : 0}%</div>
          </div>
        </div>`;
      }
    },
    series: [{
      type: 'scatter',
      data: dataChart,
      label: {
        show: false,
        position: 'bottom',
        formatter: '{b}'
      },
      itemStyle: {
        // shadowBlur: 2,
        // shadowColor: 'rgba(120, 36, 50, 0.5)',
        // shadowOffsetY: 1,
        // color 由每个点的itemStyle.color控制
      },
      markLine: {
        symbol: "none",
        silent: true,
        lineStyle: {
          type: "dashed",
          color: "#04CCA4"
        },
        data: [
          { xAxis: Obj.abilityScore4Pass || 0 },
          { yAxis: Obj.taskRate4Pass || 0 }
        ],
        label: {
          show: true,
          position: "end",
          color: "#04CCA4",
          fontWeight: "bold",
          formatter: function () {
            return "达标值";
          },
        }
      },
      markPoint: {
        symbol: 'roundRect',
        symbolSize: 1,
        itemStyle: {
          color: 'rgba(234, 85, 6, .8)'
        },
        label: {
          position: 'top'
        },
      }
    }],
    grid: {
      left: '72px',
      right: '88px',
      top: '5%',
      bottom: '15%', // 增加底部空间给dataZoom
      backgroundColor: '#f7f7fa'
    },
    graphic: [
      {
        type: 'rect',
        left: '72px',
        top: '5%',
        right: '88px',
        bottom: '15%', // 调整底部位置
        z: 0,
        shape: { width: 'auto', height: 'auto' },
        style: { fill: 'rgba(247, 250, 255, 1)' }
      },
      {
        type: 'text',
        left: '12px',
        top: '1%',
        style: {
          text: '任务达成(%)',
          fontSize: 12,
          fill: '#262626'
        }
      },
      {
        type: 'text',
        left: '78px',
        top: '7%',
        style: {
          text: '工蜂型销售',
          fontSize: 12,
          fill: '#262626'
        }
      },
      {
        type: 'text',
        right: '92px',
        top: '7%',
        style: {
          text: '明星型销售',
          fontSize: 12,
          fill: '#262626'
        }
      },
      {
        type: 'text',
        left: '78px',
        bottom: '17%',
        style: {
          text: '双困型销售',
          fontSize: 12,
          fill: '#262626'
        }
      },
      {
        type: 'text',
        right: '92px',
        bottom: '17%',
        style: {
          text: '孤狼型销售',
          fontSize: 12,
          fill: '#262626'
        }
      },
      {
        type: 'text',
        right: '8px',
        bottom: '11%',
        style: {
          text: '能力评估(分)',
          fontSize: 12,
          fill: '#262626'
        }
      }
    ]
  }

  await nextTick()
  const myChart = echarts.init(refChart.value, "light")
  myChart.setOption(option)
}

const showOtherInfo = () => {
  console.log('showOtherInfo')
  emit('showOtherInfo', unqualifiedData.value)
}

defineExpose({
  init,
  clear,
  unqualifiedData,
  qualifiedData
})
</script>

<style lang="scss" scoped>
.radar_chart_wrap {
  padding-left: 10px;

  .legend-button {
    width: 32px;
    height: 32px;
    padding: 0;
    margin: 0;
    border-radius: 50%;
    background-color: #fff;
    border: 1px solid #e5e5e5;
  }

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .Chart {
    height: 450px;
    width: calc(100vw - 350px);
  }

  .score-legend {
    display: flex;

    justify-content: left;
    margin-bottom: 20px;
    gap: 20px;
    // align-items: center;

    .score-list {
      display: flex;
      // width: calc(100% - 60px);
      gap: 20px;
      align-items: center;
      flex-wrap: wrap;
      height: 36px;
      overflow: hidden;
    }

    .down-icon {
      height: auto;
    }


    .legend-item {
      display: flex;
      align-items: center;
      cursor: pointer;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .legend-text {
        height: 20px;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;
      }
    }
  }

  .other-info {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    margin-top: 24px;
    font-size: 12px;
    padding: 8px;
    background-color: #f7f7fa;

    .other-info-item-click {
      color: #007aff;
      cursor: pointer;
      margin-left: 8px;
    }

    .other-info-item-icon {
      width: 16px;
      height: 16px;
      font-size: 16px;
      margin-right: 4px;
      color: #ff7c4d;
    }
  }
}
</style>
<style lang="scss">
.legend-text-tooltip {
  box-shadow: 0px 8px 20px 0px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  // gap: 8px;

  .legend-text-tooltip-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;

  }
}
</style>

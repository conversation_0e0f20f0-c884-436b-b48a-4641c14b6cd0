<template>
  <div class="communication-analysis-page">
    <el-tabs v-model="activeTab" class="analysis-tabs">
      <el-tab-pane label="维度" name="dimension">
        <AnalysisDimensionList v-if="activeTab === 'dimension'" ref="dimensionRef" />
      </el-tab-pane>
      <el-tab-pane label="模板" name="template">
        <AnalysisTemplateTable v-if="activeTab === 'template'" ref="templateRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import AnalysisDimensionList from './components/AnalysisDimensionList.vue';
import AnalysisTemplateTable from './components/AnalysisTemplateTable.vue';

const activeTab = ref('dimension');
const dimensionRef = ref();
const templateRef = ref();

</script>

<style lang="scss">
.communication-analysis-page {
  padding: 20px;

  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9 !important;
  }

  .el-drawer__body {
    padding: 24px;
    box-sizing: border-box;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }
}
</style>
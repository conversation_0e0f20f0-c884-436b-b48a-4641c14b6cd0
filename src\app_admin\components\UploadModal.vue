<template>
    <el-dialog v-model="isShow" title="上传文件" width="960px" :before-close="handleClose" class="upload_diglog">
        <div class="left" @click="onLeft" ref="refLeftbox">
            <addFile v-if="refFiles.length == 0" />
            <div v-if="refFiles.length > 0" class="note">
                已经选择{{ refFiles.length }}个文件
            </div>
            <div v-else class="note">
                将文件拖到此处，或<div class="upload_txt">点击上传</div>
            </div>
            <div class="subnote">{{ config.subHint }}</div>
        </div>
        <div class="right">
            <div class="class_line">
                <div class="title">文件名称</div>
                <div class="subject"> {{ subject }}</div>
            </div>
            <div class="class_line" v-show="config.options.length > 0">
                <div class="title">所属分类</div>
                <el-select v-model="selectCate" class="m-2" placeholder="请选择">
                    <el-option v-for="item in config.options" :key="item.value" :label="item.label"
                        :value="item.value" />
                </el-select>
            </div>
            <slot name="right_bottom" />
        </div>
        <input class="fileInput" type="file" ref="fileInt" multiple @change="changeHandle" @click="fileUploadCheck" />
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onOk">
                    上传
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup>
import { formatFileSize } from "@/js/utils.js"
import addFile from "@/app_admin/icons/addFile.vue"

const isShow = ref(false);
const fileInt = ref();
const refLeftbox = ref();
const op_hint = ref('')
const refFiles = ref([])
const config = ref({})
const subject = ref('')
const selectCate = ref("")
const loading = ref(false)

const onLeft = () => {
    fileInt.value.click()
}

const addDragListener = () => {
    var dropzone = refLeftbox.value;
    let i = 0;
    let timer = null
    dropzone.addEventListener('dragover', function (e) {
        i = 0;
        op_hint.value = '松开上传';
        e.preventDefault(); // 阻止默认行为
    });
    dropzone.addEventListener('dragleave', function (e) {
        i += 1;
        timer && clearTimeout(timer)
        timer = setTimeout(() => {
            if (i > 0) {
                op_hint.value = '点击 、拖拽 本地音视频文件到这里';
            }
        })
        e.preventDefault(); // 阻止默认行为
    });

    dropzone.addEventListener('drop', function (e) {
        i = 0;
        op_hint.value = '点击 、拖拽 本地音视频文件到这里';
        e.preventDefault(); // 阻止默认行为
        // 获取拖拽的文件
        _changeHandle(e.dataTransfer.files);
    });
}
const changeHandle = () => {
    _changeHandle(fileInt.value.files)
}

const _changeHandle = (files) => {
    if (files.length == 0) {
        console.log('没有选择文件 ？')
        return
    }
    refFiles.value = []
    subject.value = ''
    for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const { name, size, type } = file
        if (!type) {
            ElMessage.error(`${name}文件类型不支持，已忽略`)
            continue
        }
        if (size > config.value.maxSizeMb * 1048576) {
            ElMessage.error(`文件${name}大于${config.value.maxSizeMb}MB，已忽略`)
            continue
        }
        const ext = name.split('.').pop();
        if (!config.value.fileTypes.includes(ext.toLowerCase())) {
            ElMessage.error(`${name}文件类型不支持，已忽略`)
            continue
        }
        refFiles.value.push(file)
        subject.value += (i == 0 ? '' : ',') + name.replace(/\.[^/.]+$/, '')
    }
    fileInt.value.value = null;
}

const rechoose = () => {
    refFiles.value = [];
    subject.value = '';
    op_hint.value = '';
}

const onCancel = () => {
    isShow.value = false;
}

const onOk = () => {
    if (refFiles.value.length == 0) {
        ElMessage.error('请先选择文件！')
        return
    }
    if (!selectCate.value) {
        ElMessage.error('请选择分类！')
        return
    }
    if (loading.value) {
        return
    }
    loading.value = true;
    for (let i = 0; i < refFiles.value.length; i++) {
        const file = refFiles.value[i];
        const { name, size, type } = file
        let param = {
            ...{
                source: config.value.type,
                startTime: `${new Date().getTime()}_${i}`,
                subject: name,
                file: file,
                size: formatFileSize(size),
                categoryId: selectCate.value,
                status: 'waiting',
                tags: config.value.tags || []
            },
            ...config.value.param
        }
        g.adminFileStore.add_file(param)
    }
    g.emitter.emit('add_ppt_file', '');
    loading.value = false;
    isShow.value = false;
}

const handleClose = () => {
    isShow.value = false;
}

const show = (_config, id = '') => {
    config.value = _config;
    rechoose();
    isShow.value = true;
    nextTick(() => {
        addDragListener()
        selectCate.value = id;
    })
}

defineExpose({
    fileInt, refLeftbox, op_hint, refFiles, config,
    subject, selectCate, isShow, onOk, show
})

</script>

<style lang="scss">
.upload_diglog {
    .el-dialog__header {
        border-bottom: 1px solid #E9E9E9;
        margin-right: 0;
    }

    .el-dialog__footer {
        border-top: 1px solid #E9E9E9;
        padding-top: 20px;
    }

    .el-dialog__body {
        display: flex;
        flex-direction: row;

        .left {
            display: flex;
            flex-direction: column;
            width: 400px;
            height: 225px;
            background: #FAFAFA;
            border-radius: 4px;
            border: 1px solid #D9D9D9;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .note {
                font-weight: 500;
                color: #262626;
                display: flex;
                flex-direction: row;

                .upload_txt {
                    color: var(--el-color-primary);
                }
            }

            .subnote {
                margin: 10px 0;
            }
        }

        .right {
            display: flex;
            flex-direction: column;
            padding: 5px 24px;
            width: 50%;
            min-height: 100px;
            max-height: 400px;
            overflow-y: auto;

            .class_line {
                display: flex;
                flex-direction: column;

                .title {
                    line-height: 32px;
                    margin-right: 10px;
                    margin-top: 10px;
                }
            }
        }

        .fileInput {
            opacity: 0;
            position: absolute;
            top: 80px;
        }
    }
}
</style>
<template>
    <div class="example-page">
        <h1>日期时间选择器使用示例</h1>

        <div class="example-item">
            <h3>基础用法</h3>
            <DateTimePicker v-model="dateTime" />
            <p>选择的值: {{ dateTime ? dayjs(dateTime).format('YYYY-MM-DD HH:mm:ss') : '未选择' }}</p>
        </div>

        <div class="example-item">
            <h3>表单中使用</h3>
            <el-form :model="form" label-width="120px">
                <el-form-item label="预约时间">
                    <DateTimePicker v-model="form.appointmentTime" />
                </el-form-item>
                <el-form-item label="会议时间">
                    <DateTimePicker v-model="form.meetingTime" format="YYYY年MM月DD日 HH时mm分" placeholder="请选择会议时间" />
                </el-form-item>
            </el-form>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import DateTimePicker from './DateTimePicker.vue'
import dayjs from 'dayjs'

const dateTime = ref(null)

const form = reactive({
    appointmentTime: null,
    meetingTime: null
})
</script>

<style scoped>
.example-page {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.example-item {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fafafa;
}

.example-item h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #303133;
}

.example-item p {
    margin-top: 12px;
    color: #606266;
    font-size: 14px;
}
</style>
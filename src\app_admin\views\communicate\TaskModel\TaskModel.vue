<template>
  <div class="task-model-page">
    <el-tabs v-model="activeTab" class="model-tabs">
      <el-tab-pane label="任务项" name="task">
        <TaskListTable v-if="activeTab === 'task'" ref="taskListRef" />
      </el-tab-pane>
      <el-tab-pane label="任务模型" name="model">
        <TaskModelTable v-if="activeTab === 'model'" ref="taskModelTableRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import TaskListTable from './components/TaskListTable.vue';
import TaskModelTable from './components/TaskModelTable.vue';

const activeTab = ref('task');
const taskListRef = ref();
const taskModelTableRef = ref();

</script>

<style lang="scss">
.task-model-page {
  padding: 20px;
  font-size: 14px;
  color: #262626;

  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9 !important;
  }

  .el-drawer__body {
    padding: 16px 24px;
    box-sizing: border-box;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }

}
</style>
<template>
    <div class="opportunity-status">
        <div class="status-indicator">
            <div class="status-dot" :class="statusClass"></div>
            <span class="status-text">{{ statusText }}</span>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps(['row']);

const statusText = computed(() => {
    return props.row?.opportunity_status || '进行中';
});

const statusClass = computed(() => {
    const status = props.row?.opportunity_status || '进行中';
    switch (status) {
        case '进行中':
            return 'in-progress';
        case '已完成':
            return 'completed';
        case '已暂停':
            return 'paused';
        case '已取消':
            return 'cancelled';
        default:
            return 'in-progress';
    }
});

defineExpose({
    statusText,
    statusClass
});
</script>

<style lang="scss" scoped>
.opportunity-status {
    display: flex;
    align-items: center;
    height: 44px;

    .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;

            &.in-progress {
                background-color: #436bff;
            }

            &.completed {
                background-color: #52c41a;
            }

            &.paused {
                background-color: #faad14;
            }

            &.cancelled {
                background-color: #ff4d4f;
            }
        }

        .status-text {
            font-size: 14px;
            color: #262626;
            font-weight: 400;
        }
    }
}
</style>

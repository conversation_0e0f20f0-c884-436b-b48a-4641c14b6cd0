import fs from 'node:fs'
import vue from '@vitejs/plugin-vue'
import electron from 'vite-plugin-electron/simple'
import pkg from './package.json'
import { fileURLToPath, URL } from 'node:url';
import { defineConfig, loadEnv } from 'vite'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { injectHtml } from 'vite-plugin-html'
import IconsResolver from 'unplugin-icons/resolver'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import { visualizer } from 'rollup-plugin-visualizer';
import { OssVitePlugin } from 'oss-webpack-plugin'
import { VantResolver } from '@vant/auto-import-resolver';
// @ts-ignore
const viteVersionPlugin = require('./build/vite-version-plugin')

// @ts-ignore
import pkgJson from './package.json'
const { getCompileConfig } = require('yxt-fe-center')

const projectName = pkgJson.name
const feConfig = getCompileConfig(projectName)

const node_version = process.version.split('.')[0];
if (node_version !== 'v16' && node_version !== 'v18') {
  console.log('Fail to build. Node version should be 16 or 18, while current is ', process.version);
  process.exit(1);
}


// https://vitejs.dev/config/
export default defineConfig((option) => {
  const env = loadEnv(option.mode, process.cwd() + '/env');
  console.log('env', env)
  const lifecycle = process.env.npm_lifecycle_event;
  fs.rmSync('dist-electron', { recursive: true, force: true })

  const isServe = option.command === 'serve'
  const isBuild = option.command === 'build'
  const sourcemap = isServe || !!process.env.VSCODE_DEBUG

  // 优化 element-plus 的依赖
  const optimizeDepsElementPlusIncludes = ["element-plus/es"]

  // 使用同步方式读取目录并处理
  const elementPlusComponents = fs.readdirSync("node_modules/element-plus/es/components")
  elementPlusComponents.forEach((dirname) => {
    const stylePath = `node_modules/element-plus/es/components/${dirname}/style/css.mjs`
    if (fs.existsSync(stylePath)) {
      optimizeDepsElementPlusIncludes.push(
        `element-plus/es/components/${dirname}/style/css`
      )
    }
  })

  const electron_func = electron({
    main: {
      // Shortcut of `build.lib.entry`
      entry: 'electron/main/index.ts',
      onstart({ startup }) {
        if (process.env.VSCODE_DEBUG) {
          console.log(/* For `.vscode/.debug.script.mjs` */'[startup] Electron App')
        } else {
          startup()
        }
      },
      vite: {
        build: {
          sourcemap,
          minify: isBuild,
          outDir: 'dist-electron/main',
          rollupOptions: {
            // Some third-party Node.js libraries may not be built correctly by Vite, especially `C/C++` addons, 
            // we can use `external` to exclude them to ensure they work correctly.
            // Others need to put them in `dependencies` to ensure they are collected into `app.asar` after the app is built.
            // Of course, this is not absolute, just this way is relatively simple. :)
            external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
          },
        },
      },
    },
    preload: {
      // Shortcut of `build.rollupOptions.input`.
      // Preload scripts may contain Web assets, so use the `build.rollupOptions.input` instead `build.lib.entry`.
      input: 'electron/preload/index.ts',
      vite: {
        build: {
          sourcemap: sourcemap ? 'inline' : undefined, // #332
          minify: isBuild,
          outDir: 'dist-electron/preload',
          rollupOptions: {
            external: Object.keys('dependencies' in pkg ? pkg.dependencies : {}),
          },
        },
      },
    },
    // Ployfill the Electron and Node.js API for Renderer process.
    // If you want use Node.js in Renderer process, the `nodeIntegration` needs to be enabled in the Main process.
    // See 👉 https://github.com/electron-vite/vite-plugin-electron-renderer
    renderer: {},
  })

  const autoimport_func = AutoImport({
    // 自动引用ref，reactive等
    imports: ['vue', 'vue-router'],
    // Auto import functions from Element Plus, e.g. ElMessage, ElMessageBox... (with style)
    // 自动导入 Element Plus 相关函数，如：ElMessage, ElMessageBox... (带样式)
    resolvers: [
      ElementPlusResolver(),
      VantResolver(),

      // Auto import icon components
      // 自动导入图标组件
      IconsResolver({
        prefix: 'Icon',
      }),
    ],

  });

  let vite_base = '';
  if (env.VITE_IN_ELECTRON) {
    vite_base = ''
  } else {
    vite_base = env.VITE_OSS_PATH
  }
  return {
    envDir: 'env',
    optimizeDeps: {
      include: optimizeDepsElementPlusIncludes,
    },
    plugins: [
      vue(),
      autoimport_func,
      injectHtml({
        data: {
          feConfig
        }
      }),
      Components({
        resolvers: [ElementPlusResolver(), VantResolver()],
      }),
      OssVitePlugin({
        ossToken: feConfig.ossToken
      }),
      viteVersionPlugin(),
      env.VITE_IN_ELECTRON ? electron_func : null,
      lifecycle === 'report' ? visualizer({ open: true, brotliSize: true, filename: 'report.html' }) : null
    ],
    base: vite_base,
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    clearScreen: false,
    css: {
      preprocessorOptions: {
        scss: {
          // to remove the hint: Deprecation Warning: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.
          silenceDeprecations: ["legacy-js-api"],
        },
      },
    },
    esbuild: {
      target: 'es2015',  // 修改 esbuild 目标
      // drop: ["console"],
    },
    build: {
      target: ['es2015', 'chrome63'],
      chunkSizeWarningLimit: 600,
      rollupOptions: {
        output: {
          format: 'es',
          generatedCode: {
            constBindings: true,
            arrowFunctions: false,
            objectShorthand: false
          },
          manualChunks: {
            'vue-core': ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus'],
            'echarts': ['echarts'],
            'video.js': ['video.js'],
            'markdown': ['marked', 'markdown-it', '@iktakahiro/markdown-it-katex'],
            'livekit': ['livekit-client'],
            'utils': ['lodash-es', 'axios', 'dayjs'],
          }
        }
      }
    },
    server: {
      host: "localhost",
      port: 5173,
      open: !env.VITE_IN_ELECTRON,
    }
  }
})

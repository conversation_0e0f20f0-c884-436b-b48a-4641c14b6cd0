<template>
  <div :class="`vpt_wrap ${props.team ? 'team_visit_wrap' : 'my_visit_wrap'} `">
    <slot name="header"></slot>
    <div class="v_header">
      <div class="vline2 flex-row">
        <BtnExport :param="datas.param" />
        <BtnDate v-model:start="datas.param.startTime" v-model:end="datas.param.endTime" @reload="onDateChange"
          defaultType="last_week" />
        <DrawerSelectMixed ref="refUser" @callback="onSelectUser" type="user" :rootDeptId="rootDeptId" />
        <SelectStatus ref="refStatus" v-model:value="completeStatus" @reload="onSearch" />
        <InputSearch v-model:value="datas.param.searchKey" @reload="onSearch" />
      </div>
      <StaticPad ref="refStatic" />
    </div>
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
      <template #col_subject="{ row }">
        <div :id="row.scheduleId">
          {{ row.subject }}
        </div>
      </template>
      <template #col_completeStatus="{ row }">
        {{ trans(row.status) }}
      </template>
      <template #col_salesMateCustomerName="{ row }">
        {{ row.salesMateCustomerName || "-" }}
      </template>
      <template #col_planDuration="{ row }">
        {{ row.recordDurationSeconds || "0" }}
      </template>
      <template #col_viewRecordLink="{ row }">
        <div>
          <el-link type="primary" @click="onView(row)" v-if="row.conferenceId && row.completed">查看</el-link>
          <span v-else>-</span>
        </div>
      </template>
    </MyTable>
  </div>
</template>

<script setup>
import { getTeamScheduleList } from "@/js/api.js";
import { getDefaultDateRange, getUrlSsoUserId } from "@/app_client/tools/utils.js";
import trans from "@/js/lang.js";
import MyTable from "@/components/Table.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import SelectStatus from "./SelectStatus.vue";
import InputSearch from "./InputSearch.vue";
import StaticPad from "./static";
import BtnExport from "./BtnExport.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import { removeURLParams } from "@/js/utils.js";

const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const emit = defineEmits(["callback"]);
const refTable = ref();
const refStatic = ref();
const refStatus = ref();
const enable_edit = ref(!props.team);
const completeStatus = ref("all");
const refUser = ref()

const onSelectUser = (action, data) => {
  datas.param.ssoUserIds = data.users.map((x) => x.id);
  onSearch();
};

const _getDuration = (row) => {
  const startTime = row.scheduleStartTime || row.startTime;
  const endTime = row.scheduleEndTime || row.endTime;
  const start = new Date(startTime).getTime();
  const end = new Date(endTime).getTime();
  const durationSeconds = (end - start) / 1000;
  const hour = Math.floor(durationSeconds / 3600);
  const min = Math.floor((durationSeconds % 3600) / 60);

  let txt = "";
  if (hour > 0) {
    txt += `${hour}小时`;
  }
  if (min > 0 || hour === 0) {
    txt += `${min}分钟`;
  }
  return txt;
};

const _getStatus = (row) => {
  if (row.completed) {
    return "completed";
  } else if (row.inProgress) {
    return "ongoing";
  } else {
    return "notCompleted";
  }
};

const _getCompleted = () => {
  if (completeStatus.value == "all") {
    return null;
  } else {
    return completeStatus.value == "true";
  }
};

const _getMyTeamMeets = (p) => {
  return new Promise((resolve, reject) => {
    const param = { ...p };
    param.completed = _getCompleted();
    if (typeof param.dptIds == "string") {
      param["dptIds"] = [param["dptIds"]];
    }
    delete param.completeStatus;
    getTeamScheduleList(param).then((resp) => {
      if (resp.code == 0 && resp.data.datas) {
        resp.data.datas.forEach((x) => {
          x.recordDurationSeconds = _getDuration(x);
          x.status = _getStatus(x);
        });
        refStatic.value && refStatic.value.init(resp.data);
        resolve(resp);
      } else {
        reject(resp);
      }
    });
  });
};

const _urlDelete = (row) => {
  return deleteRecordByConfId(row.conferenceId);
};

const onView = (item) => {
  item["recognitionPath"] = "-";
  g.clientStore.viewPlanRecord(item);
};
const { startTime, endTime } = getDefaultDateRange()

const datas = reactive({
  tableid: 'visit_plan_team',
  param: {
    searchKey: '',
    salesMateTags: "",
    completed: _getCompleted(),
    username: "",
    meetingType: 7,
    startTime,
    endTime,
    dptIds: [], //要查询的部门id
    ssoUserIds: getUrlSsoUserId(),
  },
  need_init_load: false,
  need_header: false,
  form: {},
  view_txt: "查看详情",
  modal_type: "link",
  search_ph: "请输入客户名称",
  delete_hint_column: "subject",
  show_link_column: true,
  show_link_view: true,
  show_link_delete: enable_edit.value,
  show_link_edit: enable_edit.value,
  enable_checkbox: enable_edit.value,
  columns: [
    "subject",
    "scheduleStartTime",
    "planDuration",
    "hostName",
    "completeStatus",
    "salesMateCustomerName",
    "salesMateTags",
    "viewRecordLink",
  ],
  template: ["subject", "salesMateCustomerName", "planDuration", "completeStatus", "viewRecordLink"],
  urlGet: _getMyTeamMeets,
  urlDelete: _urlDelete,
});
const rootDeptId = computed(() => datas.param.dptIds.length > 0 ? datas.param.dptIds[0] : '');
const onSearch = () => {
  refTable.value.search();
};

const onDateChange = () => {
  removeURLParams(['startDate', 'endDate'])
  onSearch();
}

const cbDatas = (action, data) => {
  if (action == "init_view") {
    emit("callback", "visit_detail", data);
  }
};

const setDeptIds = (ids) => {
  datas.param.dptIds = ids;
  refUser.value.reset();
  onSearch();
};

defineExpose({
  refStatic, refTable, onSelectUser, MyTable, DrawerSelectMixed, cbDatas, refTable, BtnDate, SelectStatus, BtnExport, InputSearch, StaticPad, setDeptIds, props, refStatus, enable_edit,
});

defineOptions({
  name: "VisitRecord",
});
</script>

<style lang="scss" scoped>
.vpt_wrap {
  overflow: auto;
  background: #fff;

  .table_wrap {
    padding: 24px 0;
  }

  .vline2 {
    margin-top: 24px;
    gap: 12px;

    .sales_search {
      width: 150px;
      margin-right: 12px;
    }
  }

  .vline3 {
    height: 20px;
    font-size: 12px;
    color: #8c8c8c;
    font-style: normal;
    margin: 24px 0;
  }

  .col_operation_ {
    .el-button {
      padding: 0;
    }
  }
}

.vpt_wrap {
  :deep(table) {
    width: 1900px !important;
  }
}
</style>

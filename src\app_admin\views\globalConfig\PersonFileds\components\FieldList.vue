<template>
    <div class="course-maintenance-config">
        <ul>
            <li v-for="field in fields[props.formCode]" :key="field.value">
                <div class="label-wrap">
                    <span>{{ field.fieldName }}</span>
                    <i v-if="field.isRequired" class="required-dot"></i>
                </div>
                <div class="right-wrap" v-if="field.fieldName != '姓名'">
                    <span class="required-text" @click="onUpdate('isRequired', field)">{{ field.isRequired == 0 ? '设为必填'
                        : '设为非必填' }}</span>
                    <span class="delete-text" @click="onCallback('delete', field)">删除</span>
                    <span class="delete-text" @click="onCallback('edit', field)">编辑</span>
                    <el-switch v-model="field.fieldStatus" :active-value="1" :inactive-value="0"
                        @change="onUpdate('fieldStatus', field)" />
                </div>
            </li>
            <li>
                <div class="add-custom-field" @click="onCallback('add')">添加自定义属性</div>
            </li>
        </ul>
    </div>
</template>

<script setup>
// {
// 			"fieldName":"岗位",
// 			"fieldStatus":1,
// 			"fieldType":"String", // String, Select
// 			"fieldOptions":[],
// 			"formId":"cb019498-b734-4416-a8e0-465df1ca909b",
// 			"id":"09a46ace-8733-4753-89f9-4ef96f601e53",
// 			"isRequired":1,
// 			"maxLength":50,
// 			"minLength":1,
// 			"orgId":"6a759132-4ae7-40b2-8da5-7ce4bfc81798",
// 			"placeholder":"岗位",
// 			"sortOrder":1
// 		}
const emit = defineEmits(['callback'])
const props = defineProps({
    formCode: {
        type: String,
        required: true
    },
    fields: {
        type: Object,
        required: true
    }
})

const onUpdate = (column, field) => {
    if (column == 'isRequired') {
        field.isRequired = field.isRequired == 1 ? 0 : 1
    }
    emit('callback', 'update', props.formCode, field)
}

const onCallback = (action, field) => {
    if (action == 'add') {
        field = {
            sortOrder: props.fields[props.formCode].length + 1
        }
    }
    emit('callback', action, props.formCode, field)
}


defineExpose({
    onCallback
})

</script>

<style lang="scss" scoped>
.course-maintenance-config {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 24px;

    ul {
        width: 800px;
        // margin: 0 auto;
        padding-bottom: 48px;
        background-color: #fff;
        border-radius: 4px;

        li {
            padding: 9px 24px;
            background-color: #fff;
            border-right: 1px solid #e9e9e9;
            border-bottom: 1px solid #e9e9e9;
            border-left: 1px solid #e9e9e9;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            font-size: 14px;
            color: #262626;
            text-align: left;

            &:first-child {
                border-top: 1px solid #e9e9e9;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }

            &:last-child {
                border-bottom-right-radius: 5px;
                border-bottom-left-radius: 5px;
            }

            &:hover {
                background-color: #f0f6ff;

                .right-wrap {
                    .required-text {
                        display: block;
                    }

                    .delete-text {
                        display: block;
                    }
                }
            }

            .label-wrap {
                display: flex;
                align-items: center;
                gap: 4px;
            }

            .required-dot {
                width: 6px;
                height: 6px;
                background-color: #f56c6c;
                border-radius: 50%;
            }

            .right-wrap {
                display: flex;
                align-items: center;
                gap: 8px;

                .required-text {
                    cursor: pointer;
                    font-size: 12px;
                    display: none;
                    color: #4366ff;
                    margin-right: 24px;
                }

                .delete-text {
                    cursor: pointer;
                    font-size: 12px;
                    display: none;
                    color: #4366ff;
                }
            }

            .add-custom-field {
                cursor: pointer;
                font-size: 14px;
                color: #4366ff;
            }
        }
    }
}
</style>
<template>
    <div class="form-arrange-wrap">
        toB： {{ isToB }}，商机： {{ isShangji }}，维护了客户的沟通流程： {{ hasCustomerFlow }}
        <div class="av_item" v-show="isShangji && hasCustomerFlow">
            <CustomerTypeSelector v-model="customerType" @change="onCustomerTypeChange" />
        </div>
        <div class="av_item">
            <FormTitle title="选择客户" required />
            <div class="av_item_value customer-picker">
                <CustomerNamePicker ref="refCustomerNamePicker" v-model="localFormdata.salesMateCustomerName"
                    v-model:customerId="localFormdata.customerId" />
            </div>
        </div>
        <div class="av_item">
            <FormTitle title="沟通环节" required />
            <TagPicker v-model="localFormdata.stageId" @change="onTagChange" ref="refTagPicker" :type="customerType"
                v-model:queryId="queryId" />
        </div>
        <div class="av_item">
            <FormTitle title="沟通主题" required />
            <div class="av_item_value">
                <el-input type="text" placeholder="输入沟通主题" v-model="localFormdata.subject" maxlength="50"
                    show-word-limit></el-input>
            </div>
        </div>
    </div>
</template>

<script setup>
import CustomerNamePicker from "@/components/CustomerNamePicker/CustomerNamePicker.vue";
import TagPicker from "./TagPicker.vue";
import CustomerTypeSelector from "./CustomerTypeSelector.vue";
import FormTitle from "./FormTitle.vue";
import { communicateApi } from "@/app_admin/api";
const isShangji = computed(() => g.appStore.isShangji);
const isToB = computed(() => g.appStore.isToB);
const hasCustomerFlow = ref(false);

const props = defineProps({
    formdata: {
        type: Object,
        required: true
    }
});

const emit = defineEmits(['update:formdata', 'customerTypeChange', 'tagChange']);

// 创建本地formdata的响应式引用，用于双向绑定
const localFormdata = computed({
    get: () => props.formdata,
    set: (value) => emit('update:formdata', value)
});

const customerType = ref('customer');
const queryId = ref(0);
const refTagPicker = ref();
const refCustomerNamePicker = ref();

const onCustomerTypeChange = (type) => {
    console.log('客户类型变化:', type);
};

// 监听customerType和formdata的变化来更新queryId
watch(() => [customerType.value, localFormdata.value], (newVal) => {
    if (!newVal[1]) return
    if (newVal[0] == 'customer') {
        queryId.value = newVal[1].customerId;
    } else {
        queryId.value = 1;
    }
}, { immediate: true, deep: true });

const tagItem = ref({});
const onTagChange = (item) => {
    tagItem.value = item;
};


watch(() => [localFormdata.value.salesMateCustomerName, tagItem.value], (newVal) => {
    if (newVal[0] && tagItem.value.name) {
        localFormdata.value.subject = newVal[0] + ' - ' + tagItem.value.name;
    }
}, { immediate: true, deep: true });

watch(() => [hasCustomerFlow.value, isShangji.value], (newVal) => {
    if (isShangji.value && !hasCustomerFlow.value) {
        customerType.value = 'opportunity'
    } else {
        customerType.value = 'customer'
    }
}, { immediate: true, deep: true });

const getCustomerFlow = async () => {
    const param = { "pageSize": 1, "pageNumber": 1, "type": "1" }
    const resp = await communicateApi.pageCommunicationFlow(param)
    if (resp.code == 0) {
        hasCustomerFlow.value = resp.data.totalNum > 0
    }
}

onMounted(async () => {
    await getCustomerFlow()
})

// 暴露方法给父组件
defineExpose({
    refTagPicker,
    refCustomerNamePicker,
    customerType,
    queryId,
    localFormdata
});
</script>

<style lang="scss" scoped>
.form-arrange-wrap {
    display: flex;
    flex-direction: column;

    .av_item {
        width: 99%;
        display: flex;
        flex-direction: column;
        margin: 10px 0;

        .av_item_icon {
            width: 30px;
        }

        .av_item_value {
            width: 90%;

            .el-select {
                width: 100%;
            }
        }
    }
}
</style>

<template>
  <div class="opportunity_field_wrap">
    <FieldList ref="refFieldList" type="OPPORTUNITY_TPL" />
  </div>
</template>
<script setup>
import FieldList from './Field/FieldList.vue';

const refFieldList = ref(null)

onMounted(() => {
  refFieldList.value.initLoad()
})

defineExpose({
  FieldList
})

</script>

<style lang="scss">
.opportunity_field_wrap {
  padding: 12px 24px 24px 24px;
}
</style>

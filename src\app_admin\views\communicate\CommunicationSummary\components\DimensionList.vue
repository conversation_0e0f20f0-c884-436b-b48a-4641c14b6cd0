<template>
  <div class="dimension-container">
    <div class="dimension-grid">
      <div class="dimension-card" v-for="dimension in dimensions" :key="dimension.id">
        <div class="card-content">
          <div class="card-footer">
            <div class="card-title">
              <span class="title-text">{{ dimension.name }}</span>
              <span :class="`el-tag-1 ${dimension.system ? 'dimension-2' : 'dimension-1'}`">{{ dimension.system ? '系统' :
                '自定义' }}</span>
            </div>
          </div>
          <div class="card-placeholder">
            <span>示例图</span>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { getDimension } from "@/app_admin/api/communicate.js";

const dimensions = ref([]);

// 获取维度数据
const loadDimensions = async () => {
  try {
    const response = await getDimension({
      type: "SUMMARY"
    });
    if (response.code === 0) {
      dimensions.value = response.data.datas;
    }
  } catch (error) {
    console.error('获取维度数据失败:', error);
    ElMessage.error('获取维度数据失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDimensions();
});
</script>

<style lang="scss" scoped>
.dimension-container {
  padding: 24px 0;
  height: calc(100vh - 150px);
  box-sizing: border-box;
  overflow-y: auto;
}

.dimension-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.dimension-card {
  background: #F7F8FC;
  border-radius: 12px 12px 12px 12px;
  padding: 12px;
  box-sizing: border-box;

  // &:hover {
  //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  //   border-color: #409eff;
  // }
}

.card-content {
  .card-placeholder {
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    height: 228px;

  }

  .card-footer {
    padding: 0 12px 12px 12px;

    .card-title {
      display: flex;
      align-items: center;



      .title-text {
        font-weight: 600;
        font-size: 14px;
        color: #262626;
        line-height: 22px;
        text-align: left;
      }

      .el-tag-1 {
        padding: 1px 6px;
        border-radius: 4px 4px 4px 4px;
        font-weight: 400;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        display: inline-block;
        margin-left: 8px;
      }

      .dimension-1 {
        background: rgba(4, 204, 164, 0.1);
        color: #04CCA4;
      }

      .dimension-2 {
        background: #F0F6FF;
        color: #436BFF;
      }

    }
  }
}
</style>
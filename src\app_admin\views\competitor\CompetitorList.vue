<template>
    <div class="competitor-list-wrap" v-loading="loading" element-loading-text="正在加载...">
        <el-alert class="top-tip" type="info" show-icon closable title="用于识别会议过程中是否有提及及竞争对手，以及提及次数">

            <template #icon>
                <el-icon>
                    <WarningFilled style="color: #436BFF;" />
                </el-icon>
            </template>
        </el-alert>

        <div class="content-card">
            <div class="action-bar">
                <el-button type="primary" @click="handleAdd">
                    新建
                </el-button>
                <el-button :disabled="loading" @click="handleBatchImport">

                    批量导入
                </el-button>
                <el-button :disabled="loading" @click="downloadTemplate">
                    下载模板
                </el-button>
                <input type="file" ref="fileInput" @change="onFileChange" style="display: none;" accept=".xlsx,.xls" />

            </div>

            <div class="card-grid hide-scrollbar">
                <div class="competitor-card" v-for="item in list" :key="item.id">
                    <div class="card-header">
                        <div class="competitor-name">{{ item.commonName }}</div>
                        <div class="ops">
                            <el-icon @click="onEdit(item)">
                                <EditPen style="color: #436BFF;" />

                            </el-icon>
                            <el-icon @click="onDelete(item)">
                                <Delete style="color: #436BFF;" />
                            </el-icon>

                        </div>
                    </div>
                    <div class="card-row">
                        <span class="label">公司名称</span>
                        <span class="value">{{ item.companyName || '-' }}</span>
                    </div>
                    <div class="card-row">
                        <span class="label">公司别称</span>
                        <div class="value">
                            {{ item.alternativeName || '-' }}
                        </div>
                    </div>
                </div>

                <el-empty v-if="!loading && list.length === 0" description="暂无数据" />
            </div>
        </div>
        <!-- <DrawerUpload ref="refDrawerUpload" :isAdmin="true" /> -->
        <CompetitorEditDialog ref="refEditDialog" @callback="onDialogCallback" />
    </div>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import { Delete, WarningFilled, EditPen } from '@element-plus/icons-vue';
import CompetitorEditDialog from './CompetitorEditDialog.vue';
import { competitorApi } from '@/app_admin/api';
// import DrawerUpload from "@/components/BtnCustomerFile/DrawerUpload.vue";

const refEditDialog = ref();
const refDrawerUpload = ref();
const loading = ref(false);
const list = ref([]);
const fileInput = ref(null);

const loadList = async () => {
    try {
        loading.value = true;
        const resp = await competitorApi.getCompetitor();
        if (resp.code === 0) {
            list.value = resp.data || [];
        } else {
            ElMessage.error(resp.message || '获取数据失败');
        }
    } catch (error) {
        ElMessage.error(error.message || '获取数据失败');
    } finally {
        loading.value = false;
    }
};



// 添加竞争对手
const handleAdd = () => {
    refEditDialog.value.show_add();
};

// 编辑
const onEdit = (row) => {
    refEditDialog.value.show_edit(row);
};

// 删除
const onDelete = (row) => {
    const name = row.commonName || '';
    ElMessageBox.confirm(`确定删除“${name}”吗？`, '提示', {
        type: 'warning',
        confirmButtonText: '删除',
        cancelButtonText: '取消'
    })
        .then(async () => {
            const resp = await competitorApi.deleteCompetitor(row.id);
            if (resp.code === 0) {
                ElMessage.success('删除成功');
                loadList();
            } else {
                ElMessage.error(resp.message || '删除失败');
            }
        })
        .catch(() => { });
};

// 批量导入
const handleBatchImport = () => {
    fileInput.value.click();
};

// 下载模板
const downloadTemplate = async () => {
    try {
        loading.value = true;
        const param = {
            filename: '批量导入竞争对手模板'
        };
        await competitorApi.downloadCompetitorTemplate(param);
    } catch (error) {
        ElMessage.error('模板下载失败，请重试');
        console.error('Download template failed:', error);
    } finally {
        loading.value = false;
    }
};

const onFileChange = () => {
    const file = fileInput.value.files[0];
    if (!file) return;

    loading.value = true;
    const formData = new FormData();
    formData.append('file', file);

    competitorApi.batchUploadCompetitor(formData, '', onUploadSuccess, onUploadFail);
};

const onUploadSuccess = (data) => {
    loading.value = false;
    clearFileInput();
    if (data.code == 0) {
        if (data.data && data.data.length === 0) {
            ElMessage.success("批量导入成功");
            loadList();
        } else {
            const tip = data.data?.join('<br>') || '导入完成，但有部分数据存在问题';
            ElMessage({
                dangerouslyUseHTMLString: true,
                message: tip,
                type: 'warning',
            });
            loadList();
        }
    } else {
        ElMessage.error(data.message || '导入失败');
    }
};

const onUploadFail = (error) => {
    loading.value = false;
    clearFileInput();
    ElMessage.error("Excel解析失败，请检查文件格式后重新上传");
    console.error('Upload failed:', error);
};

const clearFileInput = () => {
    if (fileInput.value) {
        fileInput.value.value = '';
    }
};





// 对话框回调处理
const onDialogCallback = (action) => {
    if (action === 'reload') {
        loadList();
    }
};

onMounted(() => {
    loadList();
});
</script>

<style lang="scss">
.competitor-list-wrap {
    padding: 24px;
    box-sizing: border-box;

    .top-tip {
        background: #F0F6FF !important;
        border-radius: 8px 8px 8px 8px;
        color: #262626 !important;
    }

    .action-bar {
        margin-bottom: 16px;
        display: flex;
        gap: 12px;

        .el-button {
            display: flex;
            align-items: center;
            gap: 4px;
        }
    }

    .content-card {
        background: #fff;
        border-radius: 8px;
        padding: 24px 8px 0 8px;
        box-sizing: border-box;
        // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);


        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 16px;

            overflow-y: auto;
            // padding: 0 8px 16px 8px;
            box-sizing: border-box;
            height: calc(100vh - 230px);

        }

        .competitor-card {
            background: #F7F8FC;
            border-radius: 12px 12px 12px 12px;
            padding: 12px 24px;

            .card-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 8px;

                .ops {
                    display: flex;
                    gap: 12px;
                    cursor: pointer;
                }
            }

            .competitor-name {
                font-weight: 600;
                font-size: 14px;
                color: #262626;
                line-height: 22px;
            }

            .card-row {
                display: flex;
                align-items: flex-start;
                gap: 12px;
                margin-top: 8px;

                .label {
                    width: 70px;
                    font-weight: 400;
                    font-size: 12px;
                    color: #757575;
                    line-height: 18px;
                }

                .value {
                    font-weight: 400;
                    font-size: 12px;
                    color: #292929;
                    line-height: 18px;
                }
            }
        }
    }
}
</style>
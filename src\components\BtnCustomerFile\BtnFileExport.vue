<template>
    <div class="customer_btn_file" v-if="hasExportAccess">
        <el-button type="default" @click="onExport">导出</el-button>
    </div>
</template>

<script setup>
import { exportCustomer } from "@/js/api";
import { exportCustomerList } from "@/app_client/tools/api.js"

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    },
    param: {
        type: Object,
        required: false,
    },
    team: {
        type: Boolean,
        required: false,
        default: false
    }
});

const refDiaEdit = ref();
const emit = defineEmits(['reload']);
const pageAccess = ref({})
const hasExportAccess = ref(false);

const showEdit = (row) => {
    refDiaEdit.value.show(row);
}

const setAccess = (access) => {
    pageAccess.value = access;
    if (props.isAdmin) {
        hasExportAccess.value = pageAccess.value.customer_export_opr;
    }
}

const _onExportAdminCustomer = () => {
    const data = {
        "customerTypeId": "",
        "createType": 0,
    }
    data.filename = '客户列表_' + new Date().getTime();
    exportCustomer(data)
}

const onExportUserCustomer = () => {
    const data = {};
    data.filename = '客户管理_' + new Date().getTime();
    exportCustomerList(props.team, data)
}


const onExport = (cmd) => {
    if (props.isAdmin) {
        _onExportAdminCustomer()
    } else {
        onExportUserCustomer()
    }
}

const checkAccess = () => {
    g.cacheStore.getApiCustomerConfig().then(res => {
        // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
        hasExportAccess.value = res.permissions.includes(2);
    });
};

onMounted(() => {
    if (!props.isAdmin) {
        checkAccess();
    }
});


defineExpose({ onExport, refDiaEdit, showEdit, setAccess })
</script>

<style lang="scss">
.customer_btn_file {

    .el-dropdown-link {

        .vbb_title {
            margin-top: 3px;
        }

        .el-icon {
            margin-top: 3px;
        }
    }

    .el-dropdown {
        height: 18px;
        padding: 6px 12px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
        border-radius: 4px;

        .el-dropdown-item {
            width: 300px;
        }
    }

    .vbp_title {
        margin-bottom: 12px;

        span {
            color: red;
            margin-left: 4px;
        }
    }
}
</style>
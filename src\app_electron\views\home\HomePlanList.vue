<template>
  <div class="plan-list-container custom-scrollbar">
    <WarningTag v-if="!isNetworkConnected" hint="网络连接中断，请在网络恢复后重试。" />
    <HoldingPlan ref="refHoldingPlan" @callback="cbHoldingPlan" />
    <div v-if="plans.length > 0">
      <planInfoTitle title="计划沟通" img="e_plan_sche.png" />
      <div v-for="(plans, date, index) in groupedPlans" :key="date" class="plan-group">
        <div class="plan-date">
          <div class="date-icon">
            <img :src="getAssetUrl('inmeet_16.svg')" alt="" />
          </div>
          <div>{{ formatDate(date, "MM月dd日") }}</div>
          <div class="date-note">{{ dateNote(date) }}</div>
        </div>
        <div v-for="item in plans" :key="item.scheduleId" class="plan-item flex-col">
          <div class="plan-header">
            <div class="plan-title-wrapper">
              <span class="plan-title" :title="item.subject">{{ item.subject }}</span>
              <span v-if="item.notMe" class="visit-tag">协访</span>
            </div>
            <div class="plan-actions flex-row">
              <div class="more-actions">
                <el-dropdown trigger="hover" @command="(cmd) => handleCommand(cmd, item)">
                  <img :src="getAssetUrl('inmeet_7.svg')" alt="more" @click="onClickMore(item)" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="detail">查看详情</el-dropdown-item>
                      <el-dropdown-item command="prepare">访前准备</el-dropdown-item>
                      <div v-if="!item.launched && item.status != 'ongoing' && !item.notMe">
                        <el-dropdown-item command="edit">修改沟通</el-dropdown-item>
                        <el-dropdown-item command="delete">取消沟通</el-dropdown-item>
                      </div>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
              <el-button type="primary" class="start_button" @click="startPlan(item)" v-if="item.showBtnStart"
                size="small" :disabled="item.status == 'ongoing'" id="s1">开始</el-button>
              <el-tooltip class="box-item" effect="dark" content="仅沟通创建人可操作" v-if="item.notMe">
                <el-button type="primary" class="start_button" :disabled="true" size="small" id="s3">开始</el-button>
              </el-tooltip>
              <el-button type="primary" class="start_button" @click="handleCommand('detail', item)"
                v-if="item.showBtnDetail" size="small">详情</el-button>
            </div>
          </div>
          <div class="plan-info">
            <div :class="`time-wrapper ${!item.inSameDay ? 'have_plus' : ''}`">
              <span>{{ item.startDt }} - {{ item.endDt }}</span>
              <span v-if="!item.inSameDay" class="plus-one">+1</span>
            </div>
            <div class="line"></div>
            <span class="customer-name" :title="item.salesMateCustomerName">{{
              item.salesMateCustomerName
            }}</span>
          </div>
          <div class="plan-status">
            <span :class="`status ${item.status}`">{{ getLang(item.status) }}</span>
          </div>
        </div>
        <div class="line-item" v-if="index !== Object.keys(groupedPlans).length - 1" />
      </div>
    </div>
    <div v-else-if="!hasHoldingPlan" class="no_data flex-center">
      <el-empty :image="getAssetUrl('inmeet_5.svg')" description="暂无沟通" @click="reload" />
    </div>
  </div>
</template>

<script setup>
import { getAssetUrl, formatDate, getDay06, nDate, now, groupPlansByDate } from "@/js/utils";
import WarningTag from '@/app_electron/components/WarningTag.vue'
import { getScheduleList } from "@/js/api.js";
import { MoreFilled } from "@element-plus/icons-vue";
import getLang from "@/js/lang.js";
import planInfoTitle from "./planInfoTitle.vue"
import HoldingPlan from "./HoldingPlan.vue";
import { computed } from "@vue/reactivity";

const plans = ref([]);
const groupedPlans = ref({});
const showActions = ref({});
const emit = defineEmits(['callback'])
const refHoldingPlan = ref(null);
const hasHoldingPlan = ref(false);
const holdingPlan = ref({})
const isNetworkConnected = computed(() => g.electronStore.isNetworkConnected)
let lastRespData = ''

const cbHoldingPlan = (holding) => {
  holdingPlan.value = holding;
  hasHoldingPlan.value = holding.duration > -1;
  reloadPlanList()
}

const onClickMore = (plan) => {
  showActions.value[plan.scheduleId] = true;
};

const handleCommand = (command, plan) => {
  plan = toRaw(plan);
  if (command === "edit") {
    g.electronStore.openWin("arrange_visit", plan);
  } else if (command === "delete") {
    g.planStore.cancelPlan(plan.scheduleId).then((res) => {
      if (res) {
        reload();
      }
    });
  } else if (command === "detail") {
    g.electronStore.openWin("visit_detail", plan);
  } else if (command === "prepare") {
    g.electronStore.openUrl(`/prepare/${plan.scheduleId}`);
  } else if (command === "review") {
    g.clientStore.viewPlanRecord(plan);
  }
};

// 今天，昨天，明天，后天，其它情况下，不显示
const dateNote = (date) => {
  const now = new Date();
  const diffTime = now - new Date(date);
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 1) {
    return "昨天";
  } else if (diffDays === 0) {
    return "今天";
  } else if (diffDays === -1) {
    return "明天";
  } else if (diffDays === -2) {
    return "后天";
  }
  return "";
};

const startPlan = (plan) => {
  if (g.meetStore.isLocalMeeting) {
    ElMessage({
      message: "当前有正在进行的沟通，无法开始新的沟通",
      type: 'warning'
    });
    return
  }
  g.electronStore.openWin("meet", toRaw(plan));
};

const reload = () => {
  refHoldingPlan.value.reload()
}

const reloadPlanList = () => {
  refHoldingPlan.value.reload()
  const [_startOfWeek, _endOfWeek] = getDay06(new Date());
  const dateParam = {
    startTime: now("yyyy-MM-dd"),
    endTime: nDate(30),
    showAssistMeeting: true,
    completed: false,
  };
  getScheduleList(dateParam).then((res) => {
    // console.log('getScheduleList', new Date(), res);
    emit('callback', 'reload', '')
    if (res.code == 0) {
      const newRespData = JSON.stringify(res.data.datas)
      if (lastRespData !== newRespData) {
        let planList = res.data.datas.filter(x => !x.inProgress && !x.launched);
        if (hasHoldingPlan.value) {
          planList = planList.filter(x => {
            if (!x?.conferenceIds || !holdingPlan.value?.conferenceId) {
              return true
            }
            return x.conferenceIds.indexOf(holdingPlan.value.conferenceId) == -1;
          })
        }
        plans.value = planList;
        groupedPlans.value = groupPlansByDate(planList);
        showActions.value = plans.value.map((x) => ({
          id: x.scheduleId,
          show: false,
        }));
        lastRespData = newRespData
      }
    }
  });
};

defineExpose({
  plans,
  groupedPlans,
  MoreFilled,
  getAssetUrl,
  getLang,
  reload,
});
</script>

<style lang="scss">
.mac-content {
  .plan-list-container {
    height: calc(100vh - 272px);
  }
}

.win-content {
  .plan-list-container {
    height: calc(100vh - 230px);
  }
}

.plan-list-container {
  padding: 15px;
  background: #ffffff;
  border-radius: 24px 24px 0px 0px;
  overflow-x: hidden;

  .warning_tag_wrap {
    margin: 5px;
  }

  .plan-group {
    margin-bottom: 24px;

    .plan-date {
      display: flex;
      align-items: baseline;
      // margin-bottom: 15px;
      padding-left: 4px;
      box-sizing: border-box;

      .date-icon {
        width: 16px;
        height: 16px;
        // margin-right: 8px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      div {
        font-size: 12px;
        margin: 0 4px;
        color: #595959;
      }
    }

    .plan-item {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin: 15px 0;
      padding: 0 10px;
      border-radius: 8px;

      .plan-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        height: 24px;
        width: 100%;

        .plan-title-wrapper {
          display: flex;
          align-items: center;
          gap: 8px;
          max-width: 280px;
          overflow: hidden;
        }

        .plan-title {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
          max-width: 180px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
        }

        .visit-tag {
          padding: 0 4px;
          height: 18px;
          line-height: 18px;
          background: #FFF7E6;
          border-radius: 2px;
          font-size: 12px;
          color: #FF7300;
          font-weight: normal;
          flex-shrink: 0;
        }
      }

      .plan-actions {
        display: flex;
        width: 92px;
        position: relative;
        align-items: center;
        justify-content: end;

        .more-actions {
          width: 24px;
          height: 24px;
          cursor: pointer;
          margin-right: 8px;
        }

        .start_button {
          width: 58px;
          height: 30px;
          background: #436BFF;
          border-radius: 5px;
          font-weight: 400;
          cursor: pointer;

          &:hover {
            background-color: #6B90FF;
          }

          &:active {
            background-color: #2E4DD9;
          }
        }
      }

      .plan-info {
        display: flex;
        justify-content: space-between;
        font-size: 12px;
        color: #8c8c8c;
        line-height: 20px;

        .time-wrapper {
          position: relative;
          display: inline-flex;
          align-items: flex-start;

          .plus-one {
            position: absolute;
            top: -3px;
            right: -16px;
            font-size: 10px;
            color: #ff5219;
            font-weight: 500;
          }
        }

        .have_plus {
          margin-right: 12px;
        }

        .line {
          width: 1px;
          height: 12px;
          background-color: #bfbfbf;
          margin: 3px 8px;
        }

        .customer-name {
          font-size: 12px;
          color: #8c8c8c;
          width: 179px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          max-width: 108px;
        }
      }

      .plan-status {
        margin-top: 4px;

        .status {
          padding: 2px 0;
          font-size: 12px;
          border-radius: 4px;
        }

        .ongoing {
          color: #26d08e;
        }

        .notStarted {
          color: #ff5219;
        }
      }
    }

    .line-item {
      width: 100%;
      height: 1px;
      background-color: #E9E9E9;
      margin: 0;
    }
  }

  .no_data {
    width: 100%;
    margin-top: 40px;
  }
}

.el-message {
  width: 90%;
}

.el-tooltip {
  width: 80%;
}
</style>

<template>
    <div class="category-tabs" v-if="categoryData.length > 1">
        <div class="tab-item" :class="{ active: activeTab === item.key }" @click="handleTabClick(item.key)"
            v-for="item in categoryData" :key="item.key">
            <span class="tab-text">{{ item.label }} ({{ item.count }})</span>
            <div class="tab-line" v-if="activeTab === item.key"></div>
        </div>
        <div class="bottom-line"></div>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { getOpportunityCategoryData } from '@/app_client/api/opportunity.js';

// 定义 props
const props = defineProps({
    defaultActive: {
        type: String,
        default: 'new'
    }
});

// 定义 emits
const emit = defineEmits(['tab-change']);

// 分类数据
const categoryData = ref([]);
const loading = ref(false);

// 当前激活的标签
const activeTab = ref(props.defaultActive);

// 加载分类数据
const loadCategoryData = async () => {
    try {
        loading.value = true;
        const response = await getOpportunityCategoryData();
        if (response.code === 0) {
            categoryData.value = response.data;
            // 如果数据加载成功且没有默认选中项，则选中第一个
            if (categoryData.value.length > 0 && !categoryData.value.find(item => item.key === activeTab.value)) {
                activeTab.value = categoryData.value[0].key;
            }
        }
    } catch (error) {
        console.error('加载分类数据失败:', error);
    } finally {
        loading.value = false;
    }
};

// 处理标签点击
const handleTabClick = (tab) => {
    if (activeTab.value !== tab) {
        activeTab.value = tab;
        emit('tab-change', tab);
    }
};

// 组件挂载时加载数据
onMounted(() => {
    loadCategoryData();
});
</script>

<style lang="scss" scoped>
.category-tabs {
    display: flex;
    position: relative;
    margin-bottom: 16px;
    gap: 40px;
    border-bottom: 1px solid #E5E5E5;

    .tab-item {
        text-align: center;
        padding: 0 0 12px 0;
        cursor: pointer;
        position: relative;

        .tab-text {
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: color 0.3s ease;
        }

        .tab-line {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background-color: var(--el-color-primary);
            border-radius: 1px;
        }

        &.active {
            .tab-text {
                color: var(--el-color-primary);
            }
        }

        &:hover {
            .tab-text {
                color: var(--el-color-primary);
            }
        }
    }

    .bottom-line {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 1px;
        background-color: #e8e8e8;
        z-index: -1;
    }
}
</style>

<template>
    <div>
        <!-- 选择框 -->
        <div class="drawer_selecter_input" @click="onClick">
            <div v-if="selectedItems.length > 0" class="selected_tags">
                <el-tag v-for="item in selectedItems.slice(0, config.maxTagCount)" :key="item[config.column_value]"
                    closable type="info" @close="onClose(item)">
                    <el-tooltip effect="dark" :content="item[config.column_label]" :show-after="500">
                        {{ item[config.column_label] }}
                    </el-tooltip>
                </el-tag>
                <el-tag v-if="selectedItems.length > config.maxTagCount" type="info">
                    +{{ selectedItems.length - config.maxTagCount }}
                </el-tag>
            </div>
            <div class="input_hint" v-else>
                {{ hint }}
            </div>
            <div class="input_icon">
                <el-icon v-if="selectedItems.length > 0" @click="onReset">
                    <CircleClose />
                </el-icon>
                <el-icon v-else>
                    <MoreFilled />
                </el-icon>
            </div>
        </div>

        <!-- Drawer -->
        <el-drawer v-model="isShow" direction="rtl" class="drawer_selecter_wrap" append-to-body>
            <template #header>
                <div class="drawer_title">
                    {{ title }}
                </div>
            </template>
            <template #default>
                <div class="drawer_body">
                    <!-- 左侧表格 -->
                    <div class="drawer_left">
                        <div class="search_box">
                            <el-input v-model="searchKey" :placeholder="config.search_placeholder || '请输入'"
                                class="search_input" @keyup.enter="search" clearable :prefix-icon="Search"
                                @clear="search">
                            </el-input>
                        </div>
                        <div class="table_box">
                            <el-table :data="tableData" ref="refTable" header-cell-class-name="table-header"
                                table-layout="auto" :highlight-current-row="true" v-loading="loading"
                                @selection-change="handleSelectionChange" border>
                                <el-table-column type="selection" width="30" :selectable="checkSelectable" />
                                <el-table-column v-for="col in columns" :key="col.prop" :prop="col.prop"
                                    :label="col.label" alignment="center" :width="col.width">
                                    <template #default="scope">
                                        <div v-if="col.template">
                                            <slot :name="'col_' + col.prop" :row="scope.row" />
                                        </div>
                                        <div v-else>
                                            {{ scope.row[col.prop] }}
                                        </div>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页组件 -->
                            <div class="pagination_box" v-if="config.show_pager && cfg.pageTotal > cfg.param.pageSize">
                                <el-pagination background layout="total, sizes, prev, pager, next, jumper"
                                    :current-page="cfg.param.pageNumber" :page-size="cfg.param.pageSize"
                                    :page-sizes="config.pageSizes || cfg.pageSizes" :total="cfg.pageTotal"
                                    @current-change="handlePageChange" @size-change="handleSizeChange">
                                </el-pagination>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧已选择信息 -->
                    <div class="drawer_right">
                        <div class="right_main">
                            <div class="section_header">
                                <div class="section_title">已选: {{ selectedItems.length }}/{{ config.maxSelect }}</div>
                                <div class="section_btn" @click="onClearAll">清空</div>
                            </div>
                            <div class="tag_list">
                                <el-tag v-for="item in selectedItems" :key="item[config.column_value]"
                                    @close="handleItemClose(item)" closable type="info">
                                    {{ item[config.column_label] }}
                                </el-tag>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template #footer>
                <div style="flex: auto">
                    <el-button @click="onCancel">取消</el-button>
                    <el-button type="primary" @click="onConfirm"
                        :disabled="!(config.maxSelect == -1 || selectedItems.length <= config.maxSelect)">确定</el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>

<script setup>
import { MoreFilled, CircleClose, Search } from "@element-plus/icons-vue"

const { proxy } = getCurrentInstance()

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    config: {
        type: Object,
        required: true,
        default: () => ({
            urlGet: null,           // 获取数据的API
            column_value: 'id',     // 值字段名
            column_label: 'name',   // 标签字段名
            maxTagCount: 1,         // 最大显示标签数
            maxSelect: -1,          // 最大选择数量，-1表示无限制
            title: '选择',          // drawer标题
            columns: [],            // 表格列配置
            search_placeholder: '请输入', // 搜索框占位符
            show_pager: true,       // 是否显示分页
            pageSizes: [10, 20, 50, 100] // 分页大小选项
        })
    }
})

const emit = defineEmits(['callback', 'update:modelValue'])

// 响应式数据
const isShow = ref(false)
const selectedItems = ref([...props.modelValue])
const tableData = ref([])
const loading = ref(false)
const searchKey = ref('')
const refTable = ref()

// 分页配置
const cfg = reactive({
    param: {
        searchKey: '',
        pageSize: 20,
        pageNumber: 1
    },
    pageTotal: 0,
    pageSizes: [10, 20, 50, 100]
})

// 计算属性
const hint = computed(() => {
    if (selectedItems.value.length > 0) {
        return `已选${selectedItems.value.length}项`
    }
    return '请选择'
})

const title = computed(() => props.config.title || '选择')

const columns = computed(() => props.config.columns || [])

// 监听modelValue变化
watch(() => props.modelValue, (newValue) => {
    selectedItems.value = [...newValue]
}, { deep: true })

// 方法
const onClick = () => {
    isShow.value = true
    // 确保在drawer打开时，selectedItems状态正确
    nextTick(() => {
        search()
    })
}

const onClose = (item) => {
    selectedItems.value = selectedItems.value.filter(x => x[props.config.column_value] !== item[props.config.column_value])
    emit('update:modelValue', selectedItems.value)
    _emitData()
    updateHint()
}

const onReset = (event) => {
    event.preventDefault()
    event.stopPropagation()
    reset()
}

const reset = (need_emit = true) => {
    selectedItems.value = []
    emit('update:modelValue', selectedItems.value)
    updateHint()
    if (need_emit) {
        emit('callback', 'reset', [])
    }
}

const search = () => {
    if (!props.config.urlGet) {
        console.warn('urlGet is required')
        return
    }

    loading.value = true
    // 搜索时重置到第一页
    if (searchKey.value !== cfg.param.searchKey) {
        cfg.param.pageNumber = 1
        cfg.param.searchKey = searchKey.value
    }

    const params = {
        searchKey: searchKey.value,
        pageSize: cfg.param.pageSize,
        pageNumber: cfg.param.pageNumber
    }

    props.config.urlGet(params)
        .then((resp) => {
            if (resp.code == 0) {
                if (resp.data.datas) {
                    tableData.value = resp.data.datas
                    cfg.pageTotal = resp.data.totalNum || 0
                } else if (resp.data) {
                    tableData.value = Array.isArray(resp.data) ? resp.data : [resp.data]
                    cfg.pageTotal = tableData.value.length
                } else {
                    tableData.value = []
                    cfg.pageTotal = 0
                }
                // 设置已选中项 - 延迟执行确保表格已渲染
                nextTick(() => {
                    console.log('selectedItems.value', selectedItems.value)
                    if (selectedItems.value.length > 0) {
                        const selectedIds = selectedItems.value.map(item => item[props.config.column_value])
                        console.log('selectedIds', selectedIds)
                        manualCheck(selectedIds)
                    }
                    // 强制更新视图，确保右侧已选择列表正确显示
                    proxy.$forceUpdate()
                })
            } else {
                ElMessage.error(`获取数据失败`)
            }
        })
        .catch((error) => {
            console.log("err search table", error)
            ElMessage.error(`获取数据失败`)
        })
        .finally(() => {
            loading.value = false
        })
}

const handleSelectionChange = (selection) => {
    console.log('handleSelectionChange', selection, selectedItems.value)
    if (props.config.maxSelect === 1) {
        // 单选模式
        selectedItems.value = selection.length > 0 ? [selection[selection.length - 1]] : []
    } else {
        // 多选模式 - 更新当前页的选择状态
        updateSelectionFromCurrentPage(selection)
    }
    emit('update:modelValue', selectedItems.value)
    updateHint()
}

// 更新当前页的选择状态
const updateSelectionFromCurrentPage = (currentPageSelection) => {
    console.log('updateSelectionFromCurrentPage', currentPageSelection)
    const currentPageIds = currentPageSelection.map(item => item[props.config.column_value])

    // 移除当前页中已取消选择的项目
    selectedItems.value = selectedItems.value.filter(item => {
        const isInCurrentPage = tableData.value.some(row => row[props.config.column_value] === item[props.config.column_value])
        if (isInCurrentPage) {
            // 如果在当前页，检查是否还在选择中
            return currentPageIds.includes(item[props.config.column_value])
        }
        // 如果不在当前页，保留
        return true
    })

    // 添加当前页中新选择的项目
    currentPageSelection.forEach(item => {
        const exists = selectedItems.value.some(selected => selected[props.config.column_value] === item[props.config.column_value])
        if (!exists) {
            selectedItems.value.push(item)
        }
    })

    // 触发v-model更新
    emit('update:modelValue', selectedItems.value)
}

const handleItemClose = (item) => {
    console.log('handleItemClose', item)
    selectedItems.value = selectedItems.value.filter(x => x[props.config.column_value] !== item[props.config.column_value])
    emit('update:modelValue', selectedItems.value)
    // 更新表格选中状态
    if (refTable.value) {
        const selectedIds = selectedItems.value.map(item => item[props.config.column_value])
        manualCheck(selectedIds)
    }
    updateHint()
}

const onClearAll = () => {
    console.log('onClearAll')
    selectedItems.value = []
    emit('update:modelValue', selectedItems.value)
    if (refTable.value) {
        refTable.value.clearSelection()
    }
    updateHint()
}

const onCancel = () => {
    isShow.value = false
    emit('callback', 'cancel', [])
}

const onConfirm = () => {
    isShow.value = false
    _emitData()
    updateHint()
}

const _emitData = () => {
    const result = toRaw(selectedItems.value)
    emit('callback', 'confirm', result)
}

const manualCheck = (ids) => {
    console.log('manualCheck', ids)
    if (!refTable.value) {
        console.log('manualCheck no refTable.value')
        return
    }

    // 先清除当前页的所有选择
    refTable.value.clearSelection()

    // 然后设置当前页中已选择的行
    for (let i = 0; i < tableData.value.length; i++) {
        const row = tableData.value[i]
        const selected = ids.includes(row[props.config.column_value])
        if (selected) {
            refTable.value.toggleRowSelection(row, true)
        }
    }
}

const checkSelectable = (row) => {
    return true // 可以根据需要添加选择条件
}

const updateHint = () => {
    // 更新提示文本的逻辑已在computed中处理
}

// 分页事件处理
const handlePageChange = (val) => {
    cfg.param.pageNumber = val
    search()
}

const handleSizeChange = (val) => {
    cfg.param.pageSize = val
    cfg.param.pageNumber = 1
    search()
}

const init = (data = []) => {
    selectedItems.value = data
    emit('update:modelValue', selectedItems.value)
    updateHint()
}

// 暴露方法
defineExpose({
    isShow,
    selectedItems,
    onClick,
    reset,
    init,
    search,
    cfg
})
</script>

<style lang="scss">
.drawer_selecter_input {
    width: 500px;
    height: 32px;
    border-radius: 5px;
    border: 1px solid #e9e9e9;
    padding: 0 10px;
    text-align: right;
    cursor: pointer;
    color: #a8abb2;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;

    .selected_tags {
        .el-tag {
            margin: 0 4px;

            .el-tag__content {
                width: fit-content;
                max-width: 80px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }

    .input_hint {
        width: 80%;
        text-align: left;
    }

    .input_icon {
        font-size: 14px;
        color: #8c8c8c;
    }
}

.drawer_selecter_wrap {
    width: 960px !important;

    .el-drawer__header {
        margin-bottom: 0 !important;
        border-bottom: 1px solid #e9e9e9;

        .drawer_title {
            font-size: 16px;
            font-weight: 500;
        }
    }

    .el-drawer__body {
        padding: 0;

        .drawer_body {
            height: calc(100vh - 133px);
            display: flex;

            .drawer_left {
                width: 70%;
                border-right: 1px solid #e9e9e9;
                display: flex;
                flex-direction: column;

                .search_box {
                    padding: 20px;
                    border-bottom: 1px solid #e9e9e9;

                    .search_input {
                        width: 100%;
                    }
                }

                .table_box {
                    flex: 1;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;

                    .el-table {
                        flex: 1;
                    }

                    .pagination_box {
                        padding: 10px 20px;
                        border-top: 1px solid #e9e9e9;
                        background-color: #fff;
                        display: flex;
                        justify-content: center;
                    }
                }
            }

            .drawer_right {
                width: 30%;
                padding: 20px;

                .right_main {
                    height: calc(100vh - 201px);
                    overflow-y: auto;

                    .section_header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin: 10px 0;

                        .section_title {
                            color: #666;
                        }

                        .section_btn {
                            color: #436BFF;
                            cursor: pointer;
                            font-size: 14px;
                        }
                    }

                    .tag_list {
                        margin-bottom: 16px;

                        .el-tag {
                            margin: 4px;
                        }
                    }
                }
            }
        }
    }

    .el-drawer__footer {
        border-top: 1px solid #e9e9e9;
        padding: 20px;
    }
}
</style>

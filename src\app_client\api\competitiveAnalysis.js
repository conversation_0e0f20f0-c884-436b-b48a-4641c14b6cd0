import { getHttp } from "@/js/request.js";

const _http = getHttp();

import {
  getVolumedistributionApi,
  getSentimentDistributionApi,
  getKeywordSentimentApi,
  getCompetitorAIAnalysisApi,
  getEvaluationDistributionApi,
  getCompetitorRadarDataApi,
  getCrCustomerList,
  getCrDimensionList,
} from "@/app_client/tools/api.js";

import { sortNoChildLast } from "@/app_client/tools/utils.js";

const colorObject = {
  0: "#04CCA4",
  2: "#FF6B3B",
  1: "#D9D9D9",
};

const radarColorList = ["#4B7CF6", "#2ACFCF", "#FFC300", "#FF6F91"];

export const getDimensionList = async (params) => {
  const res = (await _http.post(
    `api/org/${g.appStore.user.orgId}/customer/voice/customers/top/feedbacks/dimension/days/${params.days}`,
    params.data
  )) || {};
  return res.data || [];
};

export const getCompetitorRadarData = async (obj) => {
  const res = await getCompetitorRadarDataApi(obj);
  return res?.data || { datas: [], totalNum: 0 };
};

export const getVolumedistribution = async (obj) => {
  const arr = (await getVolumedistributionApi(obj)) || {};
  return (arr.data || []).map((i, index) => {
    return {
      name: i.commonName,
      value: i.volumeDistributions.map((x) => x.volume),
      radarIndicator: i.volumeDistributions.map((x) => ({
        name: x.parentDimensionName,
        max: 10,
        min: 0,
      })),
      color: radarColorList[index],
      cardColor:
        i.totalVolume == 0
          ? colorObject[1]
          : i.totalVolume > 0
            ? colorObject[0]
            : colorObject[2],
      ...i,
    };
  });
};
export const getSentimentDistribution = async (obj) => {
  const res = (await getSentimentDistributionApi(obj)) || {};
  return (res.data || []).map((i) => {
    return {
      name: i.parentDimensionName,
      ...i,
    };
  });
};

export const getKeywordSentiment = async (obj) => {
  const res = (await getKeywordSentimentApi(obj)) || {};
  return (res.data || []).map((i) => {
    return {
      name: i.dimensionName,
      value: i.volume,
      color:
        i.attitudeSumType == 0
          ? colorObject[1]
          : i.attitudeSumType > 0
            ? colorObject[0]
            : colorObject[2],
      valueList: [
        { name: "积极", value: i.positiveCount, color: "#04CCA4" },
        { name: "消极", value: i.negativeCount, color: "#FF6B3B" },
        { name: "中性", value: i.neutralCount, color: "#D9D9D9" },
      ],
      ...i,
    };
  });
};

export const getCompetitorAIAnalysis = async (obj) => {
  const res = await getCompetitorAIAnalysisApi(obj);
  if (res.code == 0) {
    let data = res.data;

    // Convert data to markdown format
    let markdown = "";

    if (data && typeof data === "object") {
      let i = 0;
      // Iterate through each top-level key in the data
      Object.keys(data).forEach((sectionKey) => {
        const sectionData = data[sectionKey];

        // Add section heading
        markdown += `## ${sectionKey}\n\n`;

        if (Array.isArray(sectionData)) {
          // Process array of items
          sectionData.forEach((item, index) => {
            // Find the title field (contains '名称' but not '竞品名称')
            const titleField = Object.keys(item).find(
              (key) =>
                (key.includes("名称") ||
                  key.includes("标题") ||
                  key.includes("title") ||
                  key.includes("name")) &&
                key !== "竞品名称"
            );
            // Sort fields: other fields first, then '竞品名称' last
            const allKeys = Object.keys(item);
            const otherKeys = allKeys.filter(
              (key) => key !== titleField && key !== "竞品名称"
            );
            const competitorKey = allKeys.find((key) => key === "竞品名称");
            const title = titleField ? item[titleField] : `项目${index + 1}`;
            markdown += `### ${title}`;
            // Add competitor name at the end if it exists
            if (competitorKey) {
              markdown += `(${item[competitorKey]})`;
            }
            markdown += "\n";
            // Add other fields as bullet points
            otherKeys.forEach((key) => {
              markdown += `- ${key}: ${item[key]}\n`;
            });

            markdown += "\n";
          });
          if (i == 1) {
            markdown += "-------\n";
          }
          i++;
        }
      });
    }

    return markdown;
  } else {
    return "";
  }
};

export const getEvaluationDistribution = async (obj) => {
  const res = (await getEvaluationDistributionApi(obj)) || {};
  return (res.data || []).map((i) => {
    return {
      name: i.commonName,
      ...i,
    };
  });
};

export const getCrCustomerListApi = async (obj) => {
  if (obj.competitorId) {
    const res = (await getCrCustomerList(obj)) || {};
    return res.data || [];
  } else {
    return [];
  }
};

export const getCrDimensionListApi = async (competitorId, periodType, data) => {
  const res = await getCrDimensionList(competitorId, periodType, data);
  let list = res.data || [];
  return sortNoChildLast(list);
};

<template>
  <div class="page-settings-wrap">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #col_fieldName="{ row }">
        <span>{{ row.fieldName }}</span>
      </template>
      <template #col_fieldType="{ row }">
        <span>{{ row.fieldType }}</span>
      </template>
      <template #col_create="{ row }">
        <el-checkbox v-model="row.create" disabled></el-checkbox>
      </template>
      <template #col_required="{ row }">
        <el-checkbox v-model="row.required" disabled></el-checkbox>
      </template>
      <template #col_table="{ row }">
        <el-checkbox v-model="row.table" disabled></el-checkbox>
      </template>
      <template #col_filter="{ row }">
        <el-checkbox v-model="row.filter" disabled></el-checkbox>
      </template>
      <template #col_search="{ row }">
        <el-checkbox v-model="row.search" disabled></el-checkbox>
      </template>
      <template #col_detail="{ row }">
        <el-checkbox v-model="row.detail" disabled></el-checkbox>
      </template>
    </MyTable>
  </div>
</template>

<script setup>
import MyTable from '@/components/Table.vue';
import { ref, reactive } from 'vue';
import { getPageSettingsListData } from '@/app_admin/api/userManage.js';

const refTable = ref();

const tableConfig = reactive({
  tableid: 'page_settings_list',
  param: { searchKey: '' },
  need_init_load: true,
  show_search: false,
  need_header: false,
  show_btn_add: false,
  form: {},
  columns: [
    'fieldName',
    'fieldType',
    'create',
    'required',
    'table',
    'filter',
    'search',
    'detail',
  ],
  template: [
    'fieldName',
    'fieldType',
    'create',
    'required',
    'table',
    'filter',
    'search',
    'detail',
  ],
  urlGet: getPageSettingsListData,
});

const handleTableCallback = (action, data) => { };
</script>

<style lang="scss" scoped>
.page-settings-wrap {
  padding: 24px;
}
</style>
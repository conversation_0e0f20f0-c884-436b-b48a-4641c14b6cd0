<template>
    <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="task-model-form-drawer"
        :close-on-click-modal="false">
        <template #header>
            <div class="drawer-header">
                <span class="drawer-title">编辑</span>
            </div>
        </template>

        <div class="drawer-content">
            <el-form ref="formRef" :model="modelForm" :rules="rules" label-position="top"
                require-asterisk-position="right">
                <div style="margin-bottom: 24px;">
                    <el-form-item label="模型名称" prop="name">
                        <el-input v-model="modelForm.name" show-word-limit maxlength="50" />
                    </el-form-item>
                </div>

                <div class="form-section">
                    <el-form-item label="关联任务项">
                        <div v-sortable @end.prevent="handleDragEnd" class="behavior-list">
                            <div v-for="(behaviorText, index) in itemsArrList" :key="index" class="behavior-item">
                                <div class="drag-handle">
                                    <img style="width: 16px; height: 16px;" :src="getOssUrl('icon-sort.png', 3)" alt="">
                                    <p class="behavior-text single-line-ellipsis ">{{ behaviorText.label }}</p>
                                    <el-icon>
                                        <Delete @click="removeBehavior(index)" />
                                    </el-icon>
                                </div>
                            </div>
                        </div>
                    </el-form-item>

                </div>
                <LxMultiButtonSelect v-model="modelForm.itemsArr" :options="options" :max="10" :min="1">
                    <template #label="{ toggleDropdown }">
                        <div class="add-behavior-section">
                            <el-button class="add-behavior-btn" text @click="toggleDropdown">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                                <span>添加关联任务项</span>
                            </el-button>
                        </div>
                    </template>
                </LxMultiButtonSelect>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
                <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import LxMultiButtonSelect from '@/components/LxMultiButtonSelect/index.js'
import { getOssUrl } from "@/js/utils"

const props = defineProps({
    dimensionOptions: {
        type: Array,
        default: () => ([])
    }
})
const emit = defineEmits(['success', 'close'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

// 能力选项数据
const options = computed(() => {
    if (!props.dimensionOptions) {
        return []
    }
    return props.dimensionOptions.map(item => ({
        label: item.name,
        value: item.id
    }))
})

const itemsArrList = computed(() => {
    return [...modelForm.itemsArr].map(v => options.value.find(item => item.value == v))
})

const modelForm = reactive({
    name: '',
    itemsArr: []
})

// 处理能力选择确认
const handleAbilityConfirm = (selectedValues) => {
    console.log('选中的能力:', selectedValues)
    // 这里可以添加其他确认后的处理逻辑
}

const rules = {
    name: [
        { required: true, message: '请输入模型名称', trigger: 'blur' },
        { max: 50, message: '最多输入50个字符', trigger: 'blur' }
    ],
    itemsArr: [
        { required: true, message: '请选择关联任务项', trigger: 'change' },
    ]
}

const openDrawer = (data = null) => {
    drawerVisible.value = true
    modelForm.itemsArr = []
    itemsArrList.value = []
    modelForm.items = []

    if (data) {
        // 编辑模式
        isEditMode.value = true
        currentId.value = data.id
        modelForm.name = data.name
        modelForm.items = [...data.items]
        modelForm.itemsArr = data.items && data.items.length > 0 ? data.items.map(item => item.dimensionId) : []
    } else {
        // 添加模式
        isEditMode.value = false
        currentId.value = null
        modelForm.name = ''
        modelForm.itemsArr = []
    }
}
const removeBehavior = (index) => {
    if (modelForm.itemsArr.length <= 1) {
        ElMessage.warning({ message: '至少保留1个任务项', grouping: true })
        return
    }
    modelForm.itemsArr.splice(index, 1)
}
const handleDragEnd = (evt) => {
    // 交换数组元素
    const movedItem = itemsArrList.value.splice(evt.oldIndex, 1)[0]
    itemsArrList.value.splice(evt.newIndex, 0, movedItem)
}

const closeDrawer = () => {
    drawerVisible.value = false
    formRef.value?.resetFields()
}

const submitForm = () => {
    if (modelForm.itemsArr.length > 10) {
        ElMessage.warning({ message: '最多保留10个任务项', grouping: true })
        return
    }
    if (modelForm.itemsArr.length < 1) {
        ElMessage.warning({ message: '至少保留1个任务项', grouping: true })
        return
    }
    formRef.value.validate((valid) => {
        if (valid) {
            // 处理表单提交
            const formData = {
                id: currentId.value,
                name: modelForm.name,
                items: isEditMode.value ? itemsArrList.value.map(item => ({
                    dimensionId: item.value,
                    id: modelForm.items.find(i => i.dimensionId === item.value)?.id || ''
                })) : itemsArrList.value.map(item => ({
                    dimensionId: item.value,
                }))
            }

            emit('success', {
                mode: isEditMode.value ? 'edit' : 'add',
                data: formData
            })
            closeDrawer()
        } else {
            ElMessage.error('请检查输入内容')
            return false
        }
    })
}

const handleClose = (done) => {
    closeDrawer()
    done()
    emit('close')
}

// 暴露方法给父组件
defineExpose({
    openDrawer,
    closeDrawer
})
</script>

<style scoped lang="scss">
.task-model-form-drawer {
    p {
        margin: 0;
    }

    :deep(.el-drawer__header) {
        padding: 0;
        margin-bottom: 0;
        border-bottom: 1px solid #f0f0f0;
    }

}

.drawer-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;

    .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }
}

.drawer-content {
    .behavior-list {
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 12px;
    }

    .behavior-item {
        position: relative;
        display: flex;
        align-items: flex-start;
        width: 100%;



        .drag-handle {
            cursor: move;
            padding: 12px;
            display: flex;
            align-items: center;
            color: #909399;
            background: #F9FAFC;
            border-radius: 8px 8px 8px 8px;
            width: 100%;
            gap: 12px;
            margin-bottom: 4px;
            box-sizing: border-box;

            .behavior-text {
                display: flex;
                flex: 1;
                font-weight: 400;
                font-size: 14px;
                color: #262626;
                line-height: 22px;
                text-align: left;
                display: block;
            }
        }


        .char-count {
            position: absolute;
            right: 12px;
            bottom: 12px;
            font-size: 12px;
            color: #999;
            pointer-events: none;
        }

    }

    .add-behavior-btn {
        width: 100%;
        margin-top: 12px;
        background: rgba(255, 255, 255, 0.01);
        border-radius: 8px 8px 8px 8px;
        border: 1px dashed #436BFF;
        text-align: center;
        font-weight: 400;
        font-size: 14px;
        color: #436BFF;
        line-height: 22px;

        .el-icon {
            margin-right: 8px;
            font-size: 16px;
        }
    }

    // overflow-y: auto;
    .form-section-top {
        height: 38px;
        background: #F0F6FF;
        border-radius: 8px 8px 8px 8px;

        display: flex;
        align-items: center;
        padding: 0 16px;
        box-sizing: border-box;
        margin-bottom: 16px;

        img {
            width: 20px;
            height: 20px;
        }

        span {
            margin-left: 8px;
            font-weight: 400;
            font-size: 14px;
            color: #262626;
            line-height: 22px;
        }
    }
}


.drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    button {
        padding: 5px 16px;
        border-radius: 6px;
    }

    .cancel-btn {
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
    }

    .confirm-btn {
        background: #436BFF;
        border-color: #436BFF;
    }
}
</style>
<style lang="scss">
.task-model-form-drawer {
    .el-form-item {
        margin-bottom: 0;
    }
}
</style>
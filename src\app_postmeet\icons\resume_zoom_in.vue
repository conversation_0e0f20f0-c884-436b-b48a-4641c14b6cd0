<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g id="面试助手" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="客户端-简历预览（默认1064）" transform="translate(-508.000000, -54.000000)" fill="currentColor">
                <g id="编组-2" transform="translate(40.000000, 40.000000)">
                    <g id="编组备份" transform="translate(468.000000, 14.000000)">
                        <path
                            d="M10,2.5 C14.1421356,2.5 17.5,5.85786438 17.5,10 C17.5,14.1421356 14.1421356,17.5 10,17.5 C5.85786438,17.5 2.5,14.1421356 2.5,10 C2.5,5.85786438 5.85786438,2.5 10,2.5 Z M10,3.75 C6.54822031,3.75 3.75,6.54822031 3.75,10 C3.75,13.4517797 6.54822031,16.25 10,16.25 C13.4517797,16.25 16.25,13.4517797 16.25,10 C16.25,6.54822031 13.4517797,3.75 10,3.75 Z M12.5,9.375 C12.845178,9.375 13.125,9.65482203 13.125,10 C13.125,10.345178 12.845178,10.625 12.5,10.625 L7.5,10.625 C7.15482203,10.625 6.875,10.345178 6.875,10 C6.875,9.65482203 7.15482203,9.375 7.5,9.375 L12.5,9.375 Z"
                            id="形状结合"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>

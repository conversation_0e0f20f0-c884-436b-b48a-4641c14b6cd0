<template>
  <div class="ability-model-page">
    <el-tabs v-model="activeTab" class="model-tabs">
      <el-tab-pane label="能力项" name="ability">
        <AbilityListTable v-if="activeTab === 'ability'" ref="abilityListRef" />
      </el-tab-pane>
      <el-tab-pane label="能力模型" name="behavior">
        <AbilityModelTable v-if="activeTab === 'behavior'" ref="abilityModelTableRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import AbilityListTable from './components/AbilityListTable.vue';
import AbilityModelTable from './components/AbilityModelTable.vue';

const activeTab = ref('ability');
const abilityListRef = ref();
const abilityModelTableRef = ref();

</script>

<style lang="scss">
.ability-model-page {
  padding: 20px;
  font-size: 14px;
  color: #262626;

  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9 !important;
  }

  .el-drawer__body {
    padding: 24px;
    box-sizing: border-box;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }

}
</style>
<template>
    <svg width="120px" height="120px" viewBox="0 0 120 120" version="1.1">
        <defs>
            <linearGradient x1="22.4221067%" y1="0%" x2="75.6903433%" y2="100%" id="linearGradient-1">
                <stop stop-color="#C9BEFF" offset="0%"></stop>
                <stop stop-color="#8AB9FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
                <stop stop-color="#F7FAFF" stop-opacity="0.82" offset="0%"></stop>
                <stop stop-color="#E2E9FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="15.9532181%" y1="0%" x2="81.7164732%" y2="100%" id="linearGradient-3">
                <stop stop-color="#C9BEFF" offset="0%"></stop>
                <stop stop-color="#8AB9FF" offset="100%"></stop>
            </linearGradient>
            <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-4">
                <stop stop-color="#F7FAFF" stop-opacity="0.82" offset="0%"></stop>
                <stop stop-color="#E2E9FF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="第三期2024-01" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="模版详情_默认" transform="translate(-672.000000, -408.000000)">
                <g id="编组-3" transform="translate(196.000000, 268.000000)">
                    <g id="编组-7" transform="translate(476.000000, 140.000000)">
                        <rect id="矩形" x="0" y="0" width="120" height="120"></rect>
                        <g id="编组-5" transform="translate(15.000000, 6.000000)">
                            <path
                                d="M8,0 L82,0 C86.418278,-8.11624501e-16 90,3.581722 90,8 L90,92 C90,96.418278 86.418278,100 82,100 L8,100 C3.581722,100 5.41083001e-16,96.418278 0,92 L0,8 C-5.41083001e-16,3.581722 3.581722,8.11624501e-16 8,0 Z"
                                id="矩形" fill="url(#linearGradient-1)" opacity="0.616843669"></path>
                            <circle id="椭圆形" stroke="#F2F6FF" stroke-width="0.4" fill="url(#linearGradient-2)" cx="80"
                                cy="91" r="18.8"></circle>
                            <path
                                d="M80,83 C81.1045695,83 82,83.8954305 82,85 L81.999,88.999 L86,89 C87.1045695,89 88,89.8954305 88,91 C88,92.1045695 87.1045695,93 86,93 L81.999,92.999 L82,97 C82,98.1045695 81.1045695,99 80,99 C78.8954305,99 78,98.1045695 78,97 L77.999,92.999 L74,93 C72.8954305,93 72,92.1045695 72,91 C72,89.8954305 72.8954305,89 74,89 L77.999,88.999 L78,85 C78,83.8954305 78.8954305,83 80,83 Z"
                                id="形状结合" fill="url(#linearGradient-3)" opacity="0.616843669"></path>
                            <path
                                d="M23,38.2 C23.7731986,38.2 24.4731986,38.5134007 24.979899,39.020101 C25.4865993,39.5268014 25.8,40.2268014 25.8,41 L25.8,43 C25.8,43.7731986 25.4865993,44.4731986 24.979899,44.979899 C24.4731986,45.4865993 23.7731986,45.8 23,45.8 L21,45.8 C20.2268014,45.8 19.5268014,45.4865993 19.020101,44.979899 C18.5134007,44.4731986 18.2,43.7731986 18.2,43 L18.2,41 C18.2,40.2268014 18.5134007,39.5268014 19.020101,39.020101 C19.5268014,38.5134007 20.2268014,38.2 21,38.2 L23,38.2 Z M69,38.2 C69.7731986,38.2 70.4731986,38.5134007 70.979899,39.020101 C71.4865993,39.5268014 71.8,40.2268014 71.8,41 L71.8,43 C71.8,43.7731986 71.4865993,44.4731986 70.979899,44.979899 C70.4731986,45.4865993 69.7731986,45.8 69,45.8 L33,45.8 C32.2268014,45.8 31.5268014,45.4865993 31.020101,44.979899 C30.5134007,44.4731986 30.2,43.7731986 30.2,43 L30.2,41 C30.2,40.2268014 30.5134007,39.5268014 31.020101,39.020101 C31.5268014,38.5134007 32.2268014,38.2 33,38.2 L69,38.2 Z"
                                id="形状结合备份-2" stroke="#F2F6FF" stroke-width="0.4" fill="url(#linearGradient-4)"></path>
                            <path
                                d="M23,18.2 C23.7731986,18.2 24.4731986,18.5134007 24.979899,19.020101 C25.4865993,19.5268014 25.8,20.2268014 25.8,21 L25.8,23 C25.8,23.7731986 25.4865993,24.4731986 24.979899,24.979899 C24.4731986,25.4865993 23.7731986,25.8 23,25.8 L21,25.8 C20.2268014,25.8 19.5268014,25.4865993 19.020101,24.979899 C18.5134007,24.4731986 18.2,23.7731986 18.2,23 L18.2,21 C18.2,20.2268014 18.5134007,19.5268014 19.020101,19.020101 C19.5268014,18.5134007 20.2268014,18.2 21,18.2 Z M69,18.2 C69.7731986,18.2 70.4731986,18.5134007 70.979899,19.020101 C71.4865993,19.5268014 71.8,20.2268014 71.8,21 L71.8,23 C71.8,23.7731986 71.4865993,24.4731986 70.979899,24.979899 C70.4731986,25.4865993 69.7731986,25.8 69,25.8 L33,25.8 C32.2268014,25.8 31.5268014,25.4865993 31.020101,24.979899 C30.5134007,24.4731986 30.2,23.7731986 30.2,23 L30.2,21 C30.2,20.2268014 30.5134007,19.5268014 31.020101,19.020101 C31.5268014,18.5134007 32.2268014,18.2 33,18.2 Z"
                                id="形状结合" stroke="#F2F6FF" stroke-width="0.4" fill="url(#linearGradient-4)"></path>
                            <path
                                d="M23,58.2 C23.7731986,58.2 24.4731986,58.5134007 24.979899,59.020101 C25.4865993,59.5268014 25.8,60.2268014 25.8,61 L25.8,63 C25.8,63.7731986 25.4865993,64.4731986 24.979899,64.979899 C24.4731986,65.4865993 23.7731986,65.8 23,65.8 L21,65.8 C20.2268014,65.8 19.5268014,65.4865993 19.020101,64.979899 C18.5134007,64.4731986 18.2,63.7731986 18.2,63 L18.2,61 C18.2,60.2268014 18.5134007,59.5268014 19.020101,59.020101 C19.5268014,58.5134007 20.2268014,58.2 21,58.2 Z M69,58.2 C69.7731986,58.2 70.4731986,58.5134007 70.979899,59.020101 C71.4865993,59.5268014 71.8,60.2268014 71.8,61 L71.8,63 C71.8,63.7731986 71.4865993,64.4731986 70.979899,64.979899 C70.4731986,65.4865993 69.7731986,65.8 69,65.8 L33,65.8 C32.2268014,65.8 31.5268014,65.4865993 31.020101,64.979899 C30.5134007,64.4731986 30.2,63.7731986 30.2,63 L30.2,61 C30.2,60.2268014 30.5134007,59.5268014 31.020101,59.020101 C31.5268014,58.5134007 32.2268014,58.2 33,58.2 Z"
                                id="形状结合备份-3" stroke="#F2F6FF" stroke-width="0.4" fill="url(#linearGradient-4)"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg></template>

<script>
export default {
    name: 'OutlineIcon',
}
</script>
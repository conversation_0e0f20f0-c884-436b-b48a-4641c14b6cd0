<template>
    <div class="dt_left">
        <div class="dt_left_header">
            <el-input v-model="filterText" placeholder="请输入部门名称" class="search_dept" :prefix-icon="Search" clearable>
            </el-input>
        </div>
        <div class="dt_left_main">
            <el-tree :data="treeData" show-checkbox node-key="value" :default-checked-keys="checkedKeys"
                :disabled="data => data.disabled" :props="defaultProps" ref="refTree" :check-strictly="true"
                :filter-node-method="filterNode" @check="onChecked">
                <template #default="{ node, data }">
                    <div class="custom-tree-node flex-row">
                        <div>{{ data.label }}</div>
                        <div class="icon_op">
                            <el-dropdown>
                                <el-icon>
                                    <Setting />
                                </el-icon>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item @click="onCurrNode(node, data)">
                                            {{ `${node.checked ? '取消节点' : '选择节点'}` }}</el-dropdown-item>
                                        <el-dropdown-item @click="onSameLevel(node, data)">
                                            {{ `${node.checked ? '取消选择同级' : '选择同级'}` }}</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </div>
                </template>
            </el-tree>
        </div>
    </div>
</template>

<script setup>
import { Search, Setting } from '@element-plus/icons-vue'
import { extractIds, array_remove, array_merge, getSameLevelIds } from "@/app_admin/tools/utils.js"
import { throttle } from '@/js/utils';

const emit = defineEmits(['update:selectedNodes']);

const props = defineProps({
    selectedNodes: {
        type: Array,
        default: () => []
    },
    mustSubDept: {
        type: Boolean,
        default: false
    }
})

const fullTreeData = ref([])
const treeData = ref([])
const checkedKeys = ref([])
const refTree = ref()
const filterText = ref('')

const defaultProps = {
    children: 'children',
    label: 'label',
}

onMounted(() => {
    g.cacheStore.getDeptTreeData().then(resp => {
        // 初始化节点的disabled状态
        const initDisabledState = (nodes) => {
            for (const node of nodes) {
                node.disabled = false;
                if (node.children) {
                    initDisabledState(node.children);
                }
            }
        };
        initDisabledState(resp);

        fullTreeData.value = resp;
        treeData.value = resp;
        checkedKeys.value = props.selectedNodes.map(x => x.value);

        // 初始化已选节点的子节点disabled状态
        for (const nodeId of checkedKeys.value) {
            const node = findNode(treeData.value, nodeId);
            if (node && node.children && props.mustSubDept) {
                // 获取所有子部门ID
                const allChildIds = getAllChildIds(node);
                // 更新子节点的disabled状态并勾选
                updateNodeDisabled(node.children, node.value, true, true);
                // 将子部门ID添加到选中列表
                checkedKeys.value = array_merge(checkedKeys.value, allChildIds);
            }
        }
        // 更新树的选中状态
        refTree.value?.setCheckedKeys(checkedKeys.value)
    })
})

const filterNode = (value, data) => {
    if (!value) return true
    return data.label.includes(value)
}

const func = () => {
    const choosed = toRaw(refTree.value.getCheckedNodes())
    emit('update:selectedNodes', choosed.map(x => toRaw(x)))
}

const updateSelectedThrottleFunc = throttle(() => { func() }, 500)

const getAllChildIds = (node) => {
    const ids = [];
    const traverse = (n) => {
        if (n.value) ids.push(n.value);
        if (n.children) {
            n.children.forEach(child => traverse(child));
        }
    };
    traverse(node);
    return ids;
};

const findNode = (nodes, id) => {
    for (const node of nodes) {
        if (node.value === id) return node;
        if (node.children) {
            const found = findNode(node.children, id);
            if (found) return found;
        }
    }
    return null;
};

const updateNodeDisabled = (nodes, parentId, disabled, checked = false) => {
    for (const node of nodes) {
        if (props.mustSubDept && parentId) {
            node.disabled = disabled;
            node.checked = checked;
        }
        if (node.children) {
            updateNodeDisabled(node.children, node.value, disabled, checked);
        }
    }
};

const onChecked = (data, node) => {
    const ids = extractIds(toRaw(data));
    const checked = toRaw(node)['checkedKeys'].indexOf(toRaw(data).value) > -1;

    if (checked) {
        if (props.mustSubDept && data.children) {
            const allChildIds = getAllChildIds(toRaw(data));
            _add_ids([data.value, ...allChildIds]);
            // 更新子节点的disabled状态
            updateNodeDisabled(data.children, data.value, true, true);
        } else {
            _add_ids(ids);
        }
    } else {
        if (props.mustSubDept && data.children) {
            const allChildIds = getAllChildIds(toRaw(data));
            _remove_ids([data.value, ...allChildIds]);
            // 更新子节点的disabled状态
            updateNodeDisabled(data.children, data.value, false, false);
        } else {
            _remove_ids(ids);
        }
    }
    updateSelectedThrottleFunc()
}

const _add_ids = (ids) => {
    checkedKeys.value = array_merge(checkedKeys.value, ids)
    refTree.value.setCheckedKeys(checkedKeys.value)
}

const _remove_ids = (ids) => {
    checkedKeys.value = array_remove(checkedKeys.value, ids)
    refTree.value.setCheckedKeys(checkedKeys.value)
}

const onCurrNode = (node, data) => {
    if (!node.checked) {
        _add_ids([data.value])
    } else {
        _remove_ids([data.value])
    }
}

const onSameLevel = (node, data) => {
    const ids = getSameLevelIds(toRaw(fullTreeData.value)[0], toRaw(data).pid);
    if (!node.checked) {
        _add_ids(ids)
    } else {
        _remove_ids(ids)
    }
}

const reset = () => {
    checkedKeys.value = []
    refTree.value?.setCheckedKeys([])
}

watch(filterText, (val) => {
    refTree.value.filter(val)
})

defineExpose({
    reset,
    refTree
})
</script>

<style lang="scss" scoped>
.dt_left {
    width: 70%;
    display: flex;
    flex-direction: column;
    padding: 20px;

    .dt_left_header {
        height: 44px;

        .search_dept {
            width: 40%;
            min-width: 200px;
        }
    }

    .dt_left_main {
        height: calc(100vh - 201px);
        overflow-y: auto;

        .custom-tree-node {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-right: 8px;

            .icon_op {
                visibility: hidden;
                display: flex;
                align-items: center;

                .el-icon {
                    cursor: pointer;
                    font-size: 16px;
                    padding: 4px;
                }
            }
        }

        .custom-tree-node:hover {
            .icon_op {
                visibility: visible;
            }
        }
    }
}
</style>
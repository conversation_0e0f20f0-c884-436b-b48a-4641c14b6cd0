<template>
    <el-drawer v-model="dialogVisible" size="640" class="customer_edit_drawer" :close-on-click-modal="false"
        :close-on-press-escape="false">
        <template #header>
            <div class="vd_title">
                {{ title }}
            </div>
        </template>
        <template #default>
            <div class="customer_edit_form">
                <el-form :model="formData" label-width="120px" label-position="top" ref="formRef" :rules="rules">
                    <el-form-item label="客户名称" required prop="name" class="fi_item">
                        <CustomerNamePickerQcc v-model="formData.name" :enableQcc="false" />
                    </el-form-item>
                    <el-form-item label="负责人" required prop="ownerId" class="fi_item">
                        <DrawerSelectMixed ref="refOwner" @callback="onSelectOwner" type="user" :rootDeptId="rootDeptId"
                            :allDept="isSelectAllDept" :queryManagedDepartment="false" :maxSelect="1" />
                    </el-form-item>
                    <el-form-item label="协同跟进人" prop="coFollowers" class="fi_item">
                        <DrawerSelectMixed ref="refCoFollowers" @callback="onSelectCoFollowers" type="user"
                            class="co_followers" :rootDeptId="rootDeptId" :allDept="isSelectAllDept"
                            :queryManagedDepartment="false" />
                    </el-form-item>
                    <el-form-item v-for="field in formFields" :key="field.fieldId" :label="field.fieldName"
                        class="fi_item" :prop="field.fieldId">

                        <el-input v-if="field.fieldType == 'String'" v-model="formData[field.fieldId]"
                            :placeholder="field.placeholder || '请输入' + field.fieldName" :maxlength="field.maxLength"
                            :minlength="field.minLength" @blur="validateField(field.fieldId)" />

                        <el-input type="textarea" v-if="field.fieldType == 'TextArea'" v-model="formData[field.fieldId]"
                            :placeholder="field.placeholder || '请输入' + field.fieldName" :maxlength="field.maxLength"
                            :minlength="field.minLength" @blur="validateField(field.fieldId)" />

                        <el-date-picker v-if="field.fieldType == 'Date'" v-model="formData[field.fieldId]"
                            :format="getDtFormat(field)" :value-format="getDtFormat(field)"
                            :placeholder="field.placeholder || '请选择' + field.fieldName"
                            @blur="validateField(field.fieldId)" />

                        <el-date-picker v-if="field.fieldType == 'DateTime'" type="datetime"
                            v-model="formData[field.fieldId]"
                            :placeholder="field.placeholder || '请选择' + field.fieldName" :format="getDtFormat(field)"
                            :value-format="getDtFormat(field)" @blur="validateField(field.fieldId)" />

                        <el-input-number v-if="field.fieldType == 'Integer'" v-model="formData[field.fieldId]"
                            :max="field.maxValue == -1 ? Number.MAX_SAFE_INTEGER : field.maxValue" :min="field.minValue"
                            @blur="validateField(field.fieldId)" />

                        <el-select v-if="field.fieldType == 'Select'" v-model="formData[field.fieldId]"
                            :placeholder="field.placeholder || '请选择' + field.fieldName"
                            @blur="validateField(field.fieldId)">
                            <el-option v-for="item in field.fieldOptionsArray" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>

                        <el-select v-if="field.fieldType == 'MultiSelect'" v-model="formData[field.fieldId]" multiple
                            :placeholder="field.placeholder || '请选择' + field.fieldName"
                            @blur="validateField(field.fieldId)">
                            <el-option v-for="item in field.fieldOptionsArray" :key="item" :label="item" :value="item">
                            </el-option>
                        </el-select>
                    </el-form-item>
                </el-form>
            </div>
        </template>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="dialogVisible = false">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确定</el-button>
            </span>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, toRaw } from "vue";
import { getFormFields, getCustomerConfig, createCustomer, updateCustomer, getCustomerInfo } from "@/js/api.js"
import CustomerNamePickerQcc from "@/components/CustomerNamePicker/CustomerNamePickerQcc.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
const dialogVisible = ref(false);
const refOwner = ref(null);
const refCoFollowers = ref(null);
const rootDeptId = ref(null);
const formRef = ref(null);
const rules = ref({})
const title = ref('')
const domain = g.appStore.user.ssoDomain || '';
//  这里isSelectAllDept应该是一直true,但是pro-phx-ucstable测试机构部门太多，加载太慢，所以暂时先这样
const isSelectAllDept = ref(domain.indexOf("pro-phx-ucstable") < 0);
const emit = defineEmits(["reload"]);
const source = location.href.indexOf("admin") > -1 ? 0 : 1;
let dataId = ''
let editData = {}
const enableBusinessRule = ref(0)

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})
const _default_form_data = {
    source,
    "name": "",
    "ownerId": "",
    "coFollowers": [],
}

const getDtFormat = (field) => {
    if (field.fieldType == 'DateTime') {
        if (field.datetimePrecision == "hour") {
            return 'YYYY-MM-DD HH'
        } else {
            return 'YYYY-MM-DD HH:mm'
        }
    } else {
        return 'YYYY-MM-DD'
    }
}

const formData = ref({
    ..._default_form_data
})

const formFields = ref([]);

const validateField = (prop) => {
    if (formRef.value) {
        try {
            formRef.value.validateField(prop);
        } catch (error) {
            console.log("validateField error", error);
        }
    }
}

const onSelectOwner = (action, data) => {
    formData.value.ownerId = data.users.length > 0 ? data.users[0].id : '';
    validateField('ownerId');
}

const onSelectCoFollowers = (action, data) => {
    formData.value.coFollowers = data.users.map(user => user.id);
}

const get_data = () => {
    return new Promise((resolve, reject) => {
        console.log('get_data', formRef.value)
        if (formRef.value) {
            formRef.value.validate((valid) => {
                if (valid) {
                    const fd = toRaw(formData.value);
                    let data = {
                        source,
                        name: fd.name,
                        ownerId: fd.ownerId,
                        coFollowers: fd.coFollowers,
                        dynamicFormData: {
                            formCode: "CUSTOMER_TPL",
                            fieldValues: []
                        }
                    }

                    formFields.value.forEach(item => {
                        let fieldValue = fd[item.fieldId] || ''
                        const newFiledValue = Array.isArray(fieldValue) ? fieldValue.join(',') : fieldValue
                        data.dynamicFormData.fieldValues.push({
                            id: item.id || '',
                            fieldId: item.fieldId,
                            fieldName: item.fieldName,
                            fieldValue: newFiledValue,
                            order: item.order || 1
                        })
                    });
                    resolve(data);
                } else {
                    resolve(false);
                }
            });
        } else {
            ElMessage.error('请先完成表单');
            resolve(false);
        }
    });
}

const handleConfirm = async () => {
    const data = await get_data();
    console.log('handleConfirm', data)
    if (!data) {
        return;
    }
    if (dataId) {
        updateCustomer(dataId, data).then(res => {
            if (res.code == 0) {
                dialogVisible.value = false;
                ElMessage.success('更新成功');
                emit('reload');
            } else {
                ElMessage.error(res.message || '更新失败');
            }
        }).catch(err => {
            console.log("update customer error", err);
            ElMessage.error('更新客户失败');
        })
    } else {
        createCustomer(data).then(res => {
            if (res.code == 0) {
                dialogVisible.value = false;
                ElMessage.success('创建成功');
                emit('reload');
            } else {
                ElMessage.error(res.message || '创建失败');
            }
        }).catch(err => {
            console.log("add customer error", err);
            ElMessage.error('创建客户失败');
        })
    }
}

const show_add = async () => {
    title.value = `创建客户`
    dataId = ''
    formData.value = {
        ..._default_form_data,
    }
    editData = { formData: { fieldValues: [] } }
    dialogVisible.value = true;

    try {
        // 获取字段定义信息
        const fieldDefRes = await getFormFields('CUSTOMER_TPL');
        const fieldDefinitions = fieldDefRes.code === 0 ? fieldDefRes.data : [];

        // 初始化表单字段
        formFields.value = fieldDefinitions.map(fieldDef => ({
            fieldId: fieldDef.id,
            fieldName: fieldDef.fieldName,
            fieldType: fieldDef.fieldType || 'String',
            placeholder: fieldDef.placeholder || '',
            maxLength: fieldDef.maxLength || 255,
            minLength: fieldDef.minLength || 0,
            maxValue: fieldDef.maxValue || -1,
            minValue: fieldDef.minValue || 0,
            fieldOptions: fieldDef.fieldOptions || '',
            fieldOptionsArray: fieldDef.fieldOptions ? fieldDef.fieldOptions.split(',') : [],
            isRequired: fieldDef.isRequired || 0,
            datetimePrecision: fieldDef.datetimePrecision || 'minute',
            order: fieldDef.sortOrder || 1
        }));

        // 设置验证规则
        rules.value = {
            name: [{
                required: true,
                message: enableBusinessRule == 0 ? '请输入客户名称' : '',
                trigger: 'blur'
            }],
            ownerId: [{
                required: true,
                message: '请选择负责人',
                trigger: 'blur'
            }],
            ...getCustomRules(formFields.value)
        }

        nextTick(() => {
            formRef.value.resetFields();
            setTimeout(() => {
                formRef.value.resetFields();
            }, 100);
            refOwner.value.init({ users: [] })
            refCoFollowers.value.init({ users: [] })
        })
    } catch (error) {
        console.error('获取字段定义失败:', error);
        ElMessage.error('获取字段定义失败');
        dialogVisible.value = false;
    }
}

const show_edit_byid = async (id) => {
    getCustomerInfo(id).then(res => {
        if (res.code == 0) {
            show_edit(res.data)
        } else {
            ElMessage.error('获取客户信息失败')
            dialogVisible.value = false
        }
    }).catch(err => {
        console.log(err);
        ElMessage.error('获取客户信息失败')
        dialogVisible.value = false
    })
}

const show_edit = async (data) => {
    title.value = '编辑客户'
    // console.log('show_edit', JSON.stringify(data))
    dataId = data.id
    editData = { ...data }
    dialogVisible.value = true;

    try {
        // 获取字段定义信息，用于获取字段类型
        const fieldDefRes = await getFormFields('CUSTOMER_TPL');
        const fieldDefinitions = fieldDefRes.code === 0 ? fieldDefRes.data : [];

        // 从formData.fieldValues中获取字段信息，并合并字段类型信息
        const fieldValues = data.formData?.fieldValues || [];
        formFields.value = fieldValues.map(fieldValue => {
            // 查找对应的字段定义
            const fieldDef = fieldDefinitions.find(def => def.id === fieldValue.fieldId);
            return {
                ...fieldValue,
                fieldType: fieldDef?.fieldType || 'String', // 默认使用String类型
                placeholder: fieldDef?.placeholder || '',
                maxLength: fieldDef?.maxLength || 255,
                minLength: fieldDef?.minLength || 0,
                maxValue: fieldDef?.maxValue || -1,
                minValue: fieldDef?.minValue || 0,
                fieldOptions: fieldDef?.fieldOptions || '',
                fieldOptionsArray: fieldDef?.fieldOptions ? fieldDef.fieldOptions.split(',') : [],
                isRequired: fieldDef?.isRequired || 0,
                datetimePrecision: fieldDef?.datetimePrecision || 'minute'
            };
        });

        // 处理负责人（单选）
        const ownerUser = data?.salesSsoUserIds && data?.salesSsoUserNames ?
            [{ id: data.salesSsoUserIds, fullname: data.salesSsoUserNames }] : [];

        // 处理协同跟进人（多选）
        const coFollowerUsers = [];
        const coFollowerIds = data?.followSsoUserIds?.split(',') || [];
        const coFollowerNames = data?.followSsoUserNames?.split(',') || [];
        for (let i = 0; i < coFollowerIds.length; i++) {
            if (coFollowerIds[i] && coFollowerNames[i]) {
                coFollowerUsers.push({ id: coFollowerIds[i], fullname: coFollowerNames[i] });
            }
        }

        // 初始化表单数据
        formData.value = {
            id: data.id,
            source,
            name: data.name,
            ownerId: data.salesSsoUserIds || '',
            coFollowers: coFollowerIds
        }

        // 处理动态字段值
        formFields.value.forEach(field => {
            if (field.fieldType == 'MultiSelect' || field.fieldType == 'Select') {
                formData.value[field.fieldId] = field.fieldValue ? field.fieldValue.split(',') : [];
            } else if (field.fieldType == 'Date' || field.fieldType == 'DateTime') {
                formData.value[field.fieldId] = field.fieldValue;
            } else if (field.fieldType == 'Integer') {
                formData.value[field.fieldId] = parseInt(field.fieldValue) || 0;
            } else {
                formData.value[field.fieldId] = field.fieldValue || '';
            }
        });

        // 设置验证规则
        rules.value = {
            name: [{
                required: true,
                message: enableBusinessRule == 0 ? '请输入客户名称' : '',
                trigger: 'blur'
            }],
            ownerId: [{
                required: true,
                message: '请选择负责人',
                trigger: 'blur'
            }],
            ...getCustomRules(formFields.value)
        }

        // console.log(JSON.stringify(toRaw(formData.value)));
        refOwner.value.init({ users: ownerUser })
        refCoFollowers.value.init({ users: coFollowerUsers })
    } catch (error) {
        console.error('获取字段定义失败:', error);
        ElMessage.error('获取字段定义失败');
        dialogVisible.value = false;
    }
}

const getCustomRules = (fields) => {
    const fieldRules = {};

    fields.forEach((field) => {
        const rules = [];

        if (field.isRequired) {
            rules.push({
                required: true,
                message: `请输入${field.fieldName}`,
                trigger: "blur",
            });
        }

        if (field.fieldType == "String" || field.fieldType == "TextArea") {
            if (field.maxLength) {
                rules.push({
                    max: field.maxLength,
                    message: `${field.fieldName}最多输入${field.maxLength}个字符`,
                    trigger: "blur",
                });
            }

            if (field.minLength) {
                rules.push({
                    min: field.minLength,
                    message: `${field.fieldName}最少输入${field.minLength}个字符`,
                    trigger: "blur",
                });
            }
        }

        if (rules.length > 0) {
            fieldRules[field.fieldId] = rules;
        }
    });

    return fieldRules;
};

const initLoad = () => {
    getCustomerConfig().then(res => {
        enableBusinessRule.vlaue = res.data.enableBusinessRule
    })
}

onMounted(() => {
    initLoad()
})

defineExpose({
    show_add,
    show_edit,
    show_edit_byid
})
</script>

<style lang="scss">
.customer_edit_drawer {
    .vd_title {
        color: #262626;
    }

    .customer_edit_form {
        .fi_item {
            margin-left: 24px;

            .mixed_input_box {
                width: 100%;
            }
        }

        .fi_divider {
            margin-top: 40px;
            margin-bottom: 24px;
        }

        .divider_title {
            border-left: 4px solid #436BFF;
            padding-left: 10px;
            line-height: 20px;
        }
    }
}
</style>

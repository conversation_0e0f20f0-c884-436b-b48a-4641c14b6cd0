<template>
  <div class="overview flex-col" v-loading="loading" element-loading-text="数据加载中... 请稍等">
    <div class="overview-header flex-row">
      <div class="flex-row  title-line-wrap">
        <div class="title-line"></div>
        <div class="title">总览</div>
      </div>
    </div>

    <div class="overview-body">
      <CategorySection title="沟通计划总数" :hint="type_hints['visitPlan']" :items="getDataFilter('visitPlan', columnData)"
        :itemsPerRow="2" :active="activeCategory === 'visitPlan'" @click="handleCategoryClick('visitPlan')"
        :isReport="isReport" :isDept="deptData?.children?.length > 0" />

      <CategorySection title="沟通记录" :hint="type_hints['visitRecord']" :items="getDataFilter('visitRecord', columnData)"
        :itemsPerRow="2" :active="activeCategory === 'visitRecord'" @click="handleCategoryClick('visitRecord')"
        :isReport="isReport" :isDept="deptData?.children?.length > 0" />


      <CategorySection title="沟通表现" :hint="type_hints['visitPerformance']"
        :items="getDataFilter('visitPerformance', columnData)" :itemsPerRow="1"
        :active="activeCategory === 'visitPerformance'" @click="handleCategoryClick('visitPerformance')"
        :isReport="isReport" :isDept="deptData?.children?.length > 0" />
    </div>
  </div>
</template>

<script setup>
import { deepCopy } from "@/js/utils";
import CategorySection from './CategorySection.vue';
import { getTeamVisitReport } from "@/app_client/tools/api.js";
import { type_hints, convertOverviewData, getReportDate } from '../misc.js';

const props = defineProps({
  isReport: {
    type: Boolean,
    default: false
  }
});
const isShangji = computed(() => g.appStore.isShangji);

const deptData = ref({ children: [] });
const loading = ref(false);
const periodType = computed(() => g.clientBoardStore.periodType);
const activeCategory = ref('visitPlan');
const formData = reactive({
  "dptId": '',
  "userIds": []
});

const columnData = ref([]);


const getDataFilter = (type, list) => {
  return list.filter(
    (item) => item.category === type && !item.hide
  );
};
const reportDate = getReportDate();
const getTeamVisitReportData = async () => {
  if (!formData.dptId) return;
  loading.value = true;
  const res = await getTeamVisitReport(formData.dptId, periodType.value, reportDate);
  const result = convertOverviewData(deepCopy(res), isShangji.value);
  console.log(res);
  columnData.value = result;

  let overviewData;
  if (res.data && Object.keys(res.data).length > 0) {
    if (res.data.datas && res.data.datas.length > 0) {
      overviewData = res.data.datas.sort((a, b) => b.reportTime - a.reportTime)[0];
    } else {
      overviewData = res.data;
    }
    const { createdTime } = overviewData;
    //update_team_report_time
    g.emitter.emit("update_team_report_time", createdTime);
    g.clientBoardStore.overviewData = overviewData;
    g.emitter.emit("team_board_overview_data", '');
  }
  loading.value = false;
};

const setDept = (dept) => {
  deptData.value = dept;
  formData.dptId = dept.value;
  g.clientBoardStore.category = activeCategory.value;
  getTeamVisitReportData();
};

const handleCategoryClick = (category) => {
  activeCategory.value = category;
  g.clientBoardStore.category = category;
};

watch(() => periodType.value, () => {
  getTeamVisitReportData();
}, { immediate: true });

defineExpose({
  getTeamVisitReportData,
  periodType,
  setDept,
});
</script>

<style lang="scss" scoped>
.overview {
  .no_select_col {
    width: 100%;
    margin: 20px 0;
    font-size: 16px;
    color: #262626;
    height: 250px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .overview-header {
    justify-content: space-between;
    margin-bottom: 24px;

    .title-line {
      width: 3px;
      height: 16px;
      background: #436bff;
      margin: 0px 12px 0 0;
    }

    .title {
      font-weight: 700;
      font-size: 18px;
      color: #262626;
      line-height: 26px;
    }
  }

  .overview-body {
    display: flex;
    gap: 24px;
  }
}
</style>

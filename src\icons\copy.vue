<template>
    <svg width="1em" height="1em" viewBox="0 0 16 16" version="1.1">
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="选中关键字" transform="translate(-626.000000, -535.000000)">
                <g id="编组-12" transform="translate(626.000000, 532.000000)">
                    <g id="icon/复制" transform="translate(0.000000, 3.000000)">
                        <polygon id="路径" points="0 0 16 0 16 16 0 16"></polygon>
                        <path
                            d="M4.66666667,4.66666667 L4.66666667,2 C4.66666667,1.63181017 4.9651435,1.33333333 5.333333,1.33333333 L14,1.33333333 C14.3681898,1.33333333 14.6666667,1.63181017 14.6666667,2 L14.6666667,10.6666667 C14.6666667,11.0348565 14.3681898,11.3333333 14,11.3333333 L11.333333,11.3333333 L11.333333,13.9953333 C11.333333,14.366 11.034,14.666667 10.662,14.666667 L2.00466667,14.666667 C1.82656375,14.6668438 1.65570456,14.5961711 1.52976672,14.4702333 C1.40382888,14.3442954 1.3331562,14.1734362 1.333333,13.9953333 L1.33533333,5.338 C1.33533333,4.96733333 1.63466667,4.66666667 2.00666667,4.66666667 L4.66666667,4.66666667 Z M6,4.66666667 L10.662,4.66666667 C11.0326667,4.66666667 11.3333333,4.966 11.3333333,5.338 L11.3333333,10 L13.3333333,10 L13.3333333,2.66666667 L6,2.66666667 L6,4.66666667 Z M2.66866667,6 L2.66666667,13.3333333 L10,13.3333333 L10,6 L2.66866667,6 Z"
                            id="形状" fill="currentColor"></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'Icon',
}
</script>

<style lang='scss' scoped></style>

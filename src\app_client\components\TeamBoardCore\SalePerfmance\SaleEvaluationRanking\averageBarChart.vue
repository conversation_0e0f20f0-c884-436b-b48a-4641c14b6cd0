<template>
  <div class="ser_wrap flex-col">
    <div class="ser_wrap_flex_row">
      <RadioGroup v-if="lastNotDept" v-model="teamType" class="fch_radio" :options="options2" /> <span
        v-else>&nbsp;</span>
      <RadioGroup v-model="perfTypeRanking" class="fch_radio" :options="options" @change="onChangePerfType" />
    </div>
    <div class="ser_types">
      <el-select v-model="currType" placeholder="请选择维度" style="width: 440px" @change="onClickDim">
        <el-option label="全部维度" value="all" key="all" />
        <el-option v-for="item in allDimData" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </div>
    <div class="sp_body">
      <div class="sp-left">
        <AvgBarChart v-loading="loading" element-loading-text="数据加载中... 请稍等" ref="refAvgBarChart" @callback="onCallback"
          :innerWidth="innerWidth" :standardScore="standardScore" :isEndDept="teamType === 'user'"
          :isStandardNumber="true" key="salesAssessmentConfig" :mark="perfTypeRanking === 'ability' ? '' : '%'">
        </AvgBarChart>
      </div>

      <div class="sp-right" v-if="needRightUi" v-loading="loadingData">
        <div v-if="perfTypeRanking === 'ability'">
          <SalesAbilityRadar ref="refSalesAbilityRadar" :pieData="pieDataObj" :barData="barData" />
        </div>
        <div v-if="perfTypeRanking === 'taskCompletion'">
          <TaskCompletion ref="refTaskCompletion" :pieData="pieDataObj" :barData="barData" />
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>

import AvgBarChart from "@/app_client/components/AvgBarChart.vue";
import { getDimensionScores } from "@/app_client/tools/api.js";
import { getSalesAllDims } from "@/js/api.js";
import { computed, ref } from "@vue/reactivity";
import RadioGroup from '@/app_client/components/RadioGroup.vue'
import TaskCompletion from './TaskCompletion.vue'
import { throttle } from "@/js/utils.js"; // 引入节流函数
import { getUserDimensionData, getSalesAssessmentConfig, getAssessment, getListUserReportInThisTeam, getReportSubUser } from '@/app_client/tools/api'
import SalesAbilityRadar from "@/app_client/components/TeamBoardCore/components/SalesAbilityRadarUser.vue";
import { getReportDate } from '@/app_client/components/TeamBoardCore/misc.js'
import { round, getUrlParam } from '@/js/utils'
const refSalesAbilityRadar = ref(null)
const refTaskCompletion = ref(null)
const refAverageBarChart = ref([])
const barData = ref([])
const pieDataObj = ref({})
const analyseUser = ref({ ssoUserId: '' })
const reportDate = getReportDate();
const perfRankRowData = computed(() => {
  return g.clientBoardStore.perfRankRowData;
})
const standardScore = computed(() => {
  if (perfTypeRanking.value === 'ability') {
    return g.clientBoardStore.standardSetting.abilityScore4Pass;
  } else if (perfTypeRanking.value === 'taskCompletion') {
    return g.clientBoardStore.standardSetting.taskRate4Pass;
  }
  return 0;
})

const needRightUi = ref(false)
const options2 = [
  {
    label: '团队',
    value: 'department'
  },
  {
    label: '成员',
    value: 'user'
  }
]
const cmap = {
  ability: "salesAbilityAssesses",
  taskCompletion: "taskCompleteAssesses",
};
const options = g.clientBoardStore.perfTypeOptions;
const emit = defineEmits(['callback'])
const currType = ref('all');
const teamType = ref('department');
const perfTypeRanking = ref('');
const avgBarChartShow = ref(false);
const periodType = computed(() => {
  return g.clientBoardStore.periodType;
})
const overviewDept = computed(() => {
  return g.clientBoardStore.overviewDept;
});
const lastNotDept = ref(false)
const innerWidth = ref(0)
const perfAllDims = ref({});
const refAvgBarChart = ref(null);
const allDimData = computed(() => {
  const list = perfAllDims.value[cmap[perfTypeRanking.value]] || {};
  if (list.customizedAssessments?.length > 0 || list.systemAssessments?.length > 0) {
    return [...list["customizedAssessments"], ...list["systemAssessments"]];
  }
  return [];
});

const loading = ref(true);
const loadingData = ref(true);

// 清除选中维度
const onChangePerfType = () => {
  currType.value = 'all';
}
// 获取所有维度
const getAllDim = () => {
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const parm = {
    startTime, endTime,
  }
  getSalesAssessmentConfig(parm).then((res) => {
    perfAllDims.value = res.data;
  });
};
// 生成维度相关的柱状图
const initBar = () => {
  const orgId = g.appStore.user.orgId;
  const deptId = overviewDept.value.value;
  if (!orgId || !deptId) return;
  const userOrDeparment = teamType.value //user,department
  const dataType = perfTypeRanking.value == 'ability' ? 'ABILITY' : 'COMPLETION';
  const dimensionId = currType.value;
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const parm = {
    "pageSize": 10,
    "pageNumber": 1,
    "orderBy": "",
    "asc": false,
    startTime, endTime,
    "reportType": periodType.value
  }
  loading.value = true;
  getDimensionScores(orgId, deptId, userOrDeparment, dataType, dimensionId, parm).then((res) => {
    const datas = res.data.datas;
    const data = datas.map((item) => {
      return {
        label: teamType.value == 'department' ? item.deptName : item.name,
        value: item.score ? Number(item.score).toFixed(2) : 0,
        data: item
      }
    });

    nextTick(() => {
      innerWidth.value = document.querySelector(".sp-left").offsetWidth
      refAvgBarChart.value.selectedRegion = ''
      refAvgBarChart.value.init(data);
    })
    loading.value = false;
  }).finally(() => {
    loading.value = false
  })
}

const onCallback = (type, data) => {
  if (g.clientBoardStore.perfRankRowData.ssoUserId == data.data.ssoUserId) {
    openUserDetail(type, {})
    g.clientBoardStore.perfRankRowData = {};

  } else {
    g.clientBoardStore.perfRankRowData = data.data;
    openUserDetail(type, data)
  }

}
const onClickDim = (item) => {
  init();
}

const init = () => {
  initBar();
}
// 点击弹窗的详情展示
const openUserDetail = (action, data) => {
  if (teamType.value === 'department') return
  if (action === 'setUser') {
    analyseUser.value = data.data || {}
    _updateRightUI()
    needRightUi.value = analyseUser.value.ssoUserId ? true : false
  } else if (action === 'init') {
    needRightUi.value = false
  }
}
const _initAbilityChart = async () => {
  loadingData.value = true
  pieDataObj.value = {}
  barData.value = []
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const parm = {
    startTime, endTime,
  }
  await Promise.all([getUserDimensionData(g.appStore.user.ssoOrgId, analyseUser.value.ssoUserId, 'ABILITY', periodType.value, parm), getReportSubUser(analyseUser.value.deptId, periodType.value, reportDate)]).then(([radarData, res2]) => {
    if (radarData.code === 0 && radarData.data.datas) {
      const data = radarData.data.datas.length > 0 ? [
        {
          color: '#426BFF',
          value: radarData.data.datas.map((x) => x.assessmentValue ? Number(x.assessmentValue).toFixed(2) : 0),
          radarIndicator: radarData.data.datas.map((x) => ({
            name: x.assessmentName,
            max: 100,
            min: 0,
            value: x.assessmentValue ? Number(x.assessmentValue).toFixed(2) : 0
          })),
        }
      ] : []
      barData.value = data
    }

    if (res2.data.datas && res2.data.datas.length > 0) {
      pieDataObj.value = res2.data.datas.find(item => item.ssoUserId === analyseUser.value.ssoUserId) || {}
      pieDataObj.value.averageAbilityValue = Number(analyseUser.value.score || 0).toFixed(2)
      g.clientBoardStore.regionData.data = toRaw(pieDataObj.value);
    }

  }).finally(() => {
    loadingData.value = false
  })

}

const _initTaskCompletion = async () => {
  loadingData.value = true
  pieDataObj.value = {}
  barData.value = []
  const ssoUserId = analyseUser.value.ssoUserId
  const { startTime, endTime } = g.clientBoardStore.getPerfQueryTime()
  const parm = {
    startTime, endTime,
  }
  await Promise.all([getAssessment(periodType.value, g.appStore.user.ssoOrgId, ssoUserId, 'COMPLETION', parm), getReportSubUser(analyseUser.value.deptId, periodType.value, reportDate)]).then(([res, res2]) => {
    if (res.code === 0 && res.data?.datas) {
      barData.value = res.data.datas.map(item => ({
        label: item.assessmentName,
        value: item.assessmentValue ? Number(item.assessmentValue).toFixed(2) : 0,
        data: item
      }))
    }
    if (res2.data.datas && res2.data.datas.length > 0) {
      pieDataObj.value = res2.data.datas.find(item => item.ssoUserId === analyseUser.value.ssoUserId) || {}
      pieDataObj.value.averageTaskValue = Number(analyseUser.value.score || 0).toFixed(2)
      g.clientBoardStore.regionData.data = toRaw(pieDataObj.value);
    }

  }).finally(() => {
    loadingData.value = false
  })
}

watch(() => [perfTypeRanking.value, teamType.value, periodType.value, overviewDept.value], () => {
  needRightUi.value = false
}, { immediate: true });

watch(() => g.clientBoardStore.perfType, () => {
  perfTypeRanking.value = g.clientBoardStore.perfType;
}, { immediate: true });

watch(() => [perfTypeRanking.value, overviewDept.value, periodType.value, perfTypeRanking.value, teamType.value], () => {
  loading.value = true;
  g.clientBoardStore.perfRankRowData = {};
  getAllDim();
  init()
}, { immediate: true });

watch(() => [overviewDept.value], () => {
  lastNotDept.value = overviewDept.value?.children && overviewDept.value?.children?.length > 0
  if (!lastNotDept.value) {
    teamType.value = 'user'
  }
}, { immediate: true });



const _updateRightUI = throttle(() => {
  if (analyseUser.value.ssoUserId) {
    if (perfTypeRanking.value == 'ability') {
      _initAbilityChart()
    } else if (perfTypeRanking.value === 'taskCompletion') {
      _initTaskCompletion()
    }
  }
}, 500); // 设置节流时间为500毫秒

onMounted(() => {
  init()
})
</script>

<style lang="scss" scoped>
.ser_wrap {
  width: 100%;

  .ser_wrap_flex_row {
    display: flex;
    justify-content: space-between;
  }

  .ser_types {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;
    gap: 12px;

    margin: 20px 0 24px 0;

    .sty {
      text-align: center;
      font-weight: 400;
      font-size: 14px;
      color: #595959;
      line-height: 20px;
      border-radius: 14px;
      padding: 4px 15px;
      cursor: pointer;
      background: #F4F5F6;
    }

    .active {
      color: #436DFF;
      background: #FFFFFF rgba(67, 107, 255, 0.1);
    }

    .noraml {
      color: #595959;
      background: #F4F5F6;
    }
  }

  .sp_body {
    display: flex;
    flex-direction: row;
    gap: 10px;
    position: relative;
    width: 100%;

    .sp-left {
      width: calc(0.5 * (100vw - 220px));


    }

    .sp-right {
      display: flex;
      flex-direction: column;
      gap: 16px;
      width: calc(0.4 * (100vw - 220px));
      // position: absolute;
      // z-index: 99;
      // right: 0;
      // top: 24px;
    }
  }

}
</style>

// 表格列配置
export const getColumns = () => {
  return [
    "opportunity_name", // 商机名称
    "opportunity_status", // 商机状态
    "relatedCustomer", // 关联客户
    "salesSsoUserNames", // 负责人
    "followSsoUserNames", // 协同8跟进人

    "riskCount", // 预警
    "communication_dynamics", // 沟通动态
    "meddic", // MEDDIC
    "todosCount", // 待办
    "attendeeCount", // 客户参会人
    "competitorNames", // 竞争对手
    "communicationCount", // 沟通次数
  ];
};

// 列宽度配置
export const column_widths = {
  opportunity_name: 150,
  opportunity_status: 120,
  relatedCustomer: 110,
  salesSsoUserNames: 100,
  followSsoUserNames: 120,

  riskCount: 80,
  meddic: 200,
  communication_dynamics: 230,
  todosCount: 100,
  // todosCount: 100,
  attendeeCount: 150,
  competitorNames: 130,
  communicationCount: 110,
};

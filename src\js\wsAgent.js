// TF:wss://api-ws-tf.x-mate.com/ws?token=&clientType= {"endpoint":"xmate_schedule_update","data":null}
// 使用示例
import WebSocketClient from "@/js/webSocketClient.js";
let wsAgent = null;
const events = [
  "xmate_schedule_update",
  "CONFERENCE_REPORT_STATUS_UPDATE",
  "XMATE_DEVICE_BATTERY_LOW_RECORD_STOP",
  "XMATE_DEVICE_RECORD_STOP",
  "XMATE_REPORT_UPDATE",
  "XMATE_CONF_SUMMARY_MSG",
  "XMATE_DEVICE_RECORD_STATUS_CHANGE",
];

const update_schedule_events = [
  "XMATE_DEVICE_RECORD_STATUS_CHANGE",
  "CONFERENCE_REPORT_STATUS_UPDATE",
  "XMATE_DEVICE_RECORD_STOP",
];

const createWsAgent = () => {
  if (wsAgent) {
    return wsAgent;
  }
  // clientType:web,win,mac,android,ios
  const clientType = g.config.clientType;
  const token = g.appStore.user.token;
  if (!token) {
    console.log("ws not connect, token is null");
    return;
  }
  const url = `${
    import.meta.env.VITE_WS_URL
  }?token=${token}&clientType=${clientType}`;
  wsAgent = new WebSocketClient(url, {
    reconnectInterval: 3000, // 重连间隔（毫秒）
    heartbeatInterval: 30000, // 心跳间隔（毫秒）
  });

  // 监听连接状态
  wsAgent.onStatus("connected", () => {
    g.emitter.emit("ws_connected", true);

    for (let event of events) {
      wsAgent.on(event, (data) => {
        console.log("get ", event, data);
        if (update_schedule_events.includes(event)) {
          g.emitter.emit("xmate_schedule_update", data);
        }
        g.emitter.emit(event, data);
      });
    }
  });

  wsAgent.onStatus("disconnected", () => {
    // console.log("WebSocket已断开");
    g.emitter.emit("ws_connected", false);
  });

  wsAgent.onStatus("error", (error) => {
    console.log("WebSocket发生错误:", error);
    g.emitter.emit("ws_error", error);
  });

  // 关闭连接
  // ws.close();
  wsAgent.connect();
  return wsAgent;
};

const reconnectWsAgent = () => {
  //如果连接正常，则不再连接
  if (wsAgent && wsAgent.isConnected) {
    return;
  }
  wsAgent.close();
  wsAgent = null;
  createWsAgent();
};

export { createWsAgent, reconnectWsAgent };

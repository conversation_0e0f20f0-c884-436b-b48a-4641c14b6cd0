<template>
    <Modal ref="refModal" title="新建客户" width="30%" :close-on-click-modal="false" modal-class="customer_list_btn_add"
        @callback="cbModal">
        <div class="customer-type-select">
            <el-form ref="refForm" :model="formData" label-width="100px" :rules="rules" label-position="top">
                <el-form-item label="客户类型" prop="customerType">
                    <el-radio-group v-model="formData.customerType" required>
                        <el-radio v-for="item in customerTypeList" :key="item.id" :value="item.id">{{ item.name
                        }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
        </div>
    </Modal>
    <DrawerCustomerForm ref="diaAddForm" :isAdmin="props.isAdmin" />
</template>

<script setup>
import Modal from '@/components/Modal.vue';
import DrawerCustomerForm from "./DrawerCustomerForm.vue";
import { ref } from "vue";

const refModal = ref();
const diaAddForm = ref(null);
const refForm = ref();
const formData = ref({
    customerType: ''
});
const emit = defineEmits(['confirm']);
const customerTypeList = ref([]);
const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    }
})


const getCustomerTypes = () => {
    // getCustomerTypeList({ status: 1 }).then(res => {
    //     let list = res.data;
    //     if (!props.isAdmin) {
    //         // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
    //         list = list.filter(x => x?.permissionsStr && x?.permissionsStr?.indexOf('1') > -1)
    //     }
    //     customerTypeList.value = list;
    //     if (list.length == 1) {
    //         formData.value.customerType = list[0].id;
    //         handleConfirm()
    //     }
    // });
};

const rules = {
    customerType: [
        { required: true, message: '请选择客户类型', trigger: 'change' }
    ]
};

const handleConfirm = () => {
    refForm.value.validate((valid, fields) => {
        if (valid) {
            const item = customerTypeList.value.find(x => x.id == formData.value.customerType);
            diaAddForm.value.show_add(item);
            refModal.value.hide();
        }
    });
};

const cbModal = (action) => {
    if (action == "confirm") {
        handleConfirm()
    } else if (action == 'cancel') {
        refModal.value.hide();
    }
}

const show = () => {
    getCustomerTypes();
    refModal.value.show();
}

defineExpose({
    show
})
</script>

<style lang="scss">
.customer_list_btn_add {
    .customer-type-select {
        padding: 20px;
    }
}
</style>

<template>
    <div class="form_title">
        <div class="title">{{ title }}</div>
        <div v-if="required" class="required"></div>
    </div>
</template>

<script setup>
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    required: {
        type: Boolean,
        default: false
    }
})

</script>

<style lang="scss" scoped>
.form_title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .required {
        background-color: red;
        width: 4px;
        height: 4px;
        border-radius: 50%;
        margin-left: 4px;
    }

    .title {
        font-size: 14px;
        font-weight: 500;
    }
}
</style>
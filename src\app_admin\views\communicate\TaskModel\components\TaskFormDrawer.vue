<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="ability-form-drawer"
    :close-on-click-modal="false">
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">{{ drawerTitle }}</span>
      </div>
    </template>

    <div class="drawer-content">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" require-asterisk-position="right">
        <div>
          <el-form-item label="任务名称" prop="name">
            <el-input v-model="form.name" show-word-limit maxlength="50" />
          </el-form-item>
        </div>
        <div class="form-section">
          <el-form-item label="行为表现" prop="behaviors">
            <div class="behavior-item" v-for="(behaviorText, index) in form.items" :key="index">
              {{ index + 1 }}
              <el-input v-model="behaviorText.content" type="textarea" maxlength="200" :rows="4"
                class="behavior-textarea" show-word-limit />
              <el-button class="delete-behavior" :disabled="form.items.length <= 1" @click="removeBehavior(index)"
                circle>
                <el-icon>
                  <Delete />
                </el-icon>
              </el-button>
            </div>
          </el-form-item>
        </div>

        <div class="add-behavior-section">
          <el-button class="add-behavior-btn" @click="addBehavior" text>
            <el-icon>
              <Plus />
            </el-icon>
            <span>添加行为表现</span>
          </el-button>
        </div>
      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close, Delete } from '@element-plus/icons-vue'

const emit = defineEmits(['success'])

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

const drawerTitle = computed(() => {
  return isEditMode.value ? '编辑' : '新建'
})

const form = reactive({
  name: '',
  items: [
    {
      content: ''
    }
  ]
})
const validatePass = (rule, value, callback) => {
  console.log(value)
  if (value && value.some(item => !item)) {
    callback(new Error('请输入行为表现'))
  } else {
    callback()
  }
}

const rules = {
  name: [
    { required: true, message: '请输入任务名称', trigger: 'blur' },
    { max: 50, message: '最多输入50个字符', trigger: 'blur' }
  ],
  behaviors: [
    { required: true, message: '请输入行为表现', validator: validatePass, trigger: 'blur' },
  ]
}


const openDrawer = (data = null) => {
  drawerVisible.value = true

  if (data) {
    // 编辑模式
    isEditMode.value = true
    currentId.value = data.id
    form.name = data.name
    form.items = data.items && data.items.length > 0
      ? [...data.items]
      : [{ content: '' }]
  } else {
    // 添加模式
    isEditMode.value = false
    currentId.value = null
    form.name = ''
    form.items = [{ content: '' }]
  }
}

const closeDrawer = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const addBehavior = () => {
  if (form.items.length < 5) {
    form.items.push({ content: '' })
  } else {
    ElMessage.warning('最多添加5个行为表现')
  }
}

const removeBehavior = (index) => {
  if (form.items.length > 1) {
    form.items.splice(index, 1)
  } else {
    ElMessage.warning('至少保留一个行为表现')
  }
}

const submitForm = () => {
  if (form.items.length > 5) {
    ElMessage.warning('最多保留5个行为表现')
    return
  }
  formRef.value.validate((valid) => {
    if (valid) {
      // 处理表单提交
      const formData = {
        id: currentId.value,
        name: form.name,
        items: form.items
      }

      emit('success', {
        mode: isEditMode.value ? 'edit' : 'add',
        data: formData
      })
      closeDrawer()
    } else {
      ElMessage.error('请检查输入内容')
      return false
    }
  })
}

const handleClose = (done) => {
  closeDrawer()
  done()
}

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer
})
</script>

<style scoped lang="scss">
.ability-form-drawer {
  :deep(.el-drawer__header) {
    padding: 0;
    margin-bottom: 0;
    border-bottom: 1px solid #E9E9E9;
  }

}

.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.drawer-content {
  overflow-y: auto;
}


.behavior-item {
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 20px;
  background: #F9FAFC;
  border-radius: 8px 8px 8px 8px;
  gap: 12px;


  .char-count {
    position: absolute;
    right: 12px;
    bottom: 12px;
    font-size: 12px;
    color: #999;
    pointer-events: none;
  }

  .delete-behavior {
    width: 24px;
    height: 24px;
    border: none;
  }
}


.add-behavior-btn {
  width: 100%;
  margin-top: 12px;
  background: rgba(255, 255, 255, 0.01);
  border-radius: 8px 8px 8px 8px;
  border: 1px dashed #436BFF;
  text-align: center;
  font-weight: 400;
  font-size: 14px;
  color: #436BFF;
  line-height: 22px;

  .el-icon {
    margin-right: 8px;
    font-size: 16px;
  }
}



.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 5px 16px;
    border-radius: 6px;
  }

  .cancel-btn {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
  }

  .confirm-btn {
    background: #436BFF;
    border-color: #436BFF;
  }
}
</style>

<template>
    <div class="lx-date-time-picker">
        <el-popover v-model:visible="visible" :width="450" trigger="click" placement="bottom-start"
            popper-class="lx-date-time-picker-popover">
            <template #reference>
                <el-input v-model="displayValue" :placeholder="placeholder" readonly :clearable="clearable"
                    @clear="handleClear">
                    <template #suffix>
                        <el-icon class="el-input__icon">
                            <Calendar />
                        </el-icon>
                    </template>
                </el-input>
            </template>

            <div class="date-time-panel">
                <div class="main-content">
                    <!-- 日期选择器部分 -->
                    <div class="date-section">
                        <div class="date-header">
                            <el-button type="text" size="small" @click="changeYear(-1)">
                                <el-icon>
                                    <DArrowLeft />
                                </el-icon>
                            </el-button>
                            <el-button type="text" size="small" @click="changeMonth(-1)">
                                <el-icon>
                                    <ArrowLeft />
                                </el-icon>
                            </el-button>
                            <span class="current-month">{{ currentYear }} 年 {{ currentMonth + 1 }} 月</span>
                            <el-button type="text" size="small" @click="changeMonth(1)">
                                <el-icon>
                                    <ArrowRight />
                                </el-icon>
                            </el-button>
                            <el-button type="text" size="small" @click="changeYear(1)">
                                <el-icon>
                                    <DArrowRight />
                                </el-icon>
                            </el-button>
                        </div>
                        <div class="divider-h"></div>
                        <div class="weekdays">
                            <span v-for="day in weekdays" :key="day" class="weekday">{{ day }}</span>
                        </div>

                        <div class="calendar-grid">
                            <div v-for="date in calendarDates" :key="`${date.year}-${date.month}-${date.day}`"
                                class="calendar-day" :class="{
                                    'other-month': !date.isCurrentMonth,
                                    'selected': isSelectedDate(date),
                                    'today': isToday(date)
                                }" @click="selectDate(date)">
                                {{ date.day }}
                                <div v-if="isSelectedDate(date)" class="selected-dot"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 分隔线 -->
                    <div class="divider"></div>

                    <!-- 时间选择器部分 -->
                    <div class="time-section">
                        <div class="time-display">{{ formatTime }}</div>
                        <div class="divider-h"></div>
                        <div class="time-selector">
                            <div class="time-column hide-scrollbar" ref="hourColumn">
                                <div v-for="hour in hours" :key="hour" :data-index="hour" class="time-item"
                                    :class="{ 'selected': selectedHour === hour }" @click="selectHour(hour)">
                                    {{ hour.toString().padStart(2, '0') }}
                                </div>
                            </div>
                            <div class="time-separator">:</div>
                            <div class="time-column hide-scrollbar" ref="minuteColumn">
                                <div v-for="minute in minutes" :key="minute" :data-index="minute" class="time-item"
                                    :class="{ 'selected': selectedMinute === minute }" @click="selectMinute(minute)">
                                    {{ minute.toString().padStart(2, '0') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="footer">
                    <el-button type="text" @click="setToday">今天</el-button>
                    <el-button type="primary" @click="confirm">确定</el-button>
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { nextTick } from 'vue'
import { Calendar, ArrowLeft, ArrowRight, DArrowLeft, DArrowRight } from '@element-plus/icons-vue'
import dayjs from 'dayjs'

const props = defineProps({
    modelValue: {
        type: [Date, String],
        default: null
    },
    placeholder: {
        type: String,
        default: '请选择日期时间'
    },
    clearable: {
        type: Boolean,
        default: true
    },
    format: {
        type: String,
        default: 'YYYY-MM-DD HH:mm'
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 响应式数据
const visible = ref(false)
const currentYear = ref(new Date().getFullYear())
const currentMonth = ref(new Date().getMonth())
const selectedDate = ref(null)
const selectedHour = ref(new Date().getHours())
const selectedMinute = ref(new Date().getMinutes())
const hourColumn = ref(null)
const minuteColumn = ref(null)

// 常量
const weekdays = ['日', '一', '二', '三', '四', '五', '六']

// 计算属性
const displayValue = computed(() => {
    if (!selectedDate.value) return ''
    const date = dayjs(selectedDate.value)
    return date.format(props.format)
})

const formatTime = computed(() => {
    return `${selectedHour.value.toString().padStart(2, '0')}:${selectedMinute.value.toString().padStart(2, '0')}`
})

const hours = computed(() => {
    return Array.from({ length: 24 }, (_, i) => i)
})

const minutes = computed(() => {
    return Array.from({ length: 60 }, (_, i) => i)
})

const calendarDates = computed(() => {
    const dates = []
    const firstDay = new Date(currentYear.value, currentMonth.value, 1)
    const lastDay = new Date(currentYear.value, currentMonth.value + 1, 0)

    // 获取上个月的最后几天
    const firstDayWeek = firstDay.getDay()
    for (let i = firstDayWeek - 1; i >= 0; i--) {
        const date = new Date(currentYear.value, currentMonth.value, -i)
        dates.push({
            year: date.getFullYear(),
            month: date.getMonth(),
            day: date.getDate(),
            isCurrentMonth: false
        })
    }

    // 当前月的所有日期
    for (let i = 1; i <= lastDay.getDate(); i++) {
        dates.push({
            year: currentYear.value,
            month: currentMonth.value,
            day: i,
            isCurrentMonth: true
        })
    }

    // 下个月的前几天，补齐 6 行 * 7 列
    const remainingDays = 42 - dates.length
    for (let i = 1; i <= remainingDays; i++) {
        const date = new Date(currentYear.value, currentMonth.value + 1, i)
        dates.push({
            year: date.getFullYear(),
            month: date.getMonth(),
            day: date.getDate(),
            isCurrentMonth: false
        })
    }

    return dates
})

// 将选中项滚动到顶部
const scrollHourIntoViewTop = (hour) => {
    const el = hourColumn.value
    if (!el) return
    const node = el.querySelector(`.time-item[data-index="${hour}"]`)
    if (node) el.scrollTop = node.offsetTop - 60
}

const scrollMinuteIntoViewTop = (minute) => {
    const el = minuteColumn.value
    if (!el) return
    const node = el.querySelector(`.time-item[data-index="${minute}"]`)
    if (node) el.scrollTop = node.offsetTop - 60
}

// 方法
const changeYear = (delta) => {
    currentYear.value += delta
}

const changeMonth = (delta) => {
    let newMonth = currentMonth.value + delta
    if (newMonth < 0) {
        newMonth = 11
        currentYear.value--
    } else if (newMonth > 11) {
        newMonth = 0
        currentYear.value++
    }
    currentMonth.value = newMonth
}

const selectDate = (date) => {
    if (!date.isCurrentMonth) return
    selectedDate.value = new Date(date.year, date.month, date.day)
}

const selectHour = (hour) => {
    selectedHour.value = hour
    nextTick(() => scrollHourIntoViewTop(hour))
}

const selectMinute = (minute) => {
    selectedMinute.value = minute
    nextTick(() => scrollMinuteIntoViewTop(minute))
}

const isSelectedDate = (date) => {
    if (!selectedDate.value) return false
    return date.year === selectedDate.value.getFullYear() &&
        date.month === selectedDate.value.getMonth() &&
        date.day === selectedDate.value.getDate()
}

const isToday = (date) => {
    const today = new Date()
    return date.year === today.getFullYear() &&
        date.month === today.getMonth() &&
        date.day === today.getDate()
}

const setToday = () => {
    const today = new Date()
    selectedDate.value = new Date(today.getFullYear(), today.getMonth(), today.getDate())
    selectedHour.value = today.getHours()
    selectedMinute.value = today.getMinutes()
    currentYear.value = today.getFullYear()
    currentMonth.value = today.getMonth()
}

const confirm = () => {
    if (!selectedDate.value) {
        selectedDate.value = new Date()
    }

    const result = new Date(
        selectedDate.value.getFullYear(),
        selectedDate.value.getMonth(),
        selectedDate.value.getDate(),
        selectedHour.value,
        selectedMinute.value
    )
    console.log(result)
    emit('update:modelValue', result)
    emit('change', result)
    visible.value = false
}

const handleClear = () => {
    selectedDate.value = null
    emit('update:modelValue', null)
    emit('change', null)
}

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
    if (newValue) {
        const date = dayjs(newValue)
        selectedDate.value = date.toDate()
        selectedHour.value = date.hour()
        selectedMinute.value = date.minute()
        currentYear.value = date.year()
        currentMonth.value = date.month()
    } else {
        selectedDate.value = null
    }
}, { immediate: true })

// 弹出层打开时自动滚动到当前选中项顶端
watch(visible, (val) => {
    if (val) {
        nextTick(() => {
            scrollHourIntoViewTop(selectedHour.value)
            scrollMinuteIntoViewTop(selectedMinute.value)
        })
    }
})

// 初始化
onMounted(() => {
    if (!props.modelValue) {
        setToday()
    }
})
</script>

<style lang="scss">
.lx-date-time-picker {
    display: inline-block;
}

.lx-date-time-picker-popover {
    padding: 0 !important;

    .date-time-panel {
        display: flex;
        flex-direction: column;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .main-content {
        display: flex;
        flex: 1;
    }

    .date-section {
        padding: 16px;
        flex: 1;
    }

    .date-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16px;
    }

    .current-month {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
    }

    .weekdays {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
        margin-bottom: 8px;
    }

    .weekday {
        text-align: center;
        font-size: 12px;
        color: #909399;
        padding: 8px 0;
    }

    .calendar-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 4px;
    }

    .calendar-day {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        font-size: 14px;
        color: #303133;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.2s;
    }

    .calendar-day:hover {
        background-color: #f5f7fa;
    }

    .calendar-day.other-month {
        color: #c0c4cc;
    }

    .calendar-day.selected {
        background-color: #409eff;
        color: white;
    }

    .calendar-day.today {
        font-weight: 500;
        color: #409eff;
    }

    .calendar-day.selected.today {
        color: white;
    }

    .selected-dot {
        position: absolute;
        bottom: 2px;
        width: 4px;
        height: 4px;
        background-color: currentColor;
        border-radius: 50%;
    }

    .divider {
        width: 1px;
        background-color: #e4e7ed;
        margin: 0;
    }

    .divider-h {
        height: 1px;
        background-color: #e4e7ed;
        margin: 0;
    }

    .time-section {
        padding: 16px 0;
        display: flex;
        flex-direction: column;
        /* align-items: center; */
        text-align: center;
        min-width: 120px;
    }

    .time-display {
        font-size: 18px;
        font-weight: 500;
        color: #303133;
        margin-bottom: 16px;
    }

    .time-selector {
        display: flex;
        align-items: center;
        gap: 2px;
        height: 100%;
    }

    .time-column {
        display: flex;
        flex-direction: column;
        max-height: 280px;
        overflow-y: auto;
        border-radius: 4px;
    }

    .time-item {
        padding: 8px 4px;
        font-size: 14px;
        color: #303133;
        cursor: pointer;
        text-align: center;
        transition: all 0.2s;
        min-width: 40px;
    }

    .time-item:hover {
        background-color: #f5f7fa;
    }

    .time-item.selected {
        background-color: #409eff;
        color: white;
    }

    .time-separator {
        font-size: 16px;
        font-weight: 500;
        color: #303133;
    }

    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        border-top: 1px solid #e4e7ed;
        background-color: #fafafa;
    }

    /* 滚动条样式 */
    .time-column::-webkit-scrollbar {
        width: 4px;
    }

    .time-column::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .time-column::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .time-column::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
}
</style>
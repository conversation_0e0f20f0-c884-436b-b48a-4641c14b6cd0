<template>
  <div class="ability-model-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">新建</el-button>
        <el-button @click="openStandardDialog">达标分值设置</el-button>
      </template>
      <template #col_modelName="{ row }">
        <div class="model-name">{{ row.name }}</div>
      </template>

      <template #col_relatedTaskItems="{ row }">
        <div class="related-abilities">
          <div v-for="(line, index) in row.items" :key="index" class="ability-line">
            {{ index + 1 }}. {{ line.dimensionName }}
          </div>
        </div>
      </template>

      <template #col_testScore="{ row }">
        <div class="test-score">{{ row.testScore }}</div>
      </template>

      <template #col_createdUserName="{ row }">
        <div>{{ row.createdUserName }}</div>
      </template>

      <template #col_createdTime="{ row }">
        <div>{{ row.createdTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <TaskModelFormDrawer ref="formDrawerRef" v-if="formDrawerShow" :dimensionOptions="dimensionOptions" @success="handleFormSuccess" @close="formDrawerShow = false" />
    <StandardDialog ref="refStandardDialog" @callback="cbFunc" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import TaskModelFormDrawer from "./TaskModelFormDrawer.vue";
import { getDimension, getModels, deleteModel, createModel, updateModel, getStandardSetting } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import StandardDialog from "./StandardDialog.vue";

const formDrawerShow = ref(false)
const refTable = ref();
const formDrawerRef = ref();
const refStandardDialog = ref();
const tableConfig = reactive({
  tableid: 'task_model_1',
  param: {
    type: "TASK",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  delete_hint_column: 'modelName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  sortable: "custom",
  sortables: ["updateTime"],
  columns: ["modelName", "relatedTaskItems", "createdUserName", "createdTime"],
  template: ["modelName", "relatedTaskItems"],
  urlGet: getModels,
  urlDelete: deleteModel
});

const dimensionOptions = ref();
const standardSetting = ref();
const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  formDrawerShow.value = true
  nextTick(() => {
    formDrawerRef.value.openDrawer();
  })
};

const handleEdit = (row) => {
  formDrawerShow.value = true
  nextTick(() => {
  formDrawerRef.value.openDrawer(row);
  })
};

const handleFormSuccess = (result) => {
  const { mode, data } = result;

  if (mode === 'add') {
    // 处理添加逻辑
    const params = {
      name: data.name,
      items: data.items,
      type: 'TASK'
    };

    createModel(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('添加成功');
        refTable.value.search();
      } else {
        ElMessage.error(`添加失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error(`添加失败: ${err.response.data.message}`);
    });
  } else if (mode === 'edit') {
    // 处理编辑逻辑
    const params = {
      id: data.id,
      name: data.name,
      items: data.items,
      type: 'TASK'
    };

    updateModel(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('编辑成功');
        refTable.value.search();
      } else {
        ElMessage.error(`编辑失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error(`编辑失败: ${err.response.data.message}`);
    });
  }
};

const handleDelete = (row) => {
  confirmDelete(row.name, (status) => {
    if (status) {
      deleteModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      });
    }
  });
};

const openStandardDialog = () => {
  refStandardDialog.value.openDrawer({ targetSettings: standardSetting.value });
};
const getRelatedTaskItems = () => {
  getStandardSetting().then((resp) => {
    if (resp.code == 0) {
      standardSetting.value = (resp.data || {}).taskRate4Pass || 0
    }
  });
}

const cbFunc = (result) => {
  getRelatedTaskItems()
  refTable.value.search();
};



defineExpose({
  refTable
});

onMounted(() => {
  getDimension({ type: "TASK", pageSize: 999 }).then((resp) => {
    if (resp.code == 0) {
      dimensionOptions.value = resp.data.datas || []
    }
  });
  getRelatedTaskItems()
});
</script>

<style lang="scss" scoped>
.ability-model-table {
  padding: 20px 4px;

  :deep(.table_class) {
    height: calc(100vh - 250px) !important;

    table {
      min-width: 1000px !important;

      .col_operation_ {
        width: 200px;
      }
    }
  }


  .related-abilities {
    max-width: 400px;

    .ability-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .test-score {
    color: #333;

  }
}
</style>
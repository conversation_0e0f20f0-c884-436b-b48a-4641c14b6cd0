<template>
    <svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1">
        <g id="沟通过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="总结" transform="translate(-1134.000000, -90.000000)">
                <g id="沟通内容" transform="translate(640.000000, 72.000000)">
                    <g id="编组-12" transform="translate(312.000000, 12.000000)">
                        <g id="编组-6备份-2" transform="translate(176.000000, 0.000000)">
                            <g id="Icon/16px/人脸识别" transform="translate(6.000000, 6.000000)">
                                <rect id="矩形" x="0" y="0" width="20" height="20"></rect>
                                <path
                                    d="M17,1.38 C18.4497475,1.38 19.625,2.55525253 19.625,4.005 L19.625,5.999 L18.375,5.999 L18.375,4.005 C18.375,3.24560847 17.7593915,2.63 17,2.63 L3,2.63 C2.24060847,2.63 1.625,3.24560847 1.625,4.005 L1.625,11.005 C1.625,11.7643915 2.24060847,12.38 3,12.38 L6,12.38 L6,13.629 L3,13.63 C1.55025253,13.63 0.375,12.4547475 0.375,11.005 L0.375,4.005 C0.375,2.55525253 1.55025253,1.38 3,1.38 L17,1.38 Z"
                                    id="形状结合" fill="currentColor" fill-rule="nonzero"></path>
                                <path
                                    d="M17,7.38 C18.4497475,7.38 19.625,8.55525253 19.625,10.005 L19.625,16.005 C19.625,17.4547475 18.4497475,18.63 17,18.63 L10,18.63 C8.55025253,18.63 7.375,17.4547475 7.375,16.005 L7.375,10.005 C7.375,8.55525253 8.55025253,7.38 10,7.38 L17,7.38 Z M17,8.63 L10,8.63 C9.24060847,8.63 8.625,9.24560847 8.625,10.005 L8.625,16.005 C8.625,16.7643915 9.24060847,17.38 10,17.38 L17,17.38 C17.7593915,17.38 18.375,16.7643915 18.375,16.005 L18.375,10.005 C18.375,9.24560847 17.7593915,8.63 17,8.63 Z"
                                    id="矩形" fill="currentColor" fill-rule="nonzero"></path>
                                <path
                                    d="M15.5,10.38 C15.845178,10.38 16.125,10.659822 16.125,11.005 C16.125,11.350178 15.845178,11.63 15.5,11.63 L14.124,11.63 L14.125,15.505 C14.125,15.850178 13.845178,16.13 13.5,16.13 C13.154822,16.13 12.875,15.850178 12.875,15.505 L12.874,11.63 L11.5,11.63 C11.154822,11.63 10.875,11.350178 10.875,11.005 C10.875,10.659822 11.154822,10.38 11.5,10.38 L15.5,10.38 Z"
                                    id="形状结合" fill="currentColor" fill-rule="nonzero"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>

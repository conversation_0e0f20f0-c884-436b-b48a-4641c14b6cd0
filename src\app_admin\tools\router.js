const adminRouter = [
  {
    path: "/admin",
    component: () => import("@/app_admin/components/Layout.vue"),
    children: [
      {
        path: "pre_prepare",
        component: () => import("@/app_admin/views/prePrepare/PrePrepare.vue"),
        meta: {
          code: "sales_before_visit",
          name: "访前准备",
        },
      },
      {
        path: "post_analysis",
        component: () =>
          import("@/app_admin/views/postAnalysis/PostAnalysis.vue"),
        meta: {
          code: "sales_after_visit",
          name: "访后分析",
        },
      },
      // {
      //   path: "template",
      //   component: () => import("@/app_admin/views/template/TemplateList.vue"),
      //   meta: {
      //     code: 'sales_solution_template',
      //     name: '模板管理'
      //   }
      // },
      // {
      //   path: "template/edit/:id",
      //   component: () => import("@/app_admin/views/template/Edit.vue"),
      // },
      // {
      //   path: "template/use_records/:id",
      //   component: () => import("@/app_admin/views/template/UseRecord.vue"),
      // },
      {
        path: "category",
        component: () => import("@/app_admin/views/category/categorys.vue"),
        meta: {
          code: "sales_solution_classify",
          name: "分类管理",
        },
      },
      {
        path: "category/list/:id",
        component: () => import("@/app_admin/views/category/CateList.vue"),
      },
      {
        path: "risk",
        component: () => import("@/app_admin/views/risk"),
        meta: {
          code: "risk_notice",
          name: "风险预警",
        },
      },
      {
        path: "doc",
        component: () => import("@/app_admin/views/doc/DocList.vue"),
        meta: {
          code: "sales_solution_file",
          name: "文件管理",
        },
      },
      {
        path: "commodity",
        component: () => import("@/app_admin/views/commodity"),
        meta: {
          code: "sales_product_manage",
          name: "商品管理",
        },
      },
      {
        path: "usage",
        component: () => import("@/app_admin/views/usage"),
        meta: {
          code: "usage_status",
          name: "使用情况",
        },
      },
      {
        path: "global_config",
        component: () =>
          import("@/app_admin/views/globalConfig/GlobalConfig.vue"),
        meta: {
          code: "global_config",
          name: "全局配置",
        },
      },
      {
        path: "library",
        component: () => import("@/app_admin/views/library/Library.vue"),
        meta: {
          code: "library",
          name: "案例库",
        },
      },
      {
        path: "customer_type",
        component: () =>
          import("@/app_admin/views/customerType/CustomerType.vue"),
        meta: {
          code: "customerType",
          name: "客户类型",
        },
      },
      {
        path: "customer_list",
        component: () =>
          import("@/app_admin/views/customerList/CustomerList.vue"),
        meta: {
          code: "customerList",
          name: "客户列表",
        },
      },
      {
        path: "customer_field_set",
        component: () =>
          import("@/app_admin/views/customerField/CustomerField.vue"),
        meta: {
          code: "customerField",
          name: "客户字段设置",
        },
      },
      {
        path: "ability_model",
        component: () =>
          import("@/app_admin/views/communicate/AbilityModel/AbilityModel.vue"),
        meta: {
          code: "sales_ability_model",
          name: "能力模型",
        },
      },
      {
        path: "task_model",
        component: () =>
          import("@/app_admin/views/communicate/TaskModel/TaskModel.vue"),
        meta: {
          code: "sales_task_model",
          name: "任务模型",
        },
      },
      {
        path: "communication_summary",
        component: () =>
          import(
            "@/app_admin/views/communicate/CommunicationSummary/CommunicationSummary.vue"
          ),
        meta: {
          code: "communication_summary",
          name: "沟通总结",
        },
      },
      {
        path: "communication_analysis",
        component: () =>
          import(
            "@/app_admin/views/communicate/CommunicationAnalysis/CommunicationAnalysis.vue"
          ),
        meta: {
          code: "communication_analysis",
          name: "沟通分析",
        },
      },
      {
        path: "communication_process",
        component: () =>
          import(
            "@/app_admin/views/communicate/CommunicationProcess/CommunicationProcess.vue"
          ),
        meta: {
          code: "communication_process",
          name: "沟通流程",
        },
      },
      {
        path: "competitor",
        component: () =>
          import("@/app_admin/views/competitor/CompetitorList.vue"),
        meta: {
          code: "competitor_manage",
          name: "竞争对手管理",
        },
      },
      {
        path: "opportunity_field_settings",
        component: () =>
          import(
            "@/app_admin/views/customerField/OpportunityField.vue"
          ),
        meta: {
          code: "opportunity_field_settings",
          name: "字段设置",
        },
      },
      {
        path: "opportunity_page_settings",
        component: () =>
          import("@/app_admin/views/userManage/pageSettings/PageSettings.vue"),
        meta: {
          code: "opportunity_page_settings",
          name: "页面设置",
        },
      },
      {
        path: "/admin/competitor",
        name: "CompetitorList",
        component: () =>
          import("@/app_admin/views/competitor/CompetitorList.vue"),
        meta: { title: "竞争对手管理" },
      },
      {
        path: "risk_warning",
        component: () => import("@/app_admin/views/risk/Risk.vue"),
        meta: { title: "风险预警" },
      },
    ],
  },
  {
    path: "/manage",
    component: () => import("@/app_admin/components/Layout.vue"),
    children: [
      {
        path: "department",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_department",
          name: "部门管理",
        },
      },
      {
        path: "employee",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_orgoumgmt",
          name: "员工名册",
        },
      },
      {
        path: "account",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_account_recovery",
          name: "恢复账号",
        },
      },
      {
        path: "position",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_positionmgmt",
          name: "岗位管理",
        },
      },
      {
        path: "rank",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_position",
          name: "职级管理",
        },
      },
      {
        path: "extendfield",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_extend_field",
          name: "扩展字段",
        },
      },
      {
        path: "role",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "core_rolepermission",
          name: "角色权限",
        },
      },

      {
        path: "teamSetting",
        component: () => import("@/app_admin/views/team/TeamList.vue"),
        meta: {
          code: "udp_teamSettings",
          name: "团队设置",
        },
      },
      {
        path: "usergroup",
        component: () => import("@/app_admin/views/manage"),
        meta: {
          code: "udp_usergroup",
          name: "用户组管理",
        },
      },
    ],
  },
  {
    path: "/vp/:templateId/:docId",
    component: () => import("@/app_admin/views/View.vue"),
  },
  {
    path: "/ve/:url_encode",
    component: () => import("@/app_admin/views/MsView.vue"),
  },
];

export default adminRouter;

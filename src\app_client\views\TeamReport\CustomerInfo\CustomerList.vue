<template>
  <div class="team_customer_report_wrap">
    <el-card class="customer_card card_no_border" shadow="never">
      <MyTable ref="refTable" :cfg="datas" class="dl_wrap">
        <template #_header_left>
        </template>

        <template #expand_row="{ row }">
          <div class="sub_visit_list_wrap">
            <SubVisitList :row="row" />
          </div>
        </template>

        <template #_header_filter>
          <DrawerSelectMixed @callback="onSelectUser" ref="refUser" type="user" :rootDeptId="rootDeptId" />
        </template>

        <template #col_customer_name="{ row }">
          <CustomerName :row="row" />
        </template>

        <template #col_confCount="{ row }">
          <div class="link">
            {{ formatCustNum(row.confCount) }}
          </div>
        </template>

        <template #col_salesName="{ row }">
          {{ row.salesName }}
        </template>

        <template #col_deptName="{ row }">
          <ColDept :name="row.deptName" />
        </template>

        <template #col_visitTargetAchievementRate="{ row }">
          {{ row.visitTargetAchievementRate }}%
        </template>
      </MyTable>
    </el-card>
  </div>
</template>

<script setup>
import { getCustomerList } from "@/app_client/tools/api.js";
import { formatCustNum } from "@/app_client/tools/utils.js";
import { getUrlParam } from "@/js/utils.js";
import MyTable from "@/components/Table.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import CustomerName from "@/app_client/components/CustomerName.vue";
import ColDept from "@/app_client/components/ColDept.vue";
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import SubVisitList from "./SubVisitList.vue";

const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});
const emit = defineEmits(["callback"]);
const refTable = ref();
const tags = ref([]);
// {
// 				"confCount":1,
// 				"customerId":"1872112282486480896",
// 				"customerName":"检科测试集团有限公司",
// 				"deptName":"Xmate-测试",
// 				"salesName":"超级管理员-修改"
// 			},
const columns = ["customer_name", "confCount", "salesName", "deptName", "visitTargetAchievementRate"]
const rootDeptId = computed(() => {
  return g.clientBoardStore.getTopDeptId();
});
const _getKanbanCustomers = (p) => {
  if (typeof p.dptIds == "string") {
    p["dptIds"] = [p["dptIds"]];
  }
  const user = g.appStore.user;

  return new Promise((resolve, reject) => {
    getCustomerList(user.orgId, user.id, p).then(resp => {
      const data = resp.data.datas;
      data.forEach(item => {
        item.name = item.customerName;
      });
      resolve({ code: 0, data: { datas: data, totalNum: resp.data.totalNum } });
    }).catch(reject);
  });
};

const startDate = getUrlParam('startDate');
const endDate = getUrlParam('endDate');
const datas = reactive({
  tableid: "team_customer_report",
  param: {
    categoryIds: [],
    startTime: startDate + ' 00:00:00', //开始时间，注间时间格式 YYYY-mm-dd HH24:mi:ss
    endTime: endDate + ' 23:59:59', //结束时间
    tags: [], //标签
    dptIds: [],
    ssoUserIds: [],
    orderBy: "confCount",
    asc: false,
  },
  need_init_load: false,
  show_search: true,
  need_header: true,
  need_expand_column: true,
  form: {},
  sortable: "custom",
  modal_type: "link",
  search_ph: "请输入客户名称搜索",
  show_link_column: false,
  columns: columns,
  template: columns,
  urlGet: _getKanbanCustomers,
});

const onSelectUser = (action, data) => {
  datas.param.ssoUserIds = data.users.map((x) => x.ssoUserId);
  onSearch();
};


const getCompetitorNames = (row) => {
  const item = toRaw(row);
  if (item.competitorNames) {
    return item.competitorNames.split(",").filter((x) => !!x);
  } else {
    return [];
  }
};

const onSearch = () => {
  refTable.value.search();
};

watch(() => g.clientBoardStore.getTopDeptId(), (data) => {
  datas.param.dptIds = g.clientBoardStore.getTopDeptId()
  onSearch();
});

onMounted(() => {
});

defineExpose({
  MyTable,
  refTable,
  BtnDate,
  getCompetitorNames,
  CustomerName,
  SubVisitList,
  props,
  ColDept,
});
</script>

<style lang="scss" scoped>
.team_customer_report_wrap {
  background: #f7f9fe;

  :deep(table) {
    min-width: 1000px !important;
  }

  .table_wrap {
    margin: 4px;
  }

  :deep(.search_box) {
    .search_input {
      width: 215px !important;
    }
  }

  :deep(.table_class) {

    .el-scrollbar {
      // height: unset !important;
      overflow: auto;
    }

    table {
      border-collapse: collapse;

      :deep(td:first-child) {
        border-right: 2px solid black;
      }

      .link {
        cursor: pointer;
      }
    }
  }

  .sub_visit_list_wrap {
    width: 1200px;
  }
}
</style>

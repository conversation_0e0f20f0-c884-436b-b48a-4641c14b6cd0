<template>
    <div class="lx-multi-select">
        <lx-select-main v-model="copyNewVal" :option="options" :placeholder="placeholder" ref="lxSelectRef">
            <div class="multi-select-content">
                <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"
                    class="check-all-item">
                    全选
                </el-checkbox>
                <el-checkbox-group v-model="selectedValues" @change="handleCheckedChange" class="checkbox-group">
                    <el-checkbox v-for="option in options" :key="option.value" :label="option.value"
                        :value="option.value" class="checkbox-item ">
                        {{ option.label }}
                    </el-checkbox>
                </el-checkbox-group>
                <div class="footer">
                    <el-button type="primary" @click="handleConfirm">确定</el-button>
                </div>
            </div>
        </lx-select-main>
    </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import LxSelectMain from './LxSelectMain.vue'

const props = defineProps({
    modelValue: {
        type: Array,
        default: () => []
    },
    options: {
        type: Array,
        default: () => []
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    max: {
        type: Number,
        default: 0
    },
    min: {
        type: Number,
        default: 0
    }
})

const emit = defineEmits(['update:modelValue', 'change', 'confirm'])

const selectedValues = ref([...props.modelValue])
const copyNewVal = ref([...props.modelValue])
const checkAll = ref(false)
const lxSelectRef = ref(null)

// 计算是否为半选状态
const isIndeterminate = computed(() => {
    const checkedCount = selectedValues.value.length
    return checkedCount > 0 && checkedCount < props.options.length
})
// 更新全选状态
const updateCheckAllState = () => {
    const checkedCount = selectedValues.value.length
    checkAll.value = checkedCount === props.options.length
}
// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
    copyNewVal.value = [...newVal]
    selectedValues.value = [...newVal]
    updateCheckAllState()
}, { immediate: true })



// 处理全选
const handleCheckAllChange = (checked) => {
    if (checked) {
        selectedValues.value = props.options.map(item => item.value)
    } else {
        selectedValues.value = []
    }
}

// 处理单项选择
const handleCheckedChange = () => {
    updateCheckAllState()
}

// 处理确定按钮
const handleConfirm = () => {
    if (props.max && selectedValues.value.length > props.max) {
        ElMessage.warning(`最多选择${props.max}个`)
        return
    }
    if (props.min && selectedValues.value.length < props.min) {
        ElMessage.warning(`最少选择${props.min}个`)
        return
    }
    lxSelectRef.value.handleSelect(selectedValues.value)
    emit('update:modelValue', selectedValues.value)
    emit('change', selectedValues.value)
}
</script>

<style lang="scss" scoped>
.lx-multi-select {
    width: 100%;

    :deep(.lx-select) {
        width: 100%;
        box-sizing: border-box;
    }

    :deep(.lx-select-dropdown) {
        width: 100%;
        box-sizing: border-box;
        padding: 10px 4px;
        max-height: 300px;
    }

    .multi-select-content {
        width: 100%;
        box-sizing: border-box;

        .check-all-item {
            box-sizing: border-box;
            width: 100%;
            padding: 8px 16px;
            border-bottom: 1px solid #f0f0f0;


        }

        .checkbox-group {
            display: flex;
            flex-direction: column;
            height: 200px;
            overflow-y: auto;

            .checkbox-item {
                margin: 0;
                padding: 8px 16px;
                box-sizing: border-box;
                width: 100%;

                :deep(.el-checkbox__label) {
                    color: #606266;
                    font-size: 14px;
                    font-weight: 400;
                    display: block;
                    width: 100%;
                    box-sizing: border-box;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
                    background-color: #436BFF;
                    border-color: #436BFF;
                }

                &:hover {
                    background-color: #f5f7fa;
                    border-radius: 4px;
                }
            }
        }

        .footer {
            padding: 8px 8px 0 8px;
            border-top: 1px solid #f0f0f0;
            display: flex;
            justify-content: flex-end;

            .el-button {
                background: #436BFF;
                border-color: #436BFF;
            }
        }
    }
}
</style>
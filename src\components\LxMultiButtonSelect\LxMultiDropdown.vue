<template>
    <div class="lx-select" v-click-outside="closeDropdown">

        <div class="lx-select-input">
            <slot name="label" :toggle-dropdown="toggleDropdown"></slot>
        </div>
        <div class="lx-select-dropdown" v-show="isOpen">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { vClickOutside } from '@/components/directives/clickOutside'

const props = defineProps({
    modelValue: {
        type: [String, Number, Array],
        default: ''
    },
    placeholder: {
        type: String,
        default: '请选择'
    },
    clearable: {
        type: Boolean,
        default: false
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const isOpen = ref(false)


const toggleDropdown = () => {
    console.log('toggleDropdown')
    isOpen.value = !isOpen.value
}

const closeDropdown = () => {
    isOpen.value = false
}

const handleSelect = (value, label) => {
    emit('update:modelValue', value)
    emit('change', value)
    closeDropdown()
}

defineExpose({
    handleSelect,
    toggleDropdown
})
</script>

<style lang="scss" scoped>
.lx-select {
    position: relative;
    width: 100%;
    user-select: none;



    &-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        max-height: 350px;
        margin: 5px 0;
        background: #FFFFFF;
        box-shadow: 0px 0px 16px 0px rgba(0, 0, 0, 0.08);
        border-radius: 8px 8px 8px 8px;
        z-index: 1000;
        overflow-y: auto;
    }
}
</style>
import { keyPre, ConstValue } from "@/js/const_value.js";

export const setStore = (key, value) => {
  try {
    if (typeof value == "object") {
      value = JSON.stringify(value);
    }
    localStorage.setItem(keyPre + key, value);
  } catch (e) {
    return value;
  }
};

export const getStore = (key, value = "") => {
  try {
    const res = localStorage.getItem(keyPre + key) || value;
    if (res.indexOf("{") > -1 || res.indexOf("[") > -1) {
      return JSON.parse(res);
    } else {
      return res;
    }
  } catch (e) {
    return value;
  }
};

export const removeStore = (key) => {
  localStorage.removeItem(keyPre + key);
};

export const getUser = () => {
  const user = getStore(ConstValue.keyUserInfo, "{}");
  return user;
};

// 检查键是否存在
export const checkStoreExists = (key) => {
  var value = localStorage.getItem(key);
  return value !== null;
};

export const clearStore = () => {
  removeStore(ConstValue.keyUserInfo);
  removeStore(ConstValue.keyYxtUserInfo);
  removeStore(ConstValue.keyOrgInfo);
  removeStore(ConstValue.keyMenuPermission);
  removeStore(ConstValue.keyToken);

  const keys = [
    "token",
    "yxt-userInfo",
    "store",
    "domain",
    "share_token",
    "fast-token",
    "mobile",
    "userId",
    "username",
    "fullname",
  ];

  for (let key of keys) {
    localStorage.removeItem(key);
  }

  // 删除所有 yxtlm_* 相关缓存
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.includes("yxtlm_")) {
      localStorage.removeItem(key);
    }
  }
};

export const setStoreEveryKey = (obj) => {
  for (let key in obj) {
    let value = obj[key];
    // 如果value是对象，则转为JSON字符串
    if (typeof value === "object") {
      value = JSON.stringify(value);
    }
    localStorage.setItem(key, obj[key], value);
  }
};

export const isAdmin = (menus) => {
  if (!menus) {
    return false;
  }
  return menus.filter((x) => x.showed).length > 0;
};

//openParam: {urlParam:{id,url},newPageData:any}
export const openWindow = (openParam) => {
  console.log("openWindow", openParam);
  if (openParam.urlParam.url) {
    if (typeof g.ipcRenderer !== "undefined") {
      if (openParam.urlParam.url.indexOf("electron") > -1) {
        g.ipcRenderer.send("create_window", JSON.stringify(openParam));
      } else {
        g.ipcRenderer.send("open_url", openParam.urlParam.url);
      }
    } else {
      jsOpenNewWindow(openParam.urlParam.url);
    }
  } else {
    console.error("no param url", openParam.urlParam);
  }
};

// 添加以下函数
export function formatDate(fdate, format = "YYYY-MM-dd") {
  if (!fdate) {
    return "";
  }

  // 处理日期字符串的兼容性问题
  if (typeof fdate === "string") {
    // 替换连字符为斜杠以提高跨浏览器兼容性
    fdate = fdate.replace(/-/g, "/");
  }

  // 创建日期对象并验证
  const dateObj = new Date(fdate);
  if (isNaN(dateObj.getTime())) {
    console.error("Invalid date:", fdate);
    return "";
  }

  var date = {
    "Y+": dateObj.getFullYear(),
    "M+": dateObj.getMonth() + 1,
    "d+": dateObj.getDate(),
    "h+": dateObj.getHours(),
    "m+": dateObj.getMinutes(),
    "s+": dateObj.getSeconds(),
    "q+": Math.floor((dateObj.getMonth() + 3) / 3),
    "S+": dateObj.getMilliseconds(),
    "w+": ["日", "一", "二", "三", "四", "五", "六"][dateObj.getDay()],
  };

  if (/(y+)/i.test(format)) {
    format = format.replace(
      RegExp.$1,
      (dateObj.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }

  for (var k in date) {
    if (new RegExp("(" + k + ")").test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1
          ? date[k]
          : ("00" + date[k]).substr(("" + date[k]).length)
      );
    }
  }
  return format;
}

export function now(dt_dormat = "yyyy-MM-dd hh:mm:ss") {
  return formatDate(new Date(), dt_dormat);
}

//添加一个函数，返回离现在最新的目录最近一的一个整10分钟的点，比如现在 2024-10-23 15:21：23，应该返回2024-10-23 15:30:00
export function nowMNAfter(dt_format = "yyyy-MM-dd hh:mm:ss", m = 10, n = 0) {
  const nowDate = new Date();
  nowDate.setMinutes(Math.ceil(nowDate.getMinutes() / m) * m + n);
  nowDate.setSeconds(0);
  nowDate.setMilliseconds(0);
  return formatDate(nowDate, dt_format);
}

export function nDate(n = 0, format = "YYYY-MM-dd") {
  return formatDate(addDays(new Date(), n), format);
}

export function addDays(date, n) {
  var result = new Date(date);
  result.setDate(result.getDate() + n);
  return result;
}

export function formatDuration(duration, autoHideHour = false) {
  // 计算小时、分钟和秒数
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = Math.floor(duration % 60);

  // 格式化时间字符串
  const formattedHours = String(hours).padStart(2, "0") + ":";
  const formattedMinutes = String(minutes).padStart(2, "0");
  const formattedSeconds = String(seconds).padStart(2, "0");
  if (autoHideHour && hours == 0) {
    return `${formattedMinutes}:${formattedSeconds}`;
  }

  // 返回格式化后的时间字符串
  return `${formattedHours}${formattedMinutes}:${formattedSeconds}`;
}

export function deepMerge(obj1, obj2) {
  for (let key in obj2) {
    obj1[key] =
      obj1[key] && obj1[key].toString() === "[object Object]"
        ? deepMerge(obj1[key], obj2[key])
        : (obj1[key] = obj2[key]);
  }
  return obj1;
}

export function require_factory(cols) {
  let res = {};
  for (let col of cols) {
    res[col] = {
      required: true,
      message: "请输入",
      trigger: "blur",
    };
  }
  return res;
}

export const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return bytes + " B";
  } else if (bytes < 1048576) {
    return (bytes / 1024).toFixed(2) + " KB";
  } else if (bytes < 1073741824) {
    return (bytes / 1048576).toFixed(2) + " MB";
  } else {
    return (bytes / 1073741824).toFixed(2) + " GB";
  }
};

export function difference(arr1, arr2) {
  return arr1.filter((x) => !arr2.includes(x));
}

export function getSpanArr(listRow, columns = [], key_column = "") {
  if (columns.length == 0) {
    return listRow;
  }

  let list = [...listRow];
  list.forEach((item) => {
    for (let col of columns) {
      item[`rowspan_${col}`] = 1;
    }
  });
  for (let col of columns) {
    for (let i = 0; i < list.length; i++) {
      let hasSpan = false;
      for (let j = i + 1; j < list.length; j++) {
        if (
          list[i][col] == list[j][col] &&
          (!key_column ||
            (key_column && list[i][key_column] == list[j][key_column]))
        ) {
          list[i][`rowspan_${col}`]++;
          list[j][`rowspan_${col}`]--;
          hasSpan = true;
        }
      }
      if (hasSpan) {
        i = i + list[i][`rowspan_${col}`] - 1;
      }
    }
  }
  return list;
}

export function confirmDelete(title, cb) {
  ElMessageBox.confirm(`您确定要删除${title}吗？删除后不可恢复`, "删除提示", {
    confirmButtonText: "确认删除",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      cb(true);
    })
    .catch((e) => {
      console.log("confirmDelete error", title, e);
      cb(false);
    });
}

// 添加一个用于存储pending promises的对象
const pendingPromises = {};

// 修改后的cacheApiFn数
export function cacheApiFn(fn, handleCacheFn, cache, loading, name) {
  return new Promise((resolve, reject) => {
    // 如果缓存中已有数据，直接返回
    if (
      cache[name] &&
      JSON.stringify(cache[name]) !== "" &&
      JSON.stringify(cache[name]) !== "{}" &&
      JSON.stringify(cache[name]) !== "[]"
    ) {
      return resolve(cache[name]);
    }

    // 如果已经有相同的请求在进行中，返回已存在的promise
    if (pendingPromises[name]) {
      return resolve(pendingPromises[name]);
    }

    // 创建新的请求
    loading[name] = true;
    pendingPromises[name] = fn()
      .then((resp) => {
        return handleCacheFn(resp);
      })
      .finally(() => {
        loading[name] = false;
        delete pendingPromises[name];
      });

    resolve(pendingPromises[name]);
  });
}

export function extractDeptIdNames(data, parentName = "") {
  let result = {};

  for (let item of data) {
    let fullName = parentName ? `${parentName}>${item.name}` : item.name;
    if (item.children && item.children.length > 0) {
      let childrenData = extractDeptIdNames(item.children, fullName);
      result = { ...result, ...childrenData };
    }
  }

  return result;
}

export const getOssUrl = (id, path_id = 1) => {
  if (checkInElectron()) {
    return `assets/${id}`;
  }
  let base_path = import.meta.env.VITE_IMAGE_OSS_PATH;
  if (path_id == 2) {
    base_path = import.meta.env.VITE_IMAGE_OSS_PATH2;
  }
  if (path_id == 3) {
    base_path = import.meta.env.VITE_IMAGE_OSS_PATH3;
  }
  return `${base_path}${id}`;
};

export const getAssetUrl = (id) => {
  return `assets/${id}`;
};

export function differenceObj(arr1, arr2, id = "id") {
  const uids = new Set(arr2.map((item) => item[id]));
  return arr1.filter((item) => !uids.has(item[id]));
}

export function union(arr1, arr2) {
  return [...arr1, ...differenceObj(arr2, arr1)];
}

export function randomId() {
  return Math.random().toString(16).slice(2);
}

export function getSalesTagsConverted(data) {
  return {
    customer: data["customerTopics"].map((x) => x.label),
    internal: data["internalTopics"].map((x) => x.label),
    categories: data.categories.map((x) => x.name),
    data: data,
  };
}

export const calcCategoryIdNameMap = (list) => {
  const data = {};
  for (let i = 0; i < list.length; i++) {
    const x = list[i];
    data[x.categoryId] = x.categoryName;
  }
  return data;
};

export const getCompanyLogoUrl = (url) => {
  if (url && url.startsWith("http")) {
    return url;
  }
  return getOssUrl("customer_logo.png");
};

// 模拟path.join 写一个把字符串拼接成路径的方法
export const pathJoin = (...paths) => {
  return paths.join("/").replace(/\\/g, "/");
};

export const isMac = () => {
  return navigator.userAgent.indexOf("Macintosh") > -1;
};

export const checkInElectron = () => {
  const userAgent = navigator.userAgent;
  const testElectron = /(Electron)/i.test(userAgent);
  const testFeishu = /(appCenter SuperApp)/i.test(userAgent);
  return testElectron && !testFeishu;
};

export const getClientType = () => {
  if (checkInElectron()) {
    if (isMac()) {
      return "mac";
    } else {
      return "win";
    }
  } else {
    if (isPC()) {
      return "web";
    } else {
      return "mobile";
    }
  }
};

export const getDay06 = (date) => {
  const currDate = new Date(date);
  const startOfWeek = new Date(currDate);
  // 获取当前是周几（0-6，0是周日）
  const day = currDate.getDay();
  // 计算到本周一的偏移量（如果当前是周日，则偏移6天；否则偏移(day-1)天）
  const diff = day === 0 ? 6 : day - 1;
  startOfWeek.setDate(currDate.getDate() - diff);

  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); // 从周一开始加6天得到周日

  return [startOfWeek, endOfWeek];
};

const getPlanStatusType = (plan) => {
  const now = new Date();
  const startTime = plan["scheduleStartTime"] || plan["startTime"];
  const endTime = plan["scheduleEndTime"] || plan["endTime"];
  const startTimeDt = new Date(startTime);
  const endTimeDt = new Date(endTime);
  const fifteenMinsBefore = new Date(startTimeDt.getTime() - 15 * 60 * 1000);

  if (plan.inProgress) {
    return "ongoing";
  } else {
    // 检查是否在待开始时间区间内且没有录制记录
    if (now >= fifteenMinsBefore && now <= endTimeDt && !plan.conferenceId) {
      return "notStarted";
    } else {
      return "";
    }
  }
};

// 添加日期分组函数
export function groupPlansByDate(plans) {
  let grouped = {};
  let _plans = [...plans];
  _plans.forEach((plan) => {
    const startTime = plan["scheduleStartTime"] || plan["startTime"];
    const date = startTime.substring(0, 10);
    if (!grouped[date]) {
      grouped[date] = [];
    }
    grouped[date].push(updatePlanApiResult(plan));
  });
  // 按日期排序
  grouped = Object.keys(grouped)
    .sort()
    .reduce((obj, key) => {
      // 按startTime从小到大排序
      obj[key] = grouped[key].sort((a, b) =>
        a.scheduleStartTime.localeCompare(b.scheduleStartTime)
      );
      return obj;
    }, {});
  return grouped;
}

export const updatePlanApiResult = (plan) => {
  let startTime, endTime;
  if (plan["scheduleEndTime"]) {
    startTime = plan["scheduleStartTime"] || plan["startTime"];
    endTime = plan["scheduleEndTime"] || plan["endTime"];
  } else {
    //此数据是从计划详情返回的，没有scheduleEndTime 有duration，scheduleStartDate "2025-05-12"，scheduleStartTime:"15:30"
    // 创建一个表示本地时间的 Date 对象
    startTime = `${plan.startDate}T${plan.startTime}`;
    // 计算结束时间（本地时间）
    const endDt = new Date(
      new Date(startTime).getTime() + plan.duration * 60 * 1000
    );
    endTime = formatDate(endDt, "yyyy-MM-dd hh:mm");
  }
  if (startTime.length > 12) {
    plan["startDt"] = startTime.substring(11, 16);
  } else {
    plan["startDt"] = startTime;
  }

  plan["endDt"] = endTime.substring(11, 16);
  //添加一个字段，表示是否是过期的沟通，昨天之前的沟通都算过期
  plan["isExpired"] = endTime < now("yyyy-MM-dd");
  plan["status"] = getPlanStatusType(plan);
  // 如果开始时间和结束日期不在同一天，inSameDay设置为false
  plan["inSameDay"] = startTime.substring(0, 10) == endTime.substring(0, 10);
  plan["notMe"] = plan["hostUserId"] !== g.appStore.user.id;

  // 计算结束时间（本地时间）
  const endDt = new Date(endTime);

  // 格化日期和时间
  plan.endDate = formatDate(endDt, "yyyy-MM-dd");
  plan.endTime = formatDate(endDt, "hh:mm");
  plan.startWeek = "周" + formatDate(plan.startDate, "w");

  plan.notMe = plan.hostUserId !== g.appStore.user.id;
  plan.showBtnStart = !plan.launched && !plan.notMe;
  plan.showBtnReview = plan.launched && !plan.inProgress && plan.recognitionStatus !== "DELETED";
  plan.showBtnDetail = !plan.completed && plan.status == "ongoing";
  if (!plan.duration) {
    const { scheduleStartTime, scheduleEndTime } = plan;
    if (scheduleStartTime && scheduleEndTime) {
      const startDt = new Date(scheduleStartTime);
      const endDt = new Date(scheduleEndTime);
      const duration = (endDt - startDt) / 1000 / 60;
      plan.duration = duration;
    }
  }
  return plan;
};

export const mergeColumnsAndData = (titles, cateIdNameMap, raw) => {
  const mergedArray = [];
  for (let i = 0; i < raw.length; i++) {
    const data = raw[i].details;
    const obj = {};
    for (let j = 0; j < titles.length; j++) {
      obj[titles[j]] = data[j];
    }
    obj["id"] = raw[i].id;
    obj["categoryId"] = raw[i].categoryId;
    obj["listId"] = raw[i].listId;
    obj["_label"] = data[0];
    obj["所属分类"] = cateIdNameMap[raw[i].categoryId];
    mergedArray.push(obj);
  }
  return mergedArray;
};

export const toClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // 如果navigator.clipboard不可用，使用传统方法
    try {
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.opacity = "0";
      document.body.appendChild(textarea);
      textarea.select();
      document.execCommand("copy");
      document.body.removeChild(textarea);
      return true;
    } catch (e) {
      console.error("复制失败:", e);
      return false;
    }
  }
};

export const trimObject = (data) => {
  Object.keys(data).forEach((key) => {
    if (typeof data[key] === "string") {
      data[key] = data[key].trim();
    }
  });
  return data;
};

export function downloadFile(url, filename) {
  const a = document.createElement("a");
  a.href = url;
  a.download = filename || ""; // 设置下载文件的名称
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

// 从完整的树形数据中提取指定部门子部门
export function extractDeptTree(fullTreeData, targetDeptId) {
  // 递归查找目标部门
  const findDept = (data) => {
    for (const item of data) {
      if (item.value === targetDeptId) {
        return item;
      }
      if (item.children && item.children.length > 0) {
        const found = findDept(item.children);
        if (found) {
          return found;
        }
      }
    }
    return null;
  };

  // 从根节点开始查找
  for (const root of fullTreeData) {
    const result = findDept([root]);
    if (result) {
      return result;
    }
  }

  return [];
}

// 修改 URL 验证函数
export const validateUrl = (url) => {
  if (!url) return true;
  try {
    // 使用 URL 构造器判断合法性
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
};

export const toPercent = (value) => {
  if (!value) {
    return "0%";
  }
  return (100 * value).toFixed(0) + "%";
};

export const jsOpenNewWindow = (url) => {
  let a = document.createElement("a");
  a.setAttribute("href", url);
  a.setAttribute("target", "_blank");
  const id = "superLabel_" + new Date().getTime();
  a.setAttribute("id", id);
  // 防止反复添加
  if (!document.getElementById(id)) {
    document.body.appendChild(a);
  }
  a.click();
  setTimeout(() => {
    document.body.removeChild(a);
  }, 100);
};

// 这个方法需要和移动端同步修改
export const isPC = () => {
  const userAgent = navigator.userAgent;
  const mobileKeywords = [
    "Mobi",
    "Android",
    "iPhone",
    "iPad",
    "Windows Phone",
    "Tablet",
    "Mobile",
  ];

  // 检查用户代理字符串中是否包含移动设备的关键字
  let isMobile = false;
  for (let i = 0; i < mobileKeywords.length; i++) {
    if (userAgent.indexOf(mobileKeywords[i]) !== -1) {
      isMobile = true; // 包含移动设备的关键字，不是PC
    }
  }
  return !isMobile;
};

export const getSsoBaseApi = () => {
  let yxtApiBase = "";
  if (!feConfig?.common) {
    console.error("fail to get feConfig", feConfig);
    return "";
  }

  if (feConfig.apiEnv.indexOf("prod") > -1) {
    const userInfo = getStore("userInfo");
    if (userInfo.apiDomain) {
      yxtApiBase = userInfo.apiDomain;
    } else {
      yxtApiBase = "https://api-phx-ali.yunxuetang.cn";
    }
  } else {
    //去掉最后一个/
    yxtApiBase = window.feConfig?.common.apiBaseUrl;
    yxtApiBase = yxtApiBase.substring(0, yxtApiBase.length - 1);
  }
  return yxtApiBase;
};

export const processFuncPoints = (funcPoints) => {
  const map = {};
  const forbidCtrlTypeStatus = {
    0: "hide",
    1: "gray",
    2: "forbid_custom",
  };
  const enableCtrlTypeStatus = {
    0: "show",
    1: "hide",
    2: "enable_custom",
  };
  for (let i = 0; i < funcPoints.length; i++) {
    const point = funcPoints[i];
    const enable = point.orgState === 1;
    for (let j = 0; j < point.functions.length; j++) {
      const func = point.functions[j];
      map[func.funcCode] = {
        factorCode: point.factorCode,
        enable,
        status: enable
          ? enableCtrlTypeStatus[func.enableCtrlType]
          : forbidCtrlTypeStatus[func.forbidCtrlType],
      };
    }
  }
  return map;
};

export const getFuncStatus = (f2) => {
  try {
    const funcPointsMap = getStore(ConstValue.keyFuncStatusMap, {});
    const funcPoint = funcPointsMap[f2];
    return funcPoint?.enable || false;
  } catch (e) {
    return false;
  }
};

export const mergeMenuNew = (permissions, menuType) => {
  // 筛选客户端且显示的菜单
  let menuItems = permissions
    .filter((item) => item.showed === 1 && item.code.indexOf("appm_") == -1)
    .sort((a, b) => a.orderIndex - b.orderIndex);

  if (menuType == "client") {
    menuItems = menuItems.filter(
      (item) =>
        item.type === 1 &&
        item.code.indexOf("app_") == -1 &&
        item.code.indexOf("appm_") == -1
    );
  } else if (menuType == "admin") {
    menuItems = menuItems.filter((item) => item.type === 2);
  } else if (menuType == "electron") {
    menuItems = menuItems.filter(
      (item) => item.type === 1 && item.code.indexOf("app_") > -1
    );
  } else {
    menuItems = [];
  }
  // 找出顶级菜单（没有父菜单或父ID为空的项）
  const topMenuItems = menuItems.filter(
    (item) =>
      !item.parentId ||
      item.parentId === "" ||
      menuItems.findIndex((p) => p.id === item.parentId) === -1
  );

  // console.log('topMenuItems', menuType, topMenuItems)

  // 构建父子关系的辅助函数
  const buildTree = (items, parentId = null) => {
    return items
      .filter((item) => {
        // 找出顶级菜单（没有父菜单或父ID为空的项）
        if (parentId === null) {
          // 某些顶级菜单可能parentId为空字符串或undefined
          return item.parentId === topMenuItems[0].parentId;
        }
        return item.parentId === parentId;
      })
      .map((item) => {
        // 转换菜单项格式
        const menuItem = {
          id: item.id,
          name: item.name,
          index: item.pageUrl.replace("/#", ""),
          icon: item.navIcon,
          code: item.code,
          orderIndex: item.orderIndex,
          showed: item.showed,
          // 递归查找子菜单
          children: buildTree(menuItems, item.id),
        };

        return menuItem;
      });
  };

  // 从顶级菜单开始构建整个菜单树
  return buildTree(menuItems);
};

export function convertTreeName(tree) {
  var newTree = Array.isArray(tree) ? [] : {};
  for (var key in tree) {
    if (tree.hasOwnProperty(key)) {
      if (key == "children" && tree[key].length == 0) {
        delete tree[key];
      } else if (key === "name") {
        newTree["label"] = tree[key];
      } else if (key === "id") {
        newTree["value"] = tree[key];
      } else if (typeof tree[key] === "object" && tree[key] !== null) {
        newTree[key] = convertTreeName(tree[key]);
      } else {
        newTree[key] = tree[key];
      }
    }
  }

  return newTree;
}

// 防抖函数,不触发后delay再执行fn,delay：毫秒
export const debounce = (fn, delay) => {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
};

// 节流函数,不触发后ms再执行fn,ms：毫秒
export const throttle = (fn, ms) => {
  let ctrl = true;
  return function (...args) {
    if (!ctrl) {
      return;
    }
    ctrl = false;
    // @ts-ignore
    fn.apply(this, args);
    const timer = setTimeout(function () {
      ctrl = true;
      clearTimeout(timer);
    }, ms);
  };
};

export const replaceLastNewline = (str) => {
  var lastIndex = str.lastIndexOf("\n");
  if (lastIndex !== -1) {
    return str.substring(0, lastIndex) + str.substring(lastIndex + 1);
  }
  return str;
};

export const getDateByTimeUnit = (unit) => {
  const date = new Date();
  const localDate = new Date(date.getTime() + date.getTimezoneOffset() * 60000);
  const year = localDate.getFullYear();
  const month = localDate.getMonth();
  const quarter = Math.floor(month / 3);
  const day = localDate.getDay(); // 获取星期几 (0-6, 0代表周日)
  let startDate = null;
  let endDate = null;
  const adaytime = 24 * 60 * 60 * 1000;
  switch (unit) {
    case "week":
      // 计算到本周一的偏移量（如果当前是周日，则偏移6天；否则偏移(day-1)天）
      const diff = day === 0 ? 6 : day - 1;
      startDate = new Date(localDate.getTime() - diff * 24 * 60 * 60 * 1000); // 本周一
      endDate = new Date(startDate.getTime() + 6 * 24 * 60 * 60 * 1000); // 本周日
      break;
    case "month":
      startDate = new Date(Date.UTC(year, month, 1)); // 本月第一天
      endDate = new Date(Date.UTC(year, month + 1, 0)); // 本月最后一天
      break;
    case "quarter":
      startDate = new Date(Date.UTC(year, quarter * 3, 1)); // 本季度第一天
      endDate = new Date(Date.UTC(year, (quarter + 1) * 3, 0)); // 本季度最后一天
      break;
    case "year":
      startDate = new Date(Date.UTC(year, 0, 1)); // 本年1月1日
      endDate = new Date(Date.UTC(year, 11, 31)); // 本年12月31日
      break;
    case "last_week":
      // 计算到上周一的偏移量
      const lastWeekDiff = day === 0 ? 12 : day + 5; // 如果当前是周日，则偏移13天；否则偏移(day+6)天
      startDate = new Date(
        localDate.getTime() - lastWeekDiff * 24 * 60 * 60 * 1000
      ); // 上周一
      endDate = localDate; // 返回今天
      break;
    case "last_month":
      startDate = new Date(localDate.getTime() - 30 * adaytime); // 30天前
      endDate = localDate; // 返回今天
      break;
    case "last_quarter":
      startDate = new Date(localDate.getTime() - 90 * adaytime); // 90天前
      endDate = localDate; // 返回今天
      break;
    case "last_year":
      startDate = new Date(localDate.getTime() - 365 * adaytime); // 365天前
      endDate = localDate; // 返回今天
      break;
    case "last_2year":
      startDate = new Date(localDate.getTime() - 365 * 2 * adaytime); // 2年前
      endDate = localDate; // 返回今天
      break;
    default:
      startDate = date;
      endDate = date;
      console.error("Invalid time unit:", unit);
      break;
  }
  return {
    startDate,
    endDate,
  };
};

export const getDateRangeStr = (type = "quarter") => {
  const { startDate, endDate } = getDateByTimeUnit(type);
  return {
    startDt: formatDate(startDate, "yyyy-MM-dd") + " 00:00:00",
    endDt: formatDate(endDate, "yyyy-MM-dd") + " 23:59:59",
  };
};

export const generateUrl = (baseUrl, params) => {
  const queryString = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
    )
    .join("&");

  return queryString ? `${baseUrl}?${queryString}` : baseUrl;
};

// 检查即将开始的沟通计划
export const checkUpcomingPlans = (plans) => {
  const now = new Date();
  plans.forEach((plan) => {
    const startTime = new Date(plan.scheduleStartTime);
    const endTime = new Date(plan.scheduleEndTime);
    // 计算开始时间前15分钟的时间点
    const tenMinutesBeforeStart = new Date(
      startTime.getTime() - 15 * 60 * 1000
    );
    // 如果当前时间在"开始前10分钟"到"结束时间"之间
    // 且计划未完成且未在进行中
    const notifyList = getStore(ConstValue.keyNotifyList, []);
    const hasNotify = notifyList.includes(plan.scheduleId);
    if (
      now >= tenMinutesBeforeStart &&
      now <= endTime &&
      !plan.completed &&
      plan.status !== "ongoing" &&
      !hasNotify
    ) {
      // 添加标记避免重复通知
      // 打开通知窗口
      // g.electronStore.openWin('visit_notification', plan)
      g.emitter.emit("show_visit_notification", plan);

      // 标记该计划已通知
      notifyList.push(plan.scheduleId);
      if (notifyList.length > 10) {
        notifyList.shift();
      }
      setStore(ConstValue.keyNotifyList, notifyList);
    } else {
      console.log("checkUpcomingPlans no");
    }
  });
};

export const getSecondsShowTime = (recordDurationSeconds) => {
  if (!recordDurationSeconds) {
    return "-";
  }
  const hour = Math.floor(recordDurationSeconds / 3600);
  let min = Math.floor((recordDurationSeconds % 3600) / 60);
  let second = Math.floor((recordDurationSeconds % 60) / 1);
  let txt = "";
  if (hour > 0) {
    txt += `${hour}小时`;
    if (min > 0) {
      txt += `${min}分钟`;
    }
  } else {
    if (min > 0) {
      txt += `${min}分钟`;
      if (second > 0) {
        txt += `${second}秒`;
      }
    } else {
      txt += `${second}秒`;
    }
  }

  return txt;
};

// 将秒数转换为时分秒格式
export const secondsToTime = (seconds) => {
  if (!seconds) return "00:00:00";

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return [hours, minutes, secs]
    .map((v) => v.toString().padStart(2, "0"))
    .join(":");
};

export function checkChildrenHasSub(children) {
  return children.filter((x) => x.children && x.children.length > 0).length > 0;
}

export function deepCopy(obj) {
  let _obj = Array.isArray(obj) ? [] : {};
  for (let i in obj) {
    // @ts-ignore
    _obj[i] = typeof obj[i] === "object" ? deepCopy(obj[i]) : obj[i];
  }
  return _obj;
}

export const getUrlParam = (name, defaultValue = "", type = "string") => {
  try {
    const url = window.location.href;
    // 手动提取 ? 后面的参数部分
    const queryString = url.split("?")[1];
    if (!queryString) {
      return defaultValue;
    }
    const urlSearchParams = new URLSearchParams(queryString);
    let value = urlSearchParams.get(name) || defaultValue;
    if (type == "int") {
      value = parseInt(value);
    }
    return value;
  } catch (e) {
    console.log("error getUrlParam", e);
    return defaultValue;
  }
};

export function removeURLParams(remove_params = []) {
  let url = location.href;
  // 分离 URL 的路径部分和参数部分
  let [path, queryString] = url.split("?");

  // 初始化参数对象
  let params = {};

  // 如果有参数部分,则解析参数
  if (queryString) {
    let queryParams = new URLSearchParams(queryString);
    for (let [key, value] of queryParams.entries()) {
      if (!remove_params.includes(key)) {
        params[key] = value;
      }
    }
  }

  let urlParams = new URLSearchParams(params).toString();
  const newURL = `${path}?${urlParams}`;
  // 无刷新更新当前URL
  window.history.replaceState({}, "", newURL);
}

export function round(x, pos) {
  if (typeof x === "number") {
    return Math.round(x * Math.pow(10, pos)) / Math.pow(10, pos);
  } else if (typeof x === "string") {
    try {
      return round(parseFloat(x), pos);
    } catch (e) {
      return 0;
    }
  }
  return 0;
}

export const get_rules = (fields, pre_str = "fieldValues.") => {
  const fieldRules = {
    // 姓名验证规则
    username: [{ required: true, message: "请输入姓名", trigger: "blur" }],
  };
  // 动态字段验证规则
  fields.forEach((field) => {
    const rules = [];

    if (field.isRequired) {
      rules.push({
        required: true,
        message: `请输入${field.fieldName}`,
        trigger: "blur",
      });
    }

    if (field.fieldType == "String" || field.fieldType == "TextArea") {
      if (field.maxLength) {
        rules.push({
          max: field.maxLength,
          message: `${field.fieldName}最多输入${field.maxLength}个字符`,
          trigger: "blur",
        });
      }

      if (field.minLength) {
        rules.push({
          min: field.minLength,
          message: `${field.fieldName}最少输入${field.minLength}个字符`,
          trigger: "blur",
        });
      }
    }

    if (rules.length > 0) {
      fieldRules[`${pre_str}${field.id}`] = rules;
    }
  });

  return fieldRules;
};

export const isShowTooltip = (
  val,
  e,
  font_size = 14,
  max_rows = 1,
  other_width = 0
) => {
  // 创建一个临时dom，需为行内盒子或者行内块盒子，让宽度适应内容
  const creatDom = document.createElement("span");
  creatDom.innerText = val;
  creatDom.style.whiteSpace = "pre";
  creatDom.style.fontSize = font_size + "px";
  document.body.appendChild(creatDom);
  const width = creatDom.offsetWidth + other_width;
  creatDom.remove();
  return width < e.target.offsetWidth * max_rows;
};

export const genUUID = () => {
  let random;
  let uuid = "";
  for (let i = 0; i < 32; i++) {
    random = (Math.random() * 16) | 0;
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      uuid += "-";
    }
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16);
  }
  return uuid;
};

export const getYxtOrgDomain = () => {
  const isOnlineBlue = location.host.indexOf("blue.") > -1;
  const isLocal =
    location.host.indexOf("localhost") > -1 ||
    location.host.indexOf("127.0.0.1") > -1;
  const isLocalBlue = isLocal && localStorage.getItem("isBlue") == "true";
  if (isOnlineBlue || isLocalBlue) {
    return "salesmate.yunxuetang.cn";
  } else {
    return g.appStore?.user?.ssoDomain || "";
  }
};

<template>
    <div class="communication-dynamics-head flex-row">
        <div>沟通动态</div>
        <el-popover placement="top-start" :width="200" trigger="click" popper-class="communication-dynamics-popper">
            <template #reference>
                <div class="cdh_refer">
                    <CustomVisitIcon />
                </div>
            </template>
            <div class="cdh_pop_main">
                <div class="cdh_line flex-row" v-for="type in communicationTypes" :key="type.name">
                    <div class="cdh1" :style="{ background: type.color }"></div>
                    <div class="cdh2">
                        {{ type.name }}
                    </div>
                </div>
            </div>
        </el-popover>
    </div>
</template>

<script setup>
import CustomVisitIcon from "@/app_client/icons/custom_visit.vue";

const communicationTypes = ref([
    { name: '会议', color: '#5B8FF9' },
    { name: '电话', color: '#25D954' },
    { name: '邮件', color: '#F6BD16' },
    { name: '拜访', color: '#FF75AD' }
]);

defineExpose({
    CustomVisitIcon,
});
</script>

<style lang="scss">
.communication-dynamics-head {
    justify-content: space-between;

    .cdh_refer {
        margin-top: 2px;
        margin-left: 28px;
        cursor: pointer;
    }
}

.communication-dynamics-popper {
    .cdh_pop_main {
        .cdh_line {
            padding: 4px 0;

            .cdh1 {
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin: 4px 6px;
            }

            .cdh2 {
                font-size: 12px;
                color: #666;
            }
        }
    }
}
</style>

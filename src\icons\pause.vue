<template>
    <svg width="36px" height="36px" viewBox="0 0 36 36" version="1.1">
        <g id="沟通过程挖掘--" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="播放器-收起-音频条-交互" transform="translate(-32.000000, -343.000000)">
                <g id="播放" transform="translate(32.000000, 343.000000)">
                    <circle id="椭圆形" fill="#436BFF" cx="18" cy="18" r="18"></circle>
                    <path
                        d="M21,11 L23,11 C23.5522847,11 24,11.4477153 24,12 L24,24 C24,24.5522847 23.5522847,25 23,25 L21,25 C20.4477153,25 20,24.5522847 20,24 L20,12 C20,11.4477153 20.4477153,11 21,11 Z M13,11 L15,11 C15.5522847,11 16,11.4477153 16,12 L16,24 C16,24.5522847 15.5522847,25 15,25 L13,25 C12.4477153,25 12,24.5522847 12,24 L12,12 C12,11.4477153 12.4477153,11 13,11 Z"
                        id="形状结合" fill="#FFFFFF"></path>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'AddIcon',
}
</script>

<style lang='scss' scoped></style>

<template>
    <el-drawer v-model="drawerVisible" :before-close="handleClose" size="500px" class="ability-model-form-drawer"
        :close-on-click-modal="false">
        <template #header>
            <div class="drawer-header">
                <span class="drawer-title">编辑</span>
            </div>
        </template>

        <div class="drawer-content">
            <el-form ref="formRef" :model="modelForm" :rules="rules" label-position="top"
                require-asterisk-position="right">
                <div>
                    <el-form-item label="模型名称" prop="name">
                        <el-input v-model="modelForm.name" show-word-limit maxlength="50" />
                    </el-form-item>
                </div>

                <div class="form-section">
                    <el-form-item label="关联能力方向" prop="itemsArr">
                        <LxMultiSelect v-model="modelForm.itemsArr" :options="abilityOptions" :max="6" :min="1"
                            placeholder="请选择关联能力方向" @confirm="handleAbilityConfirm" />
                    </el-form-item>
                </div>
            </el-form>
        </div>

        <template #footer>
            <div class="drawer-footer">
                <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
                <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import LxMultiSelect from '@/components/LxMultiSelect/LxMultiSelect.vue'
const emit = defineEmits(['success'])

const props = defineProps({
    dimensionOptions: {
        type: Array,
        default: () => []
    }
})


const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)

// 能力选项数据
const abilityOptions = computed(() => {
    return props.dimensionOptions.map(item => ({
        label: item.name,
        value: item.id
    }))
})

const modelForm = reactive({
    name: '',
    items: [],
    itemsArr: []
})

// 处理能力选择确认
const handleAbilityConfirm = (selectedValues) => {
    console.log('选中的能力:', selectedValues)
    // 这里可以添加其他确认后的处理逻辑
}

const rules = {
    name: [
        { required: true, message: '请输入模型名称', trigger: 'blur' },
        { max: 50, message: '最多输入50个字符', trigger: 'blur' }
    ],
    itemsArr: [
        { required: true, message: '请选择关联能力方向', trigger: 'change' },
    ]
}

const openDrawer = (data = null) => {
    drawerVisible.value = true

    if (data) {
        // 编辑模式
        isEditMode.value = true
        currentId.value = data.id
        modelForm.name = data.name
        modelForm.items = data.items || []
        modelForm.itemsArr = data.items && data.items.length > 0 ? data.items.map(item => item.dimensionId) : []
    } else {
        // 添加模式
        isEditMode.value = false
        currentId.value = null
        modelForm.name = ''
        modelForm.items = []
    }
}

const closeDrawer = () => {
    drawerVisible.value = false
    formRef.value?.resetFields()
}

const submitForm = () => {
    if (modelForm.itemsArr.length > 6) {
        ElMessage.warning({ message: '最多保留6个能力方向', grouping: true })
        return
    }
    formRef.value.validate((valid) => {
        if (valid) {
            // 处理表单提交
            const formData = {
                id: currentId.value,
                name: modelForm.name,
                items: isEditMode.value ? modelForm.itemsArr.map(item => ({
                    dimensionId: item,
                    id: modelForm.items.find(item => item.dimensionId === item)?.id
                })) : modelForm.itemsArr.map(item => ({
                    dimensionId: item,
                }))
            }
            emit('success', {
                mode: isEditMode.value ? 'edit' : 'add',
                data: formData
            })
            closeDrawer()
        } else {
            ElMessage.error('请检查输入内容')
            return false
        }
    })
}

const handleClose = (done) => {
    closeDrawer()
    done()
}

// 暴露方法给父组件
defineExpose({
    openDrawer,
    closeDrawer
})
</script>

<style scoped lang="scss">
.ability-model-form-drawer {
    :deep(.el-drawer__header) {
        padding: 0;
        margin-bottom: 0;
        border-bottom: 1px solid #f0f0f0;
    }

}

.drawer-header {
    display: flex;
    align-items: center;
    padding: 16px 24px;

    .drawer-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
    }
}

.drawer-content {
    // overflow-y: auto;
}


.drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;

    button {
        padding: 5px 16px;
        border-radius: 6px;
    }

    .cancel-btn {
        border: 1px solid #d9d9d9;
        background: #fff;
        color: #666;
    }

    .confirm-btn {
        background: #436BFF;
        border-color: #436BFF;
    }
}
</style>
<template>
    <div class="customer_btn_file" v-if="hasImportAccess">
        <el-button type="default" @click="showDrawer">导入</el-button>
        <DrawerUpload ref="refDrawerUpload" :isAdmin="isAdmin" />
    </div>
</template>

<script setup>
import DrawerUpload from "./DrawerUpload.vue";

const props = defineProps({
    isAdmin: {
        type: Boolean,
        default: false
    },
    param: {
        type: Object,
        required: false,
    },
    team: {
        type: <PERSON>olean,
        required: false,
        default: false
    }
});

const refDiaEdit = ref();
const emit = defineEmits(['reload']);
const refDrawerUpload = ref(null);
const pageAccess = ref({})
const hasImportAccess = ref(false);

const showEdit = (row) => {
    refDiaEdit.value.show(row);
}

const setAccess = (access) => {
    pageAccess.value = access;
    if (props.isAdmin) {
        hasImportAccess.value = pageAccess.value.customer_import_opr;
    }
}

const showDrawer = () => {
    refDrawerUpload.value.show();
}
const checkAccess = () => {
    g.cacheStore.getApiCustomerConfig().then(res => {
        // 1-创建客户,2-导入客户,3-编辑客户,4-删除客户
        hasImportAccess.value = res.permissions.includes(2);
    });
};

onMounted(() => {
    if (!props.isAdmin) {
        checkAccess();
    }
});

defineExpose({ showDrawer, refDiaEdit, showEdit, setAccess })
</script>

<style lang="scss">
.customer_btn_file {

    .el-dropdown-link {

        .vbb_title {
            margin-top: 3px;
        }

        .el-icon {
            margin-top: 3px;
        }
    }

    .el-dropdown {
        height: 18px;
        padding: 6px 12px;
        border: 1px solid #D9D9D9;
        cursor: pointer;
        border-radius: 4px;

        .el-dropdown-item {
            width: 300px;
        }
    }

    .vbp_title {
        margin-bottom: 12px;

        span {
            color: red;
            margin-left: 4px;
        }
    }
}
</style>
<template>
    <div class="lx-collapse-item" :class="{ 'is-disabled': disabled }">
        <div class="lx-collapse-item__header" @click="handleHeaderClick"
            :class="{ 'is-active': isActive, 'is-disabled': disabled }">
            <div class="lx-collapse-item__header-left">
                <div class="lx-collapse-item__arrow">
                    <el-icon>
                        <CaretTop v-if="isActive" />
                        <CaretBottom v-else />
                    </el-icon>
                </div>
                <div class="lx-collapse-item__title">
                    <slot name="title">{{ title }}</slot>
                </div>
            </div>
            <div class="lx-collapse-item__header-right">
                <slot name="right"></slot>
            </div>
        </div>
        <div class="lx-collapse-item__content" :class="{ 'is-active': isActive }" v-show="isActive">
            <div class="lx-collapse-item__body">
                <slot></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { inject, computed } from 'vue'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    name: {
        type: [String, Number],
        required: true
    },
    disabled: {
        type: Boolean,
        default: false
    }
})

const collapse = inject('collapse')

const isActive = computed(() => {
    return collapse.isActive(props.name)
})

const handleHeaderClick = () => {
    if (props.disabled) return
    collapse.toggleActive(props.name)
}
onMounted(() => {
    if (props.disabled) return
    collapse.toggleActive(props.name)
})
</script>

<style scoped lang="scss">
.lx-collapse-item {
    // background: #F9FAFC;
    border-radius: 12px 12px 12px 12px;
    border: 1px solid #F0F0F0;

    &:last-child {
        margin-bottom: 0;
    }

    &.is-disabled {
        opacity: 0.6;
    }

    &__header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px;
        box-sizing: border-box;
        background: #F9FAFC;
        border-radius: 11px 11px 11px 11px;
        cursor: pointer;
        transition: all 0.3s ease;

    }

    &__header-left {
        display: flex;
        align-items: center;
    }

    &__title {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        margin-left: 8px;
    }

    &__arrow {
        display: flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;
        color: #909399;

        svg {
            width: 16px;
            height: 16px;

        }
    }

    &__content {
        // background-color: #fff;
        transition: all 0.3s ease;
    }

    &__body {
        padding: 16px;
    }
}
</style>
<template>
    <div class="lx-collapse">
        <slot></slot>
    </div>
</template>

<script setup>
import { provide, ref } from 'vue'

const props = defineProps({
    accordion: {
        type: Boolean,
        default: false
    },
    modelValue: {
        type: [String, Number, Array],
        default: () => []
    }
})

const emit = defineEmits(['update:modelValue', 'change'])

const activeNames = ref(Array.isArray(props.modelValue) ? [...props.modelValue] : [])
// 确保 activeNames 包含所有可能的子项名称
activeNames.value = Array.isArray(props.modelValue) ? [...props.modelValue] : []

const toggleActive = (name) => {
    if (props.accordion) {
        // 手风琴模式：一次只能展开一个
        activeNames.value = activeNames.value.includes(name) ? [] : [name]
    } else {
        // 普通模式：可以展开多个
        const index = activeNames.value.indexOf(name)
        if (index > -1) {
            activeNames.value.splice(index, 1)
        } else {
            activeNames.value.push(name)
        }
    }

    // 触发更新事件
    const value = props.accordion ? activeNames.value[0] || '' : activeNames.value
    emit('update:modelValue', value)
    emit('change', value)
}

const isActive = (name) => {
    return activeNames.value.includes(name)
}

provide('collapse', {
    activeNames,
    toggleActive,
    isActive
})
</script>

<style scoped lang="scss">
.lx-collapse {
    width: 100%;
}
</style>
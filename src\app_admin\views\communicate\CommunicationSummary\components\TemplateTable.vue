<template>
  <div class="template-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #col_templateNameForSummary="{ row }">
        <div class="template-name">{{ row.name
        }}</div>
      </template>

      <template #col_relatedDimension="{ row }">
        <div class="relatedDimension">
          <div v-for="(line, index) in (row.items || [])" :key="index" class="dimension-line">
            {{ index + 1 }}. {{ line.dimensionName }}
          </div>
        </div>
      </template>

      <template #col_updater="{ row }">
        <div>{{ row.updatedUserName || '-' }}</div>
      </template>

      <template #col_updateTime="{ row }">
        <div>{{ row.updatedTime || '-' }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
      </template>
    </MyTable>

    <TemplateTableDrawer ref="templateTableDrawerRef" v-if="templateTableDrawerShow" @success="handleSuccess" :dimensions="dimensions" @close="templateTableDrawerShow = false" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getModels, updateModel, getDimension } from "@/app_admin/api/communicate.js";
import TemplateTableDrawer from './TemplateTableDrawer.vue';

const refTable = ref();
const templateTableDrawerRef = ref();
const dimensions = ref([]);
const templateTableDrawerShow = ref(false)
const tableConfig = reactive({
  tableid: 'template_list_summary',
  param: {
    type: "SUMMARY"
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模板名称",
  delete_hint_column: 'templateNameForSummary',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["templateNameForSummary", "relatedDimension", "updater", "updateTime"],
  template: ["templateNameForSummary", "relatedDimension", "updater", "updateTime"],
  urlGet: getModels,
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleEdit = (row) => {
  templateTableDrawerShow.value = true
  nextTick(() => {
    templateTableDrawerRef.value.openDrawer(row);
  })
};

const handleSuccess = (result) => {
  const { mode, data } = result;
  const params = {
    id: data.id,
    name: data.name,
    items: data.items,
    type: 'SUMMARY'
  };

  updateModel(params).then(resp => {
    if (resp.code === 0) {
      ElMessage.success('编辑成功');
      refTable.value.search();
    } else {
      ElMessage.error(`编辑失败: ${resp.message}`);
    }
  }).catch(err => {
    ElMessage.error(`编辑失败: ${err.response.data.message}`);
    console.error(err);
  });
};

defineExpose({
  refTable
});

onMounted(() => {
  getDimension({ type: "SUMMARY", pageSize: 999 }).then((resp) => {
    if (resp.code == 0) {
      dimensions.value = resp.data.datas || []
    }
  });
});
</script>

<style lang="scss" scoped>
.template-table {
  padding: 24px 4px;
  font-size: 14px;
  color: #262626;

  :deep(.table_class) {
    height: calc(100vh - 250px) !important;

    table {
      min-width: 1000px !important;

      .col_operation_ {
        width: 200px;
      }
    }
  }

  .relatedDimension {
    max-width: 400px;

    .dimension-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
<template>
  <div class="admin_ca_template_table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">新建</el-button>
      </template>
      <template #col_relatedDimension="{ row }">
        <div class="dimensions">
          <div v-for="(row, index) in row.items" :key="index" class="dimension-line">
            {{ index + 1 }} . {{ row.dimensionName }}
          </div>
        </div>
      </template>

      <template #col_createdTime="{ row }">
        <div>{{ row.createdTime }}</div>
      </template>


      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <AnalysisTemplateDrawer ref="templateTableDrawerRef" v-if="templateTableDrawerShow" @success="handleSuccess"
      :dimensions="behaviorOptions" @close="templateTableDrawerShow = false" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { communicateApi } from "@/app_admin/api";
import AnalysisTemplateDrawer from './AnalysisTemplateDrawer.vue'
import { getDimension, deleteModel } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';

const refTable = ref();
const templateTableDrawerRef = ref();
const templateTableDrawerShow = ref(false)
const tableConfig = reactive({
  tableid: 'admin_ca_template_list_analysis',
  param: {
    pageSize: 10,
    type: "ANALYSIS"
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模板名称",
  delete_hint_column: 'name',
  show_link_column: true,
  show_link_edit: true,
  show_link_view: false,
  show_link_delete: false,
  columns: ["name", "relatedDimension", "createdUserName", "createdTime"],
  template: ["relatedDimension"],
  urlGet: communicateApi.getAnalysisModelList,
  urlDelete: deleteModel
});
const behaviorOptions = ref([]);

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
  if (action == 'init_edit') {
    templateTableDrawerShow.value = true
    nextTick(() => {
      templateTableDrawerRef.value.show_edit(data);
    })
  } else if (action == "init_add") {
    templateTableDrawerShow.value = true
    nextTick(() => {
      templateTableDrawerRef.value.show_add();
    })
  }
};

const handleSuccess = () => {
  console.log('handleSuccess')
  refTable.value.search();
}

const handleDelete = (row) => {
  confirmDelete(row.name, (status) => {
    if (status) {
      deleteModel(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      }).catch(err => {
        ElMessage.error(`删除失败.错误代码 ${err.code}，错误信息 ${err.message}`);
      })
    }
  });
};
const initOptions = async () => {
  const param = {
    "pageSize": 99,
    "pageNumber": 1,
    "orderBy": "",
    "asc": false,
    "type": "ANALYSIS"
  }
  const resp = await getDimension(param) || {}
  behaviorOptions.value = resp.data?.datas?.map(x => {
    return {
      label: x.name,
      value: x.id
    }
  });
}
const handleAdd = () => {
  templateTableDrawerShow.value = true
  nextTick(() => {
    templateTableDrawerRef.value.show_add();
  })
}

defineExpose({
  refTable
});

onMounted(async () => {
  await initOptions()
})
</script>

<style lang="scss">
.admin_ca_template_table {
  padding: 24px 0;

  .table_box .table_class {
    height: calc(100vh - 285px) !important;
  }

  .template-name {
    font-weight: 500;
    color: #333;
  }

  .dimensions {
    max-width: 400px;

    .dimension-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
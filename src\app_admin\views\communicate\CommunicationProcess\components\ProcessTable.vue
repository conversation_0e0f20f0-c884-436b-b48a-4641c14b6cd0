<template>
  <div class="template-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <!-- 商机有新建按钮 -->
        <el-button type="primary" v-if="isShangjiType == '1'" @click="handleAdd">新建</el-button>
      </template>
      <template #col_processName="{ row }">
        <div class="template-name">{{ row.name }}</div>
      </template>

      <template #col_communicationSteps="{ row }">
        <div class="dimensions">
          <p v-for="(line, index) in (row.stages || [])" :key="index" class="dimension-line">
            {{ index + 1 }}. {{ line.name }}
          </p>
        </div>
      </template>

      <template #col_relatedName="{ row }">
        <div>{{ row.typeDesc }}</div>
      </template>

      <template #col_createdUserName="{ row }">
        <div>{{ row.createdUserName }}</div>
      </template>

      <template #col_createdTime="{ row }">
        <div>{{ row.createdTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <ProcessTableDrawer v-if="templateTableDrawerShow" ref="templateTableDrawerRef" :taskOptions="taskOptions"
      :analysisOptions="analysisOptions" @success="handleFormSuccess" @close="templateTableDrawerShow = false" />
  </div>
</template>

<script setup>
import { confirmDelete } from '@/js/utils.js';
import MyTable from "@/components/Table.vue";
import { pageCommunicationFlow, deleteCommunicationFlow, createCommunicationFlow, updateCommunicationFlow, getModels, } from "@/app_admin/api/communicate.js";
import ProcessTableDrawer from './ProcessTableDrawer.vue'


console.log(g.appStore.isShangji)
const isShangjiType = computed(() => {
  return g.appStore.isShangji ? "1" : "0"
})
const refTable = ref();
const templateTableDrawerRef = ref();
const templateTableDrawerShow = ref(false);
const taskOptions = ref([]);
const analysisOptions = ref([]);
const tableConfig = reactive({
  tableid: 'template_list_2',
  param: {
    type: isShangjiType.value,
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  search_ph: "模板名称",
  delete_hint_column: 'processName',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  columns: ["processName", "communicationSteps", "relatedName", "createdUserName", "createdTime"],
  template: ["processName", "communicationSteps", "relatedName", "createdUserName", "createdTime"],
  urlGet: pageCommunicationFlow,

});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  templateTableDrawerShow.value = true;
  nextTick(() => {
    templateTableDrawerRef.value.openDrawer();
  })
};

const handleEdit = (row) => {
  templateTableDrawerShow.value = true;
  nextTick(() => {
    templateTableDrawerRef.value.openDrawer(row);
  })
};
const handleDelete = (row) => {
  confirmDelete(row.name, (status) => {
    if (status) {
      deleteCommunicationFlow(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      }).catch(err => {
        ElMessage.error(`删除失败: ${err.response.data.message}`);
      })
    }
  });
};

const handleFormSuccess = (result) => {
  const { mode, data } = result;

  if (mode === 'add') {
    // 处理添加逻辑
    const params = {
      name: data.name,
      type: data.type,
      stages: data.stages
    };

    createCommunicationFlow(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('添加成功');
        templateTableDrawerRef.value.closeDrawer()
        refTable.value.search();
      } else {
        ElMessage.error(`添加失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error('添加失败');
      console.error(err);
    });
  } else if (mode === 'edit') {
    // 处理编辑逻辑
    const params = {
      id: data.id,
      name: data.name,
      type: data.type,
      stages: data.stages
    };

    updateCommunicationFlow(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('编辑成功');
        templateTableDrawerRef.value.closeDrawer()
        refTable.value.search();
      } else {
        ElMessage.error(`编辑失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error('编辑失败');
      console.error(err);
    });
  }
};

defineExpose({
  refTable
});

onMounted(() => {

  getModels({ type: "TASK", pageSize: 999 }).then((resp) => {
    if (resp.code == 0) {
      taskOptions.value = resp.data.datas || []
    }
  });
  getModels({ type: "ANALYSIS", pageSize: 999 }).then((resp) => {
    if (resp.code == 0) {
      analysisOptions.value = resp.data.datas || []
    }
  });
});
</script>

<style lang="scss" scoped>
.template-table {
  font-size: 14px;
  color: #262626;

  // padding: 24px 0;
  :deep(.table_class) {
    height: calc(100vh - 200px) !important;

    table {
      min-width: 1000px !important;

      // .col_operation_ {
      //   width: 200px;
      // }
    }
  }


  .dimensions {
    max-width: 400px;

    .dimension-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin: 0;
      }
    }
  }
}
</style>
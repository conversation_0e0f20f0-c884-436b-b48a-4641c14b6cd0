<template>
    <div class="demo-container">
        <h2>日期时间选择器组件演示</h2>

        <div class="demo-section">
            <h3>基础用法</h3>
            <DateTimePicker v-model="dateTime1" />
            <p>选择的值: {{ formatDateTime(dateTime1) }}</p>
        </div>

        <div class="demo-section">
            <h3>自定义格式</h3>
            <DateTimePicker v-model="dateTime2" format="YYYY年MM月DD日 HH时mm分" placeholder="请选择日期时间" />
            <p>选择的值: {{ formatDateTime(dateTime2) }}</p>
        </div>

        <div class="demo-section">
            <h3>不可清空</h3>
            <DateTimePicker v-model="dateTime3" :clearable="false" />
            <p>选择的值: {{ formatDateTime(dateTime3) }}</p>
        </div>

        <div class="demo-section">
            <h3>事件监听</h3>
            <DateTimePicker v-model="dateTime4" @change="handleChange" />
            <p>选择的值: {{ formatDateTime(dateTime4) }}</p>
            <p>最后变更时间: {{ lastChangeTime }}</p>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import DateTimePicker from './DateTimePicker.vue'
import dayjs from 'dayjs'

const dateTime1 = ref(null)
const dateTime2 = ref(null)
const dateTime3 = ref(new Date())
const dateTime4 = ref(null)
const lastChangeTime = ref('')

const formatDateTime = (date) => {
    if (!date) return '未选择'
    return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

const handleChange = (value) => {
    lastChangeTime.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
    console.log('日期时间变更:', value)
}
</script>

<style scoped>
.demo-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.demo-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    background-color: #fafafa;
}

.demo-section h3 {
    margin-top: 0;
    margin-bottom: 16px;
    color: #303133;
    font-size: 16px;
    font-weight: 500;
}

.demo-section p {
    margin-top: 12px;
    color: #606266;
    font-size: 14px;
}
</style>
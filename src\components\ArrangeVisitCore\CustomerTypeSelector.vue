<template>
    <div class="customer-type-selector">
        <div class="selector-container">
            <div class="selector-option" :class="{ active: modelValue === 'customer' }" @click="selectType('customer')">
                <div class="customer_type_name">
                    客户
                </div>
                <div class="customer_type_desc">
                    在客户下安排沟通
                </div>
                <img v-if="modelValue === 'customer'" :src="getOssUrl('client_checked.png', 2)" class="check_img"
                    alt="img" />
            </div>
            <div class="selector-option" :class="{ active: modelValue === 'opportunity' }"
                @click="selectType('opportunity')">
                <div class="customer_type_name">
                    商机
                </div>
                <div class="customer_type_desc">
                    在商机下安排沟通
                </div>
                <img v-if="modelValue === 'opportunity'" :src="getOssUrl('client_checked.png', 2)" class="check_img"
                    alt="img" />
            </div>
        </div>
    </div>
</template>

<script setup>
import { getOssUrl } from "@/js/utils";
const props = defineProps({
    modelValue: {
        type: String,
        default: 'customer'
    }
});

const emit = defineEmits(['update:modelValue', 'change']);

const selectType = (type) => {
    emit('update:modelValue', type);
    emit('change', type);
};
</script>

<style lang="scss" scoped>
.customer-type-selector {
    margin-bottom: 15px;

    .selector-container {
        display: flex;
        flex-direction: row;
        border-radius: 6px;
        overflow: hidden;
        gap: 10px;

        .selector-option {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            transition: all 0.2s ease;
            background-color: #ffffff;
            color: #606266;
            border-radius: 6px;
            min-width: 70px;
            text-align: left;
            user-select: none;
            position: relative;
            flex: 1;

            &:hover {
                background-color: #f5f7fa;
            }

            &.active {
                color: #262626;
                font-weight: 500;
                border: 1px solid var(--el-color-primary);
            }

            &:not(.active) {
                border: 1px solid #dcdfe6;
            }

            .customer_type_name {
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 4px;
            }

            .customer_type_desc {
                font-size: 12px;
                color: #909399;
                line-height: 1.4;
            }

            .check_img {
                position: absolute;
                top: -1px;
                right: -1px;
                width: 20px;
                height: 20px;
                z-index: 1;
            }
        }
    }
}
</style>
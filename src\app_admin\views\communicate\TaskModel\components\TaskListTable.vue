<template>
  <div class="task-list-table">
    <MyTable ref="refTable" :cfg="tableConfig" @callback="handleTableCallback">
      <template #_header_left>
        <el-button type="primary" @click="handleAdd">新建</el-button>
      </template>

      <template #col_task_name="{ row }">
        <div class="ability-name">{{ row.name }} <div :class="`dimension ${row.system ? 'dimension-2' : ''}`">{{
          row.system ? '系统' : '' }}</div>
        </div>
      </template>

      <template #col_behavior="{ row }">
        <div class="behavior-standard">
          <div v-for="(line, index) in (row.items || [])" :key="index" class="standard-line">
            {{ index + 1 }}. {{ line.content }}
          </div>
        </div>
      </template>

      <template #col_createdUserName="{ row }">
        <div>{{ row.createdUserName }}</div>
      </template>

      <template #col_createdTime="{ row }">
        <div>{{ row.createdTime }}</div>
      </template>

      <template #_link_post="{ row }">
        <el-button type="primary" :disabled="row.system" text @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" :disabled="row.system" text @click="handleDelete(row)">删除</el-button>
      </template>
    </MyTable>

    <TaskFormDrawer ref="taskFormDrawerRef" @success="handleFormSuccess" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getDimension, deleteDimension, createDimension, updateDimension } from "@/app_admin/api/communicate.js";
import { confirmDelete } from '@/js/utils.js';
import TaskFormDrawer from './TaskFormDrawer.vue';
import { ElMessage } from 'element-plus';

const refTable = ref();
const taskFormDrawerRef = ref();

const tableConfig = reactive({
  tableid: 'task_list_10',
  param: {
    type: "TASK",
  },
  need_init_load: true,
  show_search: true,
  need_header: true,
  show_search: false,
  show_btn_column: false,
  show_btn_add: false, // 使用自定义按钮
  form: {},
  delete_hint_column: 'task_name',
  show_link_column: true,
  show_link_edit: false,
  show_link_view: false,
  show_link_delete: false,
  sortable: "custom",
  sortables: ["createdTime"],
  columns: ["task_name", "behavior", "createdUserName", "createdTime"],
  template: ["task_name", "behavior", "createdUserName", "createdTime"],
  column_widths: {
    task_name: 240,
  },
  urlGet: getDimension,
  urlDelete: deleteDimension
});

const handleTableCallback = (action, data) => {
  console.log('Table callback:', action, data);
};

const handleAdd = () => {
  taskFormDrawerRef.value.openDrawer();
};

const handleEdit = (row) => {
  // 将数据转换为表单所需格式
  const editData = {
    id: row.id,
    name: row.name,
    items: row.items
  };
  taskFormDrawerRef.value.openDrawer(editData);
};

const handleDelete = (row) => {
  confirmDelete(row.name, (status) => {
    if (status) {
      deleteDimension(row.id).then((resp) => {
        if (resp.code == 0) {
          ElMessage.success("删除成功");
          refTable.value.search();
        } else {
          ElMessage.error(`删除失败.错误代码 ${resp.code}，错误信息 ${resp.message}`);
        }
      }).catch(err => {
        ElMessage.error(`删除失败: ${err.response.data.message}`);
        console.error(err);
      });
    }
  });
};

const handleFormSuccess = (result) => {
  const { mode, data } = result;

  if (mode === 'add') {
    // 处理添加逻辑
    const params = {
      name: data.name,
      items: data.items,
      type: "TASK"
    };

    createDimension(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('添加成功');
        refTable.value.search();
      } else {
        ElMessage.error(`添加失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error(`添加失败: ${err.response.data.message}`);
    });
  } else if (mode === 'edit') {
    // 处理编辑逻辑
    const params = {
      id: data.id,
      name: data.name,
      items: data.items
    };

    updateDimension(params).then(resp => {
      if (resp.code === 0) {
        ElMessage.success('编辑成功');
        refTable.value.search();
      } else {
        ElMessage.error(`编辑失败: ${resp.message}`);
      }
    }).catch(err => {
      ElMessage.error(`编辑失败: ${err.response.data.message}`);
    });
  }
};

defineExpose({
  refTable
});
</script>

<style lang="scss" scoped>
.task-list-table {
  padding: 20px 4px;

  :deep(.table_class) {
    height: calc(100vh - 250px) !important;

    table {
      min-width: 1000px !important;

      .col_operation_ {
        width: 200px;
      }
    }
  }



  .behavior-standard {
    max-width: 400px;

    .standard-line {
      margin-bottom: 4px;
      line-height: 1.4;
      color: #666;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .dimension {
    padding: 1px 6px;
    border-radius: 2px 2px 2px 2px;
    font-weight: 400;
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    display: inline-block;
  }

  .dimension-1 {
    background: rgba(4, 204, 164, 0.1);
    color: #04CCA4;
  }

  .dimension-2 {
    background: #F0F6FF;
    color: #436BFF;
  }
}
</style>
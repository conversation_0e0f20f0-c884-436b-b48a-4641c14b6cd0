<template>
    <div class="settings-container">
        <!-- 工商校验规则 -->
        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">工商校验规则</div>
            </div>
            <div class="section-content">
                <div class="form-item">
                    <label class="form-label">客户名称工商规则校验</label>
                    <div class="dropdown-field">
                        <span class="dropdown-text">强制校验客户名称为工商注册名</span>
                        <i class="dropdown-icon">▼</i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 权限设置 -->
        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">权限设置</div>
            </div>
            <div class="section-content">
                <div class="permission-table">
                    <div class="table-header">
                        <div class="header-cell">角色</div>
                        <div class="header-cell">操作权限</div>
                    </div>
                    <div class="table-row">
                        <div class="role-cell">销售</div>
                        <div class="permission-cell">
                            <div class="permission-item">
                                <input type="checkbox" id="create" checked>
                                <label for="create">创建客户</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="import" checked>
                                <label for="import">导入客户</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="edit" checked>
                                <label for="edit">编辑客户</label>
                            </div>
                            <div class="permission-item">
                                <input type="checkbox" id="delete" checked>
                                <label for="delete">删除客户</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 查重设置 -->
        <div class="settings-section">
            <div class="section-header">
                <div class="section-title">查重设置</div>
            </div>
            <div class="section-content">
                <div class="duplicate-criteria">
                    <div class="criteria-item">
                        <div class="criteria-field">
                            <span class="field-text">企业地址</span>
                            <i class="field-icon">▼</i>
                        </div>
                        <i class="delete-icon" @click="removeCriteria">🗑️</i>
                    </div>
                    <div class="logical-operator">且</div>
                    <div class="criteria-item">
                        <div class="criteria-field">
                            <span class="field-text">客户名称</span>
                            <i class="field-icon">▼</i>
                        </div>
                        <i class="delete-icon" @click="removeCriteria">🗑️</i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const removeCriteria = () => {
    // 删除查重条件的逻辑
    console.log('删除查重条件')
}
</script>

<style lang="scss" scoped>
.settings-container {

    margin: 0 auto;
    padding: 20px 4px;
    background: #fff;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.settings-section {
    margin-bottom: 30px;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    overflow: hidden;
}

.section-header {
    background: #f8f9fa;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 12px;

    &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 16px;
        background: #1890ff;
        border-radius: 2px;
    }
}

.section-content {
    padding: 20px;
}

.form-item {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #666;
}

.dropdown-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    background: #fff;
    cursor: pointer;
    transition: border-color 0.3s;

    &:hover {
        border-color: #1890ff;
    }
}

.dropdown-text {
    color: #333;
    font-size: 14px;
}

.dropdown-icon {
    color: #999;
    font-size: 12px;
}

.permission-table {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    overflow: hidden;
}

.table-header {
    display: flex;
    background: #fafafa;
    border-bottom: 1px solid #e8e8e8;
}

.header-cell {
    flex: 1;
    padding: 12px 16px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.table-row {
    display: flex;
    border-bottom: 1px solid #f0f0f0;
}

.role-cell {
    flex: 1;
    padding: 16px;
    color: #333;
    font-size: 14px;
    border-right: 1px solid #e8e8e8;
    display: flex;
    align-items: center;
}

.permission-cell {
    flex: 2;
    padding: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 8px;

    input[type="checkbox"] {
        width: 16px;
        height: 16px;
        accent-color: #1890ff;
    }

    label {
        font-size: 14px;
        color: #333;
        cursor: pointer;
    }
}

.duplicate-criteria {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.criteria-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.criteria-field {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    background: #fff;
    min-width: 120px;
    cursor: pointer;

    &:hover {
        border-color: #1890ff;
    }
}

.field-text {
    font-size: 14px;
    color: #333;
}

.field-icon {
    color: #999;
    font-size: 12px;
}

.logical-operator {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.delete-icon {
    cursor: pointer;
    font-size: 14px;
    color: #ff4d4f;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.3s;

    &:hover {
        background: #fff1f0;
    }
}
</style>

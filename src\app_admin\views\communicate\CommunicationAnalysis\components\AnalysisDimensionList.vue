<template>
  <div class="analysis-dimension-container">
    <el-button type="primary" @click="handleAdd" style="margin-bottom: 10px;">新建</el-button>
    <div class="dimension-grid">
      <div class="dimension-card" v-for="dimension in dimensions" :key="dimension.id">
        <div class="card-content">
          <div class="card-footer">
            <div class="card-title-wrap">
              <div class="card-title">
                <span class="title-text">{{ dimension.name }}</span>
                <span :class="`el-tag-1 ${dimension.system ? 'dimension-2' : ''}`">{{ dimension.system ? '系统' :
                  '' }}</span>
              </div>
              <div class="card-title-btn" v-if="!dimension.system">
                <el-icon @click="handleEdit(dimension)">
                  <EditPen />
                </el-icon>
                <el-icon @click="handleDelete(dimension)">
                  <Delete />
                </el-icon>

              </div>
            </div>
            <div class="card-meta" v-if="dimension.createdUserName || dimension.createdTime">
              <span v-if="dimension.createdUserName" class="creator">创建人: {{ dimension.createdUserName }} <el-divider
                  direction="vertical" /></span>
              <span v-if="dimension.createdTime" class="create-time">创建时间:{{ dimension.createdTime }}</span>
            </div>
          </div>
          <div class="card-placeholder">
            <span>示例图</span>
          </div>

        </div>
      </div>
    </div>

    <AnalysisDimensionDialog ref="dialogRef" @submit="handleDialogSubmit" @callback="loadDimensions" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { communicateApi } from "@/app_admin/api";
import AnalysisDimensionDialog from './AnalysisDimensionDialog.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { EditPen, Delete } from '@element-plus/icons-vue';

const dimensions = ref([]);
const dialogRef = ref(null);

// 分页相关状态
const pagination = ref({
  pageNumber: 1,
  pageSize: 10,
  total: 0
});

// 获取维度数据
const loadDimensions = async () => {
  console.log('loadDimensions');
  try {
    const param = {
      "pageSize": 1000, // 设置为一个足够大的值，或者根据API支持设置为请求所有数据的参数
      "pageNumber": 1,
      "orderBy": "",
      "asc": false,
      "type": "ANALYSIS"
    }
    const response = await communicateApi.getAnalysisDimensionListData(param);
    if (response.code === 0) {
      dimensions.value = response.data.datas;
      pagination.value.total = response.data.totalNum || 0;
    }
  } catch (error) {
    console.error('获取维度数据失败:', error);
    ElMessage.error('获取维度数据失败');
  }
};

const handleEdit = (dimension) => {
  console.log('编辑维度:', dimension);
  dialogRef.value.show_edit(dimension);
};

const handleAdd = () => {
  dialogRef.value.show_add();
};

const handleDialogSubmit = (formData) => {
  console.log('提交的表单数据:', formData);
  // 这里可以添加逻辑来处理表单提交，例如调用API添加新的维度
  // 然后重新加载维度列表
  loadDimensions();
};

// 删除确认
const confirmDelete = async (dimension) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除维度"${dimension.name}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    return true;
  } catch {
    return false;
  }
};

// 处理删除
const handleDelete = async (dimension) => {
  const confirmed = await confirmDelete(dimension);
  if (!confirmed) return;

  try {
    const response = await communicateApi.deleteAnalysisDimension(dimension.id);
    if (response.code === 0) {
      ElMessage.success('删除成功');
      // 重新加载数据
      loadDimensions();
    } else {
      ElMessage.error(response.message || '删除失败');
    }
  } catch (error) {
    console.error('删除维度失败:', error);
    ElMessage.error('删除失败');
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDimensions();
});
</script>

<style lang="scss" scoped>
.analysis-dimension-container {
  padding: 24px 0;
  height: calc(100vh - 150px);
  overflow: auto;
  box-sizing: border-box;



  .dimension-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;

    .dimension-card {
      background: #F7F8FC;
      border-radius: 12px 12px 12px 12px;
      padding: 12px;
      box-sizing: border-box;
    }

    .card-content {
      .card-placeholder {
        height: 228px;
        background: #FFFFFF;
        border-radius: 8px 8px 8px 8px;
      }

      .card-footer {
        padding: 0 12px 12px 12px;

        .card-title-btn {
          display: flex;
          align-items: center;
          gap: 12px;
          font-size: 16px;
          color: #436BFF;
        }

        .card-title-wrap {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .card-title {
          display: flex;
          align-items: center;



          .title-text {
            font-weight: 600;
            font-size: 14px;
            color: #262626;
            line-height: 22px;
            text-align: left;
          }

          .el-tag-1 {
            padding: 1px 6px;
            border-radius: 4px 4px 4px 4px;
            font-weight: 400;
            line-height: 20px;
            font-size: 12px;
            text-align: center;
            display: inline-block;
            margin-left: 8px;
          }

          .dimension-1 {
            background: rgba(4, 204, 164, 0.1);
            color: #04CCA4;
          }

          .dimension-2 {
            background: #F0F6FF;
            color: #436BFF;
          }

        }

        .card-meta {
          margin-top: 8px;
          font-weight: 400;
          font-size: 12px;
          color: #757575;
          line-height: 18px;
          text-align: left;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
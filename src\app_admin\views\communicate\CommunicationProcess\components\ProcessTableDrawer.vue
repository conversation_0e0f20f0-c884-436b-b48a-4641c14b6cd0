<template>
  <el-drawer v-model="drawerVisible" :before-close="handleClose" size="900px" class="process-table-drawer">
    <template #header>
      <div class="drawer-header">
        <span class="drawer-title">{{ isEditMode ? '编辑' : '新建' }}</span>
      </div>
    </template>

    <div class="drawer-content">
      <el-form ref="formRef" :model="modelForm" :rules="rules" label-position="top" require-asterisk-position="right">
        <div>
          <el-form-item label="流程名称" prop="name">
            <el-input v-model="modelForm.name" show-word-limit maxlength="50" />
          </el-form-item>
        </div>
        <div>
          <el-form-item label="关联" prop="type" v-if="isShangjiType">
            <el-radio-group v-model="modelForm.type" :disabled="isEditMode && modelForm.type == '0'">
              <el-radio :value="1">商机</el-radio>
              <el-radio :value="0">客户</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="沟通环节">
            <div style="width: 100%;" v-sortable @end.prevent="handleDragEnd">
              <div class=" process-description" v-for="(item, index) in modelForm.stages" :key="item.sortIndex || index"
                :ref="`stage-${item.sortIndex}`">
                <div class="process-description-title">
                  <img style="width: 16px; height: 16px;" :src="getOssUrl('icon-sort.png', 3)" alt="">
                </div>
                <div class="process-description-box">
                  <LxCollapse>
                    <LxCollapseItem :title="item.name || `沟通环节${index + 1}`" :name="index">
                      <el-row :gutter="24">
                        <el-col :span="12">
                          <el-form-item :label="`环节名称`" :prop="`stages.${index}.name`"
                            :rules="[{ required: true, message: '请输入环节名称', trigger: 'blur' }]">
                            <el-input v-model="item.name" maxlength="50" show-word-limit />
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="任务模型" :prop="`stages.${index}.taskModelId`"
                            :rules="[{ required: true, message: '请选择任务模型', trigger: 'change' }]">
                            <el-select v-model="item.taskModelId" placeholder="请选择任务模型">
                              <el-option v-for="item in taskOptions" :key="item.id" :label="item.name"
                                :value="item.id" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                      </el-row>


                      <el-row :gutter="24">
                        <el-col :span="12">
                          <el-form-item label="沟通分析" :prop="`stages.${index}.analysisModelId`"
                            :rules="[{ required: true, message: '请选择沟通分析', trigger: 'change' }]">
                            <el-select v-model="item.analysisModelId" placeholder="请选择沟通分析">
                              <el-option v-for="item in analysisOptions" :key="item.id" :label="item.name"
                                :value="item.id" />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12">
                          <el-form-item label="沟通准备">
                            <el-upload :file-list="item.fileList" style="width: 100%;" :http-request="customUpload"
                              :accept="'.xlsx,.xls'" :before-upload="beforeUpload"
                              :on-success="(response, uploadFile) => handleUploadSuccess(item, response, uploadFile)"
                              :on-remove="() => handleUploadRemove(item)" :on-error="handleUploadError"
                              :disabled="isLoading" :limit="1">

                              <el-button><el-icon>
                                  <Upload />
                                </el-icon>上传文件</el-button>
                            </el-upload>
                          </el-form-item>
                        </el-col>
                      </el-row>


                    </LxCollapseItem>
                  </LxCollapse>
                </div>

                <div class="process-description-content">
                  <el-button :disabled="modelForm.stages.length <= 1" link @click.stop="removeStage(index)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </el-form-item>
        </div>


        <el-affix position="bottom" :offset="78" target=".drawer-content">
          <div class="add-behavior-section">
            <el-button class="add-behavior-btn" @click="addStage" text>
              <el-icon>
                <Plus />
              </el-icon>
              <span>添加沟通环节 ({{ modelForm.stages.length }} / 20)</span>
            </el-button>
          </div>
        </el-affix>



      </el-form>
    </div>

    <template #footer>
      <div class="drawer-footer">
        <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Delete, Plus, Upload } from '@element-plus/icons-vue'
import { LxCollapse, LxCollapseItem } from '@/components/LxCollapse'
import { createFile } from '@/js/api'
import { ref, reactive, nextTick } from 'vue'
import { getOssUrl } from "@/js/utils"
const emit = defineEmits(['success', 'close'])

const isShangjiType = computed(() => {
  return g.appStore.isShangji
})
const props = defineProps({
  taskOptions: {
    type: Array,
    default: () => ([])
  },
  analysisOptions: {
    type: Array,
    default: () => ([])
  }
})

const drawerVisible = ref(false)
const formRef = ref(null)
const isEditMode = ref(false)
const currentId = ref(null)
const isLoading = ref(false)
const uploadStatus = ref()

const uploadUrlInfo = ref(null)


const modelForm = reactive({
  name: '',
  type: '',
  stages: [
    {
      sortIndex: 1,
      name: '',
      taskModelId: '',
      analysisModelId: '',
      attachmentUrl: '',
      fileList: []
    }
  ]
})

const stagesTemplate = ref([
  {
    sortIndex: 1,
    name: '',
    taskModelId: '',
    analysisModelId: '',
    attachmentUrl: '',
    fileList: []
  }
])

const handleDragEnd = (event) => {
  // 交换数组元素
  const movedItem = modelForm.stages.splice(event.oldIndex, 1)[0]
  nextTick(() => {
    modelForm.stages.splice(event.newIndex, 0, movedItem)
  })
}

const addStage = () => {
  if (modelForm.stages.length >= 20) {
    ElMessage.error('最多添加20个沟通环节')
    return
  }
  const newIndex = modelForm.stages.length
  modelForm.stages.push({
    sortIndex: newIndex + 1,
    name: '',
    taskModelId: '',
    analysisModelId: '',
    attachmentUrl: '',
    fileList: []
  })

  nextTick(() => {
    // 滚动到新添加的沟通环节
    const newStageElement = document.querySelector(`.process-description:nth-child(${newIndex + 1})`);
    if (newStageElement) {
      newStageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
    // 新增后清除该行校验提示，等待用户输入再触发
    formRef.value?.clearValidate?.()
  });
}

const removeStage = (index) => {
  if (modelForm.stages.length > 1) {
    modelForm.stages.splice(index, 1)
    nextTick(() => {
      formRef.value?.clearValidate?.()
    })
  } else {
    ElMessage.error('至少保留一个沟通环节')
  }
}

const rules = reactive({
  name: [
    { required: true, message: '请输入模型名称', trigger: 'blur' },
    { max: 50, message: '最多输入50个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择关联', trigger: 'change' },
  ]
})
const openDrawer = (data = null) => {
  drawerVisible.value = true


  if (data) {
    console.log("data", data)
    // 编辑模式
    isEditMode.value = true
    currentId.value = data.id
    modelForm.name = data.name
    modelForm.type = data.type
    modelForm.stages = data.stages.map(item => {
      return {
        ...item,
        fileList: item.attachmentUrl ? [{
          name: item.attachmentUrl?.split('/').pop(),
          url: item.attachmentUrl || ''
        }] : []
      }
    })
  } else {

    // 添加模式
    isEditMode.value = false
    currentId.value = null
    modelForm.name = ''
    modelForm.type = ''
    modelForm.stages = [{
      sortIndex: 1,
      name: '',
      taskModelId: '',
      analysisModelId: '',
      attachmentUrl: '',
      fileList: []
    }]
  }
  nextTick(() => formRef.value?.clearValidate?.())
}

const closeDrawer = () => {
  drawerVisible.value = false
  formRef.value?.resetFields()
}

const submitForm = async () => {
  const stageFieldProps = modelForm.stages.flatMap((_, idx) => [
    `stages.${idx}.name`,
    `stages.${idx}.taskModelId`,
    `stages.${idx}.analysisModelId`,
  ])
  try {
    // 先强制校验所有循环项字段，确保错误能正确定位显示
    await formRef.value?.validateField?.(stageFieldProps)
  } catch (_) {
    // 忽略此处异常，交由总体校验处理消息
  }

  const isValid = await formRef.value.validate().catch(() => false)
  if (isValid) {
    const formData = {
      id: currentId.value,
      name: modelForm.name,
      type: isShangjiType.value ? modelForm.type : '0',
      stages: modelForm.stages.map((item, index) => {
        return {
          ...item,
          sortIndex: index + 1
        }
      })
    }
    emit('success', {
      mode: isEditMode.value ? 'edit' : 'add',
      data: formData
    })
  } else {
    ElMessage.error('请检查输入内容')
  }
}

const handleClose = (done) => {
  closeDrawer()
  emit('close')
  done()
}

const resetUpload = () => {
  uploadStatus.value = null
  isLoading.value = false
}

const customUpload = async (options) => {
  try {
    const file = options.file
    const response = await fetch(uploadUrlInfo.value.presignedUrl, {
      method: 'PUT',
      body: file,
      headers: {
        'Content-Type': 'application/octet-stream'
      }
    })
    if (!response.ok) {
      throw new Error('上传失败')
    }
    console.log("customUpload response", options.file)
    options.file.presignedUrl = uploadUrlInfo.value.presignedUrl.split('?')[0]

    // 上传成功后调用 onSuccess，回传创建文件时的元信息，供成功回调使用
    // options.onSuccess({
    //   code: 0,
    //   data: uploadUrlInfo.value
    // })

  } catch (error) {
    options.onError(error)
  }
}
const beforeUpload = async (file) => {
  resetUpload()
  isLoading.value = true

  const isExcel = /\.(xlsx|xls|pdf|doc|docx|ppt|pptx)$/.test(file.name.toLowerCase())
  if (!isExcel) {
    ElMessage.error('只能上传 Excel、PDF、Word、PPT 文件!')
    isLoading.value = false
    return false
  }
  const isLt5M = file.size / 1024 / 1024 < 10
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过 10MB!')
    isLoading.value = false
    return false
  }

  uploadStatus.value = {
    fileName: file.name,
    uploadStepStatus: 'process',
    uploadMessage: '',
    checkStepStatus: 'wait',
    checkMessage: '',
    importStepStatus: 'wait',
    importMessage: '',
    successCount: 0,
    failCount: 0,
    fileUrl: ''
  }

  const param = {
    "module": "communication_process",
    "fileNameExt": file.name.split('.').pop(),
    "isPrivate": 1,
    "bizId": ""
  }
  const res = await createFile(param)
  if (res.code === 0) {
    uploadUrlInfo.value = res.data
    file.uploadObj = res.data
    return true
  } else {
    ElMessage.error(res.message)
    isLoading.value = false
    return false
  }
}
const handleUploadError = (error, uploadFile) => {
  isLoading.value = false
  if (!uploadStatus.value) {
    uploadStatus.value = { fileName: uploadFile?.name || '未知文件', uploadStepStatus: 'error', uploadMessage: '上传请求失败', checkStepStatus: 'wait', importStepStatus: 'wait' }
  } else {
    uploadStatus.value.uploadStepStatus = 'error'
    uploadStatus.value.uploadMessage = '网络错误或服务器无响应'
    uploadStatus.value.checkStepStatus = 'wait'
    uploadStatus.value.importStepStatus = 'wait'
  }
  ElMessage.error(uploadStatus.value.uploadMessage)
}

const handleUploadSuccess = (item, response, uploadFile) => {
  console.log("handleUploadSuccess", item, response, uploadFile)
  item.attachmentUrl = uploadFile.raw.presignedUrl.split('?')[0]
  item.fileList = [{
    name: item.attachmentUrl?.split('/').pop(),
    url: item.attachmentUrl || ''
  }]
  const index = modelForm.stages.indexOf(item)
  if (index > -1) {
    formRef.value?.validateField(`stages.${index}.fileList`)
  }
  isLoading.value = false
}

const handleUploadRemove = (item) => {
  item.fileList = []
  const index = modelForm.stages.indexOf(item)
  if (index > -1) {
    formRef.value?.validateField(`stages.${index}.fileList`)
  }
}

// 暴露方法给父组件
defineExpose({
  openDrawer,
  closeDrawer
})
</script>

<style scoped lang="scss">
.process-table-drawer {
  :deep(.el-drawer__header) {
    padding: 0;
    margin-bottom: 0;
    border-bottom: 1px solid #f0f0f0;
  }

  .add-behavior-section {
    background: #FFFFFF;
    border-radius: 0px 0px 0px 0px;
    margin-top: 12px;
    width: 100%;
    padding: 12px 65px 12px 24px;
    box-sizing: border-box;

    .add-behavior-btn {
      font-weight: 400;
      font-size: 14px;
      color: #436BFF;
      line-height: 22px;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }
    }
  }





}

.drawer-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;

  .drawer-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
}

.drawer-content {
  height: 100%;

  .process-description {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 12px;


    .process-description-title {
      margin-right: 8px;
      padding-top: 18px;
    }

    .process-description-box {
      display: flex;
      flex: 1;
    }

    .process-description-content {
      margin-left: 8px;
      padding-top: 18px;
    }

  }

  :deep(.el-collapse) {
    width: 100%;

    .el-collapse-item__header {
      // padding: 12px 0;
    }
  }

  :deep(.lx-collapse) {
    width: 100%;
  }

  :deep(.lx-collapse-item) {
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.add-file-btn {
  border: 1px solid #D9D9D9;
  border-radius: 6px;
  display: inline-block;
  width: 100px;
  height: 32px;
  cursor: pointer;
  text-align: center;

}

.uploaded-file {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.drawer-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  button {
    padding: 5px 16px;
    border-radius: 6px;
  }

  .cancel-btn {
    border: 1px solid #d9d9d9;
    background: #fff;
    color: #666;
  }

  .confirm-btn {
    background: #436BFF;
    border-color: #436BFF;
  }


}
</style>
<style lang="scss">
.process-table-drawer {
  .el-drawer__footer {
    padding: 16px 24px;
    border-top: 1px solid #E9E9E9 !important;
  }

  .el-drawer__body {
    padding: 24px;
    box-sizing: border-box;
  }

  .el-form-item.is-required:not(.is-no-asterisk).asterisk-right>.el-form-item__label {
    &::after {
      content: '' !important;
      color: #F5222D !important;
      width: 4px;
      height: 4px;
      background: #F5222D !important;
      border-radius: 50%;
      display: inline-block;
      vertical-align: middle;
      margin-right: 4px;
      margin-left: 4px;
    }
  }

  .el-form-item {
    margin-bottom: 16px;
  }
}
</style>
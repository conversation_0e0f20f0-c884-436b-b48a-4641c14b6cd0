<template>
  <div class="communication-summary-page">
    <el-tabs v-model="activeTab" class="summary-tabs">
      <el-tab-pane label="维度" name="dimension">
        <DimensionList v-if="activeTab === 'dimension'" ref="dimensionRef" />
      </el-tab-pane>
      <el-tab-pane label="模板" name="template">
        <TemplateTable v-if="activeTab === 'template'" ref="templateRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import DimensionList from './components/DimensionList.vue';
import TemplateTable from './components/TemplateTable.vue';

const activeTab = ref('dimension');
const dimensionRef = ref();
const templateRef = ref();

</script>

<style lang="scss" scoped>
.communication-summary-page {
  padding: 20px;
}
</style>
<template>
    <div class="av_item_value attachment-uploader">
        <el-button type="default" :icon="Plus" @click="onUpload">上传附件</el-button>
        <div class="attachment-list">
            <div v-for="file in localParam" :key="file.name" class="attachment-item">
                <div class="file-info flex-row">
                    <div class="file-name">{{ file.name }}</div>
                    <img :src="getOssUrl('close.svg')" alt="删除文件" class="file-icon" @click="deleteFile(file)">
                </div>
                <div v-if="file.progress > -1" class="progress-bar">
                    <div class="progress" :style="{ width: file.progress + '%' }"></div>
                </div>
            </div>
        </div>
        <input ref="fileInput" type="file" style="display: none" @change="handleFileChange">
    </div>
</template>

<script setup>
import { Plus } from '@element-plus/icons-vue'
import { uploadConferenceFile } from '@/js/api'
import { ElMessage } from 'element-plus'
import { getOssUrl } from '@/js/utils'

const props = defineProps({
    modelValue: {
        type: Array,
        required: []
    }
})
const localParam = ref(props.modelValue)

const emit = defineEmits(['update:modelValue', 'callback'])

const fileInput = ref(null)

// 添加以下常量定义
const MAX_FILES = 10
const MAX_FILE_SIZE_MB = 1024 // 100MB
const ALLOWED_TYPES = [
    '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.pdf', '.txt', '.rar', '.zip', '.jpg', '.png', '.md', '.epub', '.csv'
]

// 添加 cancelTokenSource 对象存储取消令牌
const cancelTokenSource = ref({})

const onUpload = () => {
    fileInput.value.click()
}

const deleteFile = (file) => {
    // 如果文件正在上传中,取消上传
    if (file.progress > -1 && file.progress < 100 && cancelTokenSource.value[file.name]) {
        cancelTokenSource.value[file.name].abort()
        delete cancelTokenSource.value[file.name]
    }
    localParam.value = localParam.value.filter(item => item !== file)
    updateModelValue()
}

const handleFileChange = (e) => {
    const file = e.target.files[0]
    if (!file) return

    // 检查文件数量限制
    if (localParam.value.length >= MAX_FILES) {
        ElMessage.error(`最多只能上传${MAX_FILES}个文件`)
        e.target.value = ''
        return
    }

    // 检查文件大小
    if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
        ElMessage.error(`文件大小不能超过${MAX_FILE_SIZE_MB}MB`)
        e.target.value = ''
        return
    }

    // 检查文件类型
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    if (!ALLOWED_TYPES.includes(fileExtension)) {
        ElMessage.error('允许上传的文件类型：Word、Excel、PPT、PDF、TXT、RAR、ZIP、JPG、PNG、MD、EPUB、CSV')
        e.target.value = ''
        return
    }

    const fileInfo = {
        name: file.name,
        progress: 0
    }
    localParam.value.push(fileInfo)

    const formData = new FormData()
    formData.append('file', file)
    emit('callback', 'uploading', true)

    uploadConferenceFile(
        formData,
        (xhr, percent) => {
            // 保存取消令牌
            cancelTokenSource.value[file.name] = xhr
            fileInfo.progress = Math.round(percent)
        },
        (response) => {
            // 上传成功后删除取消令牌
            delete cancelTokenSource.value[file.name]
            if (response.code === 0) {
                fileInfo.path = response.data.filePath
                localParam.value = localParam.value.map(item =>
                    item.name === response.data.fileName ? { ...item, path: fileInfo.path } : item
                )
                ElMessage.success('上传成功')
                updateModelValue()
            } else {
                ElMessage.error(response.message || '上传失败')
                localParam.value = localParam.value.filter(item => item !== fileInfo)
            }
        },
        (error) => {
            // 上传失败后删除取消令牌
            delete cancelTokenSource.value[file.name]
            ElMessage.info('已取消上传')
            localParam.value = localParam.value.filter(item => item !== fileInfo)
        }
    )

    // 清空input以便重复上传同一文件
    e.target.value = ''
}

const updateModelValue = () => {
    const isAllUploaded = localParam.value.every(x => x.progress == 100);
    emit('callback', 'uploading', !isAllUploaded)

    //需要删除progress
    const temp = localParam.value.filter(x => x.progress == 100).map(x => {
        delete x.progress
        return x
    });
    emit('update:modelValue', temp)
}

watch(() => props.modelValue, (fileList) => {
    localParam.value = fileList.map(item => ({ ...item, progress: 100 }))
}, { immediate: true })


defineExpose({
    localParam, Plus, getOssUrl
})
</script>

<style lang="scss" scoped>
.attachment-uploader {
    .attachment-list {
        margin-top: 10px;

        .attachment-item {
            color: #999;
            font-size: 13px;
            padding: 8px 10px;
            background: #EBEDEE;
            border-radius: 2px;
            margin-bottom: 8px;

            .file-info {
                margin-bottom: 4px;
                justify-content: space-between;

                .file-name {
                    font-family: PingFangSC, PingFang SC;
                    font-size: 12px;
                    color: #595959;
                    line-height: 20px;
                }

                .file-icon {
                    cursor: pointer;
                    width: 16px;
                    height: 16px;
                }
            }



            .progress-bar {
                height: 2px;
                background: #E5E9EB;
                border-radius: 1px;
                overflow: hidden;

                .progress {
                    height: 100%;
                    background: #409EFF;
                    transition: width 0.3s ease;
                }
            }
        }
    }
}
</style>

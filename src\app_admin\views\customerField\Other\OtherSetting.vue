<template>
    <div class="cf_other_setting">
        <el-form ref="refForm" :model="formData" :rules="rules" label-width="120px" label-position="top">
            <div class="nav-item">
                工商校验规则
            </div>
            <el-form-item label="客户名称工商规则校验" prop="enableBusinessRule">
                <el-select v-model="formData.enableBusinessRule" placeholder="请选择">
                    <el-option label="不强制校验客户名称为工商注册名" :value="0" />
                    <el-option label="强制校验客户名称为工商注册名" :value="1" />
                </el-select>
            </el-form-item>
            <div class="nav-item">
                权限设置
            </div>
            <el-form-item label="" prop="permissions">
                <div class="permission-list">
                    <div class="permission-header">
                        <div class="role-title">角色</div>
                        <div class="role-value">操作权限</div>
                    </div>
                    <div class="permission-item">
                        <div class="role-title">销售</div>
                        <div class="role-value">
                            <el-checkbox-group v-model="formData.permissions">
                                <el-checkbox label="创建客户" :value="1" />
                                <el-checkbox label="导入客户" :value="2" />
                                <el-checkbox label="编辑客户" :value="3" />
                                <el-checkbox label="删除客户" :value="4" />
                            </el-checkbox-group>
                        </div>
                    </div>
                </div>
            </el-form-item>
            <div class="nav-item">
                查重设置
            </div>
            <el-form-item label="">
                <div class="duplicate-rules-container">
                    <div class="vertical-line-container">
                        <div class="and-label">且</div>
                        <div class="vl_border"></div>
                    </div>
                    <div class="rules-list">
                        <div v-for="(ruleId, index) in formData.duplicateFieldIds" :key="index" class="rule-item">
                            <el-select v-model="formData.duplicateFieldIds[index]" placeholder="请选择字段">
                                <el-option v-for="field in availableDisableFields" :key="field.id"
                                    :label="field.fieldName" :value="field.id" :disabled="field.disabled" />
                            </el-select>
                            <el-button link type="danger" @click="removeDuplicateRule(index)" class="delete-button">
                                <el-icon>
                                    <Delete />
                                </el-icon>
                            </el-button>
                        </div>
                        <div class="add-rule-button" v-if="leftFields.length > 0">
                            <el-button type="primary" link @click="addDuplicateRule">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                            </el-button>
                        </div>
                    </div>
                </div>
            </el-form-item>
            <div class="check_double_note flex-col" v-if="duplicateFieldNames">
                <div class="cdn_title">查重规则</div>
                <div class="cdn_txt">若【企业客户】的"{{ duplicateFieldNames }}"内容重复，则判定为重复</div>
            </div>
        </el-form>
        <div class="setting_footer">
            <el-button type="primary" @click="confirmClick">确定</el-button>
        </div>
    </div>

</template>

<script setup>
import { ElMessage } from 'element-plus';
import { Delete, Plus } from '@element-plus/icons-vue';
import { getFormFields } from "@/js/api.js"
import { updateCustomerType, getCustomerConfig } from "@/js/api.js";
import { apiHintWrap } from "@/app_admin/tools/utils.js";

const refForm = ref();
const emit = defineEmits(["callback"]);

const defaultField = [{ id: 'CUSTOMER_NAME', fieldName: '客户名称', fixed: true, disabled: false }]

const defaultForm = {
    enableBusinessRule: 0,
    permissions: [],
    duplicateFieldIds: []
};

const leftFields = computed(() => {
    return [...defaultField, ...(availableFields.value || [])].filter(item => !formData.value.duplicateFieldIds.includes(item.id));
})

const availableDisableFields = computed(() => {
    return (availableFields.value || []).map(x => {
        return {
            ...x,
            disabled: formData.value.duplicateFieldIds.includes(x.id)
        }
    })
})

// 安全显示重复字段名称的计算属性
const duplicateFieldNames = computed(() => {
    if (!availableFields.value || !formData.value.duplicateFieldIds) {
        return '';
    }

    const fieldNames = formData.value.duplicateFieldIds
        .map(x => availableFields.value.find(y => y.id === x)?.fieldName)
        .filter(name => name) // 过滤掉undefined的值
        .join('、');

    return fieldNames;
})

const availableFields = ref(defaultField);
const formData = ref({ ...defaultForm });

const getCustomerPageFields = () => {
    return new Promise((resolve, reject) => {
        getFormFields('CUSTOMER_TPL').then(res => {
            if (res.code == 0) {
                availableFields.value = [...defaultField, ...res.data.filter(x => x.fieldType !== 'Divider')];
                resolve(true);
            } else {
                ElMessage.error(res.msg || '获取失败');
                reject(false);
            }
        })
    })
}

const initLoad = () => {
    getCustomerConfig().then((res) => {
        formData.value = { ...defaultForm, ...res.data };
    });
    getCustomerPageFields();
};


const confirmClick = () => {
    if (!refForm.value) return;
    refForm.value.validate((valid, fields) => {
        if (valid) {
            const data = toRaw(formData.value);
            apiHintWrap(updateCustomerType(data), "更新客户").then(
                ({ status, resp }) => {
                    if (status) {
                        formData.value = { ...defaultForm };
                        emit("callback", "reload", resp.data);
                    }
                }
            );
        } else {
            ElMessage.warning('请检查表单必填项');
        }
    });
};

const addDuplicateRule = () => {
    if (!formData.value.duplicateFieldIds) {
        formData.value.duplicateFieldIds = [];
    }
    if (leftFields.value.length > 0) {
        const firstId = leftFields.value[0].id;
        formData.value.duplicateFieldIds.push(firstId);
    }
};

const removeDuplicateRule = (index) => {
    formData.value.duplicateFieldIds.splice(index, 1);
};

const rules = reactive({
});

defineExpose({ initLoad, formData, rules, leftFields });
</script>

<style lang="scss" scoped>
.cf_other_setting {
    display: flex;
    padding: 24px 0;
    flex-direction: column;

    .el-form {
        width: 100%;
        height: calc(100vh - 216px);
        overflow: auto;

        .nav-item {
            padding: 2px 12px;
            margin-bottom: 24px;
            cursor: pointer;
            border-left: 3px solid #436BFF;
            font-size: 14px;
            height: 12px;
            line-height: 12px;

            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 16px;
            color: #262626;
            text-align: left;
            font-style: normal;
        }

        .permission-list {
            width: 100%;

            .permission-header {
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-bottom: 16px;
                height: 50px;
                background: #F9FAFC;
                box-shadow: inset 0px -1px 0px 0px #E9E9E9;
                border-radius: 4px 4px 0px 0px;
                padding: 0 24px;
            }

            .permission-item {
                margin-bottom: 16px;
                display: flex;
                flex-direction: row;
                box-shadow: inset 0px -1px 0px 0px #E9E9E9;

                .role-title {
                    font-size: 14px;
                    color: #262626;
                    margin-bottom: 12px;
                    font-weight: 500;
                    width: 30px;
                    margin-left: 24px;
                }

                .el-checkbox-group {
                    display: flex;
                }
            }

            .role-value {
                margin-left: 24px;
            }
        }

        .duplicate-rules-container {
            display: flex;
            background: #F9FAFC;
            align-items: flex-start;
            border-radius: 8px;
            padding: 15px 10px 10px 31px;
            position: relative;
            min-height: 60px;
            width: 100%;

            .vertical-line-container {
                display: flex;
                flex-direction: row;
                align-items: center;
                margin-right: 15px;
                padding-top: 5px;
                align-self: stretch;
                position: relative;
                width: 40px;


                .and-label {
                    font-size: 14px;
                    color: #606266;
                    position: absolute;
                    transform: translateX(-50%);
                    background: #fff;
                    padding: 0 3px;
                    z-index: 1;
                    line-height: 1;
                }

                .vl_border {
                    border-radius: 4px;
                    border-left: 1px solid #dcdfe6;
                    border-top: 1px solid #dcdfe6;
                    border-bottom: 1px solid #dcdfe6;
                    width: 20px;
                    margin-left: 15px;
                    top: -5px;
                    height: calc(100% - 40px);
                    position: relative;
                    min-height: 30px;
                }

            }

        }

        .rules-list {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding-bottom: 15px;
        }

        .rule-item {
            display: flex;
            align-items: center;

            .el-select {
                flex-grow: 1;
                margin-right: 8px;
            }

            .delete-button {
                color: #F56C6C;
                font-size: 16px;
            }
        }

        .rule-item-placeholder {
            color: #a8abb2;
            font-size: 14px;
            padding: 5px 0;
            text-align: center;
            min-height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .check_double_note {
            padding: 24px;
            background: #F9FAFC;
            border-radius: 8px;

            .cdn_title {
                height: 24px;
                font-size: 14px;
                color: #262626;
                line-height: 24px;
            }

            .cdn_txt {
                height: 24px;
                font-size: 14px;
                color: #8C8C8C;
                line-height: 24px;
            }

        }
    }



    .setting_footer {
        padding: 24px 0;
    }
}
</style>
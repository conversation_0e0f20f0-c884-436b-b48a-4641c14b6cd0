<template>
    <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <linearGradient x1="100%" y1="50%" x2="0%" y2="50%" id="lgvideo-1">
                <stop stop-color="#78A4FF" offset="0%"></stop>
                <stop stop-color="#436BFF" offset="100%"></stop>
            </linearGradient>
        </defs>
        <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="异步上传_多文件" transform="translate(-876.000000, -562.000000)">
                <g id="图标/16px/close-+-上传中，请不要关闭浏览器页面-+-编组-+-编组备份-+-编组备份-2-+-编组备份-3蒙版"
                    transform="translate(848.000000, 404.000000)">
                    <g id="编组-6" transform="translate(16.000000, 144.000000)">
                        <g id="icon/文档/Excel" transform="translate(12.000000, 14.000000)">
                            <polygon id="路径" points="0 0 32 0 32 32 0 32"></polygon>
                            <path
                                d="M21.3333333,2.66666667 L28,9.33333333 L28,28.0109333 C28,28.7413333 27.4068,29.3333333 26.6754667,29.3333333 L5.32453333,29.3333333 C4.59301333,29.3333333 4,28.7262667 4,28.0109333 L4,3.98906667 C4,3.25873333 4.59326667,2.66666667 5.32453333,2.66666667 L21.3333333,2.66666667 Z"
                                id="路径" fill="#E6EEFF" fill-rule="nonzero"></path>
                            <path
                                d="M20.0010667,15.556 L13.4958667,11.2191333 C13.4082667,11.16072 13.3053333,11.12956 13.2,11.12956 C12.9054667,11.12956 12.6666667,11.3683333 12.6666667,11.6628933 L12.6666667,20.3365333 C12.6666667,20.4418667 12.6978667,20.5448 12.7562667,20.6324 C12.9196,20.8774667 13.2508,20.9437333 13.4958667,20.7802667 L20.0010667,16.4434667 C20.0596,16.4044 20.1098667,16.3541333 20.1489333,16.2956 C20.3124,16.0505333 20.2461333,15.7193333 20.0010667,15.556 Z"
                                id="路径" fill="url(#lgvideo-1)" fill-rule="nonzero"></path>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>

<script>
export default {
    name: 'VideoIcon',
}
</script>

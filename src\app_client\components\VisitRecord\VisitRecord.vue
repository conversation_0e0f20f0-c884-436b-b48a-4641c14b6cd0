<template>
  <div :class="`visit_wrap ${props.team ? 'team_visit_wrap' : 'my_visit_wrap'} `">
    <slot name="header"></slot>
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas" class="dl_wrap">
      <template #_header_left>
        <div class="vline1 flex-row">
          <BtnUpload v-show="enable_edit" />
          <BtnBatch ref="refBatch" @reload="onSearch" v-show="enable_edit" />
          <BtnExport :param="datas.param" :team="props.team" />
        </div>
        <div class="vline2 flex-row">
          <DrawerSelectMixed ref="refUser" @callback="onSelectUser" type="user" :rootDeptId="rootDeptId"
            v-if="props.team" />
          <BtnDate v-model:start="datas.param.startTime" v-model:end="datas.param.endTime" @reload="onDateChange"
            defaultType="last_month" />
          <CrmStatus ref="refStatus" v-model:value="datas.param.syncStatus" @reload="onSearch" v-if="isShowCrmStatus" />
          <ReportStatus ref="refReportStatus" v-model:value="datas.param.reportStatus" @reload="onSearch" />
          <BtnPopMore @change="onMoreFiltersChange" />
        </div>
      </template>
      <template #_header_bottom>
        <div class="vline3">
          数据看板仅统计有效沟通数据；处理状态为【处理失败、文件处理中、会话字幕量不足】标识为无效数据
          <span class="link" @click="onShowInvalid">查看无效数据</span>
        </div>
        <StaticPad ref="refStatic" />
      </template>
      <template #col_subject="{ row }">
        <div :id="row.conferenceId">
          {{ row.subject }}
        </div>
      </template>
      <template #col_recordDurationSeconds="{ row }">
        {{ getSecondsShowTime(row.recordDurationSeconds) }}
      </template>
      <template #col_deptName="{ row }">
        <ColDept :name="row.deptName" />
      </template>
      <template #col_syncStatus="{ row }">
        <ColStatus :name="row.syncStatus" />
      </template>
      <template #col_reportStatus="{ row }">
        <ColReportStatus :row="row" />
      </template>
      <template #col_managerViewed="{ row }">
        {{ row.managerViewed ? "是" : "否" }}
      </template>
      <template #col_salesMateTags="{ row }">
        {{ row.salesMateTags || "-" }}
      </template>
      <template #col_salesGoal="{ row }">
        {{ row.salesGoal ? row.salesGoal : "-" }}
      </template>
      <template #col_salesMateCustomerName="{ row }">
        {{ row.salesMateCustomerName || "-" }}
      </template>
      <template #col_salesAbilityScore="{ row }">
        {{ row.salesAbilityScore ? row.salesAbilityScore : "-" }}
      </template>
      <template #col_taskCompleteRatio="{ row }">
        {{ row.taskCompleteRatio ? row.taskCompleteRatio + "%" : "-" }}
      </template>
      <template #col_salesAchievementStatus="{ row }">
        <div v-if="row.salesAchievementStatus" class="csa_col flex-row">
          <div class="csa">
            {{ row.salesAchievementStatus }}
          </div>
          <el-tooltip effect="dark" :content="row.salesAchievementAnalysis" placement="top-start" raw-content>
            <el-icon v-show="row.salesAchievementAnalysis" class="ccol_achi_status">
              <ArrowRight />
            </el-icon>
          </el-tooltip>
        </div>
        <div v-else>
          -
        </div>
      </template>
      <template #_link_post="{ row }">
        <el-button type="danger" text @click="linkClickDelete(row)" v-if="enable_edit">
          删除
        </el-button>
        <Operate :row="row" v-show="enable_edit" />
      </template>
    </MyTable>
    <DeleteConfirm ref="deleteConfirmRef" title="确认彻底删除会议录制?" content="删除后将无法恢复文件" confirm-text="删除" cancel-text="取消"
      @confirm="handleDeleteConfirm" />
  </div>
</template>

<script setup>
import { getCustomerMeets, deleteRecordByConfId } from "@/app_client/tools/api.js";
import { getSecondsShowTime, getUrlParam, removeURLParams } from "@/js/utils.js";
import { getDefaultDateRange } from "@/app_client/tools/utils.js";
import MyTable from "@/components/Table.vue";
import BtnUpload from "./BtnUpload";
import BtnBatch from "./BtnBatch";
import BtnExport from "./BtnExport.vue";
import BtnDate from "@/app_client/components/BtnDate.vue";
import CrmStatus from "./CrmStatus.vue";
import StaticPad from "./static";
import ColDept from "@/app_client/components/ColDept.vue";
import ColStatus from "./ColStatus.vue";
import ColReportStatus from "./ColReportStatus.vue";
import Operate from "./operate";
import ReportStatus from "./ReportStatus.vue";
import BtnPopMore from './BtnPopMore.vue'
import { ArrowRight } from '@element-plus/icons-vue'
import DrawerSelectMixed from "@/components/DrawerSelect/DrawerSelectMixed.vue";
import DeleteConfirm from "@/components/DeleteConfirm.vue";

const currentRow = ref({})
const isShowCrmStatus = ref(false);
const refReportStatus = ref();
const props = defineProps({
  team: {
    type: Boolean,
    required: false,
    default: false,
  },
});

const emit = defineEmits(["callback"]);
const refTable = ref();
const refStatic = ref();
const refBatch = ref();
const refStatus = ref();
const enable_edit = ref(!props.team);
const refUser = ref();
const deleteConfirmRef = ref()

//每行数据的删除按钮
const linkClickDelete = (row) => {
  deleteConfirmRef.value.show()
  currentRow.value = row
};

const handleDeleteConfirm = () => {
  deleteRecordByConfId(currentRow.value.conferenceId).then((resp) => {
    if (resp.code == 0) {
      ElMessage.success("删除成功");
      onSearch();
    }
  });
};

const onSelectUser = (action, data) => {
  datas.param.userIds = data.users.map((x) => x.id);
  onSearch();
};

const _getMyTeemMeets = (p) => {
  const param = { ...p };
  if (typeof param.dptIds == "string") {
    param["dptIds"] = [param["dptIds"]];
  }
  return new Promise((resolve, reject) => {
    getCustomerMeets(props.team, param).then((resp) => {
      if (resp.code == 0) {
        const { totalNum } = resp.data;
        refStatic.value && refStatic.value.init(resp.data);
        resolve({
          code: 0,
          data: {
            datas: resp.data.datas || [],
            totalNum,
          },
        });
      } else {
        reject(resp);
      }
    });
  });
};

const _urlDelete = (row) => {
  return deleteRecordByConfId(row.conferenceId);
};

const _getColumns = () => {
  const col1 = ["subject", "createdTime", "recordDurationSeconds"];
  const col_team = ["hostName", "deptName", "managerViewed"];
  const col2 = ["salesMateTags", "salesMateCustomerName", "reportStatus", "salesGoal", 'salesAchievementStatus', "salesAbilityScore", "taskCompleteRatio"];
  if (g.appStore.getFuncStatus("sales_crm_update_status")) {
    col2.push("syncStatus");
  }
  const columns = props.team ? [...col1, ...col_team, ...col2] : [...col1, ...col2];
  return columns;
};

const onShowInvalid = () => {
  datas.param.reportStatus = ["RECORD_FAIL", "LITTLE_CONTENT", "IN_PROCESS"];
  refReportStatus.value.updateValue(datas.param.reportStatus);
  onSearch();
};

let _targetStatus = getUrlParam('targetStatus', '-1')
let _abilityStatus = getUrlParam('abilityStatus', -1, 'int')
let _taskStatus = getUrlParam('taskStatus', -1, 'int')
const { startTime, endTime } = getDefaultDateRange()
let _reportStatus = []
if (_abilityStatus != -1) {
  _reportStatus = ["SUCCESS", "FAIL", "NEED_GENERATE", "NEED_ANNOTATION"]
}

const datas = reactive({
  tableid: props.team ? 'visit_record_team' : 'visit_record',
  param: {
    meetingType: 7,
    startTime,
    endTime,
    syncStatus: [],
    reportStatus: _reportStatus,
    targetStatus: _targetStatus,
    abilityStatus: _abilityStatus,
    taskStatus: _taskStatus,
    dptIds: [], //要查询的部门id
    tags: [],
  },
  need_init_load: false,
  need_header: true,
  form: {},
  view_txt: "查看详情",
  modal_type: "link",
  search_ph: "输入沟通主题、客户/销售名称搜索",
  delete_hint_column: "subject",
  show_link_column: true,
  show_link_view: true,
  show_link_delete: false,
  show_link_edit: enable_edit.value,
  enable_checkbox: enable_edit.value,
  columns: _getColumns(),
  template: [
    "subject",
    "recordDurationSeconds",
    "salesMateTags",
    "salesMateCustomerName",
    "deptName",
    "reportStatus",
    "syncStatus",
    "salesGoal",
    "managerViewed",
    "salesAbilityScore",
    "salesAchievementStatus",
    "taskCompleteRatio"
  ],
  always_show_columns: ["subject"],
  column_widths: {
    salesGoal: 200,
  },
  default_select_columns: _getColumns(),
  urlGet: _getMyTeemMeets,
  urlDelete: _urlDelete,
});
const rootDeptId = computed(() => datas.param.dptIds.length > 0 ? datas.param.dptIds[0] : '');
const onSearch = () => {
  refTable.value.search();
};

const onDateChange = () => {
  removeURLParams(['startDate', 'endDate'])
  onSearch();
}

const cbDatas = (action, data) => {
  if (action == "check_row") {
    refBatch.value.setChecked(data.checked);
  } else if (action == "init_view") {
    g.clientStore.viewPlanRecord(data);
  } else if (action == "init_edit") {
    refBatch.value.showEdit(data);
  }
};

const setDeptIds = (ids) => {
  datas.param.dptIds = ids;
  if (props.team) {
    refUser.value.reset();
  }
  onSearch();
};

const onMoreFiltersChange = (filters) => {
  datas.param.targetStatus = filters.targetStatus
  datas.param.abilityStatus = filters.abilityStatus
  datas.param.taskStatus = filters.taskStatus
  onSearch()
}

onMounted(() => {

  g.emitter.on("file_uploaded", (page) => {
    if (page == "visit") {
      onSearch();
    }
  });

  isShowCrmStatus.value = g.appStore.getFuncStatus("sales_crm_update_filter");
});

onUnmounted(() => {
  g.emitter.off("file_uploaded");
});

defineExpose({
  refStatic, MyTable, cbDatas, refTable, BtnUpload, BtnBatch, BtnExport, BtnDate,  StaticPad,
  DrawerSelectMixed, Operate, refBatch, getSecondsShowTime, setDeptIds, props, ColDept, ColStatus, refStatus,
  enable_edit, refReportStatus, CrmStatus, ReportStatus, onSelectUser,
});

defineOptions({
  name: "VisitRecord",
});
</script>

<style lang="scss" scoped>
.visit_wrap {
  overflow: auto;
  background: #fff;

  .table_wrap {

    :deep(.search_box) {
      .search_input {
        width: 263px;
      }
    }
  }

  .vline1 {
    gap: 12px;
    width: 100%;
  }

  .vline2 {
    gap: 12px;
    // width: 100%;
  }

  .vline3 {
    height: 20px;
    font-size: 12px;
    color: #8c8c8c;
    font-style: normal;
    margin: 24px 0;


    .link {
      color: var(--el-color-primary);
      cursor: pointer;
    }
  }

  .vline4 {
    margin-bottom: 16px;
  }

  .col_operation_ {
    .el-button {
      padding: 0;
    }
  }

  .ccol_achi_status {
    margin-left: 4px;
    margin-top: 5px;
    cursor: pointer;
  }
}

.team_visit_wrap {

  .table_wrap {
    padding: 24px 0;
  }

  :deep(table) {
    width: 2500px !important;
  }

}

.my_visit_wrap {
  .table_wrap {
    padding: 0 0 24px 0;
  }



  :deep(.table_class) {
    height: 550px;

    table {
      min-width: 2000px !important;

      .col_operation_ {
        width: 200px;
      }
    }
  }
}
</style>

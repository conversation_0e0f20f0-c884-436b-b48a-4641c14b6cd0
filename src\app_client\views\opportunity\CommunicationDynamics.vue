<template>
    <div class="communication-dynamics" @click="onClick">
        <div class="timeline-header">
            <div class="date-marks">
                <span class="date-mark">05/01</span>
                <span class="date-mark">06/15</span>
                <span class="date-mark">06/29</span>
            </div>
        </div>
        <div class="timeline-container">
            <div class="timeline-line"></div>
            <div class="communication-points">
                <div v-for="(point, index) in communicationPoints" :key="index" class="communication-point"
                    :class="point.type" :style="{ left: point.position + '%' }" @mouseenter="onHover(point)"
                    @mouseleave="onLeave">
                    <el-popover :width="300" trigger="hover" placement="top" v-if="point.details">
                        <template #reference>
                            <div class="point-dot"></div>
                        </template>
                        <div class="point-details">
                            <div class="detail-title">{{ point.details.title }}</div>
                            <div class="detail-time">{{ point.details.time }}</div>
                            <div class="detail-desc">{{ point.details.description }}</div>
                        </div>
                    </el-popover>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps(['row']);
const emit = defineEmits(['callback']);

// 根据行数据生成沟通点
const communicationPoints = computed(() => {
    const points = [];

    // 使用props.row中的communicationActivities数据
    const activities = props.row?.communicationActivities || [];

    // 计算时间轴位置
    const dateMap = {
        '2024-05-01': 10,
        '2024-06-15': 50,
        '2024-06-29': 85
    };

    activities.forEach((activity, index) => {
        const position = dateMap[activity.date] || 10 + (index * 20);
        points.push({
            type: activity.type,
            position: position,
            date: activity.date,
            details: {
                title: activity.title,
                time: activity.date,
                description: getActivityDescription(activity.type, activity.title)
            }
        });
    });

    return points;
});

const getActivityDescription = (type, title) => {
    const descriptions = {
        'meeting': '会议沟通，讨论项目进展和方案细节',
        'call': '电话沟通，确认需求和安排后续工作',
        'email': '邮件沟通，发送方案文档和资料',
        'visit': '现场拜访，深入了解客户需求'
    };
    return descriptions[type] || '沟通活动';
};

const onClick = () => {
    emit("callback", props.row, "communication");
};

const onHover = (point) => {
    // 处理悬停事件
    console.log('Hover on communication point:', point);
};

const onLeave = () => {
    // 处理离开事件
};

defineExpose({
    communicationPoints,
    onClick
});
</script>

<style lang="scss" scoped>
.communication-dynamics {
    position: relative;
    width: 100%;
    height: 60px;
    cursor: pointer;
    padding: 8px 0;

    .timeline-header {
        position: relative;
        height: 20px;
        margin-bottom: 8px;

        .date-marks {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;

            .date-mark {
                font-size: 10px;
                color: #8c8c8c;
                font-weight: 400;
            }
        }
    }

    .timeline-container {
        position: relative;
        height: 20px;

        .timeline-line {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #f0f1f5;
            transform: translateY(-50%);
        }

        .communication-points {
            position: relative;
            height: 100%;

            .communication-point {
                position: absolute;
                top: 50%;
                transform: translateY(-50%);

                .point-dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    cursor: pointer;
                    transition: all 0.3s ease;

                    &:hover {
                        transform: scale(1.2);
                    }
                }

                &.meeting .point-dot {
                    background-color: #5B8FF9;
                }

                &.call .point-dot {
                    background-color: #25D954;
                }

                &.email .point-dot {
                    background-color: #F6BD16;
                }

                &.visit .point-dot {
                    background-color: #FF75AD;
                }
            }
        }
    }

    .point-details {
        padding: 12px;

        .detail-title {
            font-weight: 600;
            font-size: 14px;
            color: #262626;
            margin-bottom: 4px;
        }

        .detail-time {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 8px;
        }

        .detail-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.4;
        }
    }
}
</style>

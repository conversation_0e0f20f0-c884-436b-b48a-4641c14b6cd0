<template>
  <div class="category-section" :class="{ active: active }" @click="$emit('click')">
    <div class="section-header">
      <h3>{{ title }}</h3>

      <el-tooltip class="box-item" effect="dark" :content="hint" :raw-content="true">
        <el-icon>
          <Question />
        </el-icon>
      </el-tooltip>
    </div>
    <div class="section-content" :class="{ 'grid-3': itemsPerRow == 2, 'grid-2': itemsPerRow == 1 }">
      <div v-for="item in items" :key="item.field" class="metric-item">
        <div class="label">{{ lang(item.field) }}</div>
        <div class="value-container">
          <div class="value">{{ formatValue2(item.field, item.value) }}</div>
          <div v-if="isReport && item.diff_value" class="diff-value" :class="{
            'increase': Number(item.diff_value) > 0,
            'decrease': Number(item.diff_value) < 0
          }">
            <el-icon class="trend-icon">
              <CaretTop v-if="Number(item.diff_value) > 0" />
              <CaretBottom v-if="Number(item.diff_value) < 0" />
            </el-icon>
            {{ Number(item.diff_value) > 0 ? '+' : '' }}{{ item.diff_value }}%
          </div>
          <div v-else-if="isReport" class="diff-value neutral">-</div>
        </div>
      </div>
    </div>
    <div class="active-indicator"></div>
  </div>
</template>

<script setup>
import Question from "@/icons/question.vue";
import lang from '@/js/lang'
import { CaretTop, CaretBottom } from '@element-plus/icons-vue'
import { formatValue } from '../misc'
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  hint: {
    type: String,
    required: false
  },
  items: {
    type: Array,
    required: true
  },
  itemsPerRow: {
    type: Number,
    default: 2,
    validator: (value) => [1, 2].includes(value)
  },
  active: {
    type: Boolean,
    default: false
  },
  isReport: {
    type: Boolean,
    default: false
  },
  isDept: {
    type: Boolean,
    default: false
  }
});

const formatValue2 = (field, value) => {
  return formatValue(value, field?.toLowerCase().indexOf('average') > -1 ? 2 : 0)
}


</script>

<style lang="scss" scoped>
.category-section {
  flex-grow: 1;
  background: #fff;
  border-radius: 8px;
  padding: 24px 32px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 8px;
  border: 1px solid #D9D9D9;


  &.active {
    background: #F5F8FF;
    border: 1px solid #fff;

    .active-indicator {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 3px;
      background: #436BFF;
      border-radius: 0 0 8px 8px;
    }
  }

  .section-header {
    display: flex;
    align-items: center;
    flex-direction: row;
    margin-bottom: 16px;

    h3 {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin: 0;
    }

    .el-icon {
      margin-left: 8px;
      margin-top: 2px;
      color: #8C8C8C;
    }
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    &.grid-3 {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
    }

    &.grid-2 {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .metric-item {
    .value-container {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .value {
      font-size: 20px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 4px;
    }

    .diff-value {
      display: flex;
      align-items: center;
      font-size: 14px;
      font-weight: 500;

      &.increase {
        color: #52C41A;
      }

      &.decrease {
        color: #FF4D4F;
      }

      &.neutral {
        color: #8C8C8C;
      }

      .trend-icon {
        margin-right: 2px;
      }
    }

    .label {
      font-size: 12px;
      color: #8C8C8C;
    }
  }
}
</style>
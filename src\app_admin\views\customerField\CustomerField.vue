<template>
  <div class="customer_field_wrap">

    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick" v-if="isToB">
      <el-tab-pane label="字段设置" name="field" lazy>
        <FieldList ref="refFieldList" type="CUSTOMER_TPL" />
      </el-tab-pane>
      <el-tab-pane label="其它设置" name="other" lazy>
        <OtherSetting ref="refOtherSetting" />
      </el-tab-pane>
    </el-tabs>
    <div v-else>
      <FieldList ref="refFieldList" type="CUSTOMER_TPL" />
    </div>
  </div>
</template>
<script setup>
import FieldList from './Field/FieldList.vue';
import OtherSetting from "./Other/OtherSetting.vue"

const activeName = ref('field')
const refFieldList = ref(null)
const refOtherSetting = ref(null)
const isToB = computed(() => g.appStore.isToB)

const handleClick = () => {
  nextTick(() => {
    nextTick(() => {
      if (activeName.value == 'field') {
        refFieldList.value.initLoad()
      } else if (activeName.value == 'other') {
        refOtherSetting.value.initLoad()
      }
    })
  })
}

defineExpose({
  activeName,
  FieldList
})

onMounted(() => {
  handleClick()
})

</script>

<style lang="scss">
.customer_field_wrap {
  padding: 12px 24px 24px 24px;
}
</style>

<template>
  <div class="customer_list_wrap">
    <MyTable ref="refTable" :cfg="datas" @callback="cbDatas">
      <template #_header_left>
        <el-button type="primary" @click="onAdd" v-if="pageAccess.customer_add_opr">新建</el-button>
        <BtnFileImport ref="refFileImport" @reload="onSearch" />
        <BtnExport ref="refExport" :isAdmin="true" />
        <BtnDelete ref="refBtnDelete" @reload="onSearch" />
      </template>

      <template #_header_filter>
        <SelectSourceType v-model:value="datas.param.createType" @reload="onSearch" />
      </template>

      <template #col_customer_name="{ row }">
        {{ row.name }}
      </template>
    </MyTable>
    <DrawerUpload ref="refDrawerUpload" :isAdmin="true" />
    <DrawerCustomerForm ref="refDiaCustomer" @reload="onSearch" :isAdmin="true" />
  </div>
</template>

<script setup>
import MyTable from "@/components/Table.vue";
import { getCustomerManage } from "@/app_admin/tools/api.js"
import { deleteCustomer } from "@/js/api.js"
import BtnFileImport from "@/components/BtnCustomerFile/BtnFileImport.vue";
import BtnExport from "@/components/BtnCustomerFile/BtnFileExport.vue";
import BtnDelete from "./BtnDelete.vue";
import SelectSourceType from "./SelectSourceType.vue";
import DrawerCustomerForm from "@/components/BtnAddCustomer/DrawerCustomerForm.vue";

const pageAccess = ref({})
const refDiaCustomer = ref()
const refTable = ref(null);
const refFileImport = ref(null);
const refExport = ref(null);
const refBtnDelete = ref()

const getCustomerManageWrap = (param) => {
  return new Promise((resolve, reject) => {
    const { createType, pageNumber, pageSize } = param;
    const newParam = {
      createType, pageSize,
      pageNumber: pageNumber - 1,
      keywords: param.searchKey,
    }

    getCustomerManage(newParam).then(resp => {
      // 处理 fieldValues 数据
      if (resp.data && resp.data.datas) {
        resp.data.datas = resp.data.datas.map(item => {
          if (item.formData && item.formData.fieldValues) {
            const fieldData = {};
            item.formData.fieldValues.forEach(field => {
              fieldData[field.fieldId] = field.fieldValue;
            });
            return {
              ...item,
              ...fieldData
            };
          }
          return item;
        });
      }
      resolve(resp)
    })
  })
}

const deleteCustomerWrap = (param) => {
  return new Promise((resolve, reject) => {
    deleteCustomer({
      source: 0,
      ids: [param.id]
    }).then(resp => {
      resolve(resp)
    })
  })
}

const datas = reactive({
  tableid: 'admin_customer_list-v2',
  param: {
    pageSize: 20,
    createType: NaN,
  },
  need_header: true,
  need_init_load: false,
  enable_checkbox: true,
  search_ph: '请输入关键字搜索',
  columns: ['customer_name', 'salesSsoUserNames', "followSsoUserNames", 'hostUserName', 'createdTime'
  ],
  template: ['customer_name'],
  show_link_column: true,
  show_link_delete: false,
  show_link_edit: false,
  delete_hint_column: 'name',
  urlGet: getCustomerManageWrap,
  urlDelete: deleteCustomerWrap,
  column_widths: {
    followSsoUserNames: 200
  }
});

const onAdd = () => {
  refDiaCustomer.value.show_add();
};

const onSearch = () => {
  refTable.value.search();
};

const cbDatas = (action, data) => {
  if (action == 'init_edit') {
    refDiaCustomer.value.show_edit_byid(data.id);
  } else if (action == "check_row") {
    const ids = toRaw(data.checked).map(x => x.id);
    refBtnDelete.value.setIds(ids)
  }
}

// 创建客户操作	customer_add_opr
// 查看客户操作	customer_view_opr
// 导入客户操作	customer_import_opr
// 导出客户操作	customer_export_opr
// 编辑客户操作	customer_edit_opr
// 删除客户操作	customer_del_opr
const setAccessCache = () => {
  const list = [
    "customer_export_opr",
    "customer_view_opr",
    "customer_edit_opr",
    "customer_add_opr",
    "customer_del_opr",
    "customer_import_opr"
  ];
  for (let code of list) {
    const access = g.cacheStore.checkPointActionByCode("customer_list", code)
    // console.log('code', code, access)
    pageAccess.value[code] = access

    if (pageAccess.value.customer_export_opr) {
      nextTick(() => {
        refExport.value.setAccess(pageAccess.value)
      })
    }
    if (pageAccess.value.customer_import_opr) {
      nextTick(() => {
        refFileImport.value.setAccess(pageAccess.value)
      })
    }
  }
  datas.show_link_delete = pageAccess.value.customer_del_opr;
  datas.show_link_edit = pageAccess.value.customer_edit_opr;
  refTable.value.init(toRaw(datas));
};

onMounted(() => {
  g.cacheStore.getUserMenu("admin").then(() => {
    setAccessCache();
  });
});

defineExpose({
  MyTable,
  refDiaCustomer,
  refTable,
  cbDatas,
})
</script>

<style lang='scss'>
.customer_list_wrap {
  padding: 24px;

  .vline1 {
    gap: 12px;
    width: 100%;
  }

  .vline2 {
    gap: 12px;
  }

  .search_input {
    width: 350px !important;
  }

  .table_box .table_class {
    height: calc(100vh - 185px) !important;

    .col_operation_ {
      width: 104px
    }
  }

  .dates_picker {
    margin-right: 10px;
  }
}
</style>
import { defineStore } from 'pinia'
import { getMyUserInfo } from "@/js/api.js"
import { getYxtFuncPoints } from "@/js/api_yxt.js"
import { setStore, getStore, getUser, now, checkInElectron, removeStore, clearStore, toClipboard, setStoreEveryKey, processFuncPoints, isShowTooltip } from "@/js/utils.js"
import { ConstValue } from "@/js/const_value"
import { createWsAgent, reconnectWsAgent } from "@/js/wsAgent.js"

const isElectron = checkInElectron();
const home_page = isElectron ? ConstValue.defaultElectronHomePage : ConstValue.defaultClientHomePage;

const AppStore = defineStore('app', {
    state: () => ({
        home_page,
        user: getUser(),
        funcPointsMap: getStore(ConstValue.keyFuncStatusMap, {}),
        yxtUser: {},
        extFiledLanguage: {},
        wsAgent: null
    }),
    getters: {
        isLogin(state) {
            let token = state.user && state.user.token;
            return !!token;
        },
        isDeptHead(state) {
            return state.user && state.user.manager;
        },
        settings(state) {
            return state.getStore(ConstValue.keySettings)
        },
        isToB(state) {
            return state.user && state.user.orgBusinessType == 0;
        },
        isShangji(state) {
            return state.getFuncStatus('business_opportunity');
        }
    },
    actions: {
        setStore, getStore, removeStore, isShowTooltip,
        addExtFiledLanguage(data) {
            this.extFiledLanguage = { ...this.extFiledLanguage, ...data };
        },
        reloadUserInfo(token = '') {
            return new Promise(async (resolve) => {
                this.user = getUser();
                if (!isElectron) {
                    this.createWsAgent()
                }
                await this.getAllFuncPoints();
                if (this.isLogin || this.user && this.user.token || token) {
                    const nowDate = now('yyyy-MM-dd hh');
                    if (token || getStore(ConstValue.keyLastCheckTokenDate, '') !== nowDate) {
                        getMyUserInfo(token).then(async (res) => {
                            setStore(ConstValue.keyLastCheckTokenDate, nowDate);
                            this.user = res.data
                            setStore(ConstValue.keyUserInfo, res.data);
                            localStorage.setItem('token', res.data.accessToken);
                            this._updateELectronUserInfo(res.data);
                            resolve(true)
                        }).catch((err) => {
                            console.log('getMyUserInfo error', err)
                            this.logout()
                            resolve(false)
                        })
                    } else {
                        resolve(true)
                    }
                } else {
                    console.log('logout1')
                    // this.logout();
                    resolve(false)
                }
            })
        },
        upateDeviceInfo(sn) {
            this.user.audioDeviceCode = sn;
            setStore(ConstValue.keyUserInfo, this.user);
            g.electronStore.getStore('userInfo').then(electron_user => {
                if (electron_user) {
                    electron_user.audioDeviceCode = sn;
                    g.electronStore.setStore('userInfo', electron_user);
                }
            })
        },
        _updateELectronUserInfo(apiUser) {
            if (isElectron) {
                g.electronStore.getStore('userInfo').then(electron_user => {
                    if (electron_user) {
                        const update_user = { ...electron_user, ...apiUser };
                        //console.log('update_user', update_user)
                        g.electronStore.setStore('userInfo', update_user);
                    }
                })
            }
        },
        reflashUserInfo() {
            return new Promise(async (resolve) => {
                const token = this.user && this.user.token || "";
                if (!token) {
                    resolve(false)
                    return
                }
                getMyUserInfo(token).then(async (res) => {
                    if (res.code == 0) {
                        const nowDate = now('yyyy-MM-dd hh');
                        setStore(ConstValue.keyLastCheckTokenDate, nowDate);
                        this.user = res.data
                        setStore(ConstValue.keyUserInfo, res.data);
                        localStorage.setItem('token', res.data.accessToken);
                        this._updateELectronUserInfo(res.data);
                        resolve(true)
                    } else {
                        // this.logout()
                        resolve(false)
                    }
                }).catch((err) => {
                    console.log('reflashUserInfo error', err)
                    // this.logout()
                    resolve(false)
                })
            })
        },
        electronLogin(user) {
            // console.log('electronLogin', user)
            this.user = user;
            setStore(g.cv.keyYxtUserInfo, user.userInfo)
            const yxtUser = JSON.parse(user.userInfo);
            setStoreEveryKey(yxtUser.userInfo)
            setStore(g.cv.keyUserInfo, JSON.stringify(user))
        },
        logout() {
            console.log('logout298')
            this.user = null;
            clearStore();
            g.router.push({ path: '/login' });
        },
        getTitle(path) {
            if (path.startsWith('/admin')) {
                return '绚星销售助手管理后台'
            } else if (path.startsWith('/manage')) {
                return '绚星销售助手企业管理后台'
            } else {
                return '绚星销售助手'
            }
        },
        getAppBaseUrl() {
            return `https://${this.user?.ssoDomain}`
        },
        doCopy(message, hint) {
            toClipboard(message.replace(/<br\s*\/?>/g, "\n")).then(() => {
                ElMessage({
                    message: hint,
                    grouping: true,
                    type: 'success',
                })
            })
        },
        getAllFuncPoints() {
            const that = this;
            return new Promise((resolve) => {
                getYxtFuncPoints(that.user.ssoOrgId).then((resp) => {
                    that.funcPointsMap = processFuncPoints(resp);
                    g.emitter.emit('update_func_points', '')
                    setStore(g.cv.keyFuncStatusMap, that.funcPointsMap)
                    resolve(true)
                }).catch(e => {
                    console.log('error getYxtFuncPoints', e)
                    resolve(false)
                })
            })
        },
        // 获取要素状态
        getFuncStatus(f2) {
            const funcPoint = this.funcPointsMap[f2];
            return funcPoint?.enable || false;
        },
        createWsAgent() {
            this.wsAgent = createWsAgent();
        },
        reconnectWsAgent() {
            if (this.wsAgent) {
                reconnectWsAgent(this.wsAgent);
            } else {
                this.createWsAgent();
            }
        },
        toMobileDownload() {
            const url = `${g.config.postmeet_h5_customer}/index.html#/appDownload`;
            console.log("url", url);
            window.location.href = url;
        },
        getRecordUrl(recordingPath) {
            let url = '';
            if (recordingPath.indexOf("http") == -1) {
                url = `https://x-mate.com${recordingPath}`;
            } else {
                url = recordingPath;
            }
            return url;
        },
        copy() {
            copy(`const local = ${JSON.stringify(localStorage)};
            for (const key in local) {
            localStorage.setItem(key, local[key])
            }`);
        }
    }
})

export default AppStore;
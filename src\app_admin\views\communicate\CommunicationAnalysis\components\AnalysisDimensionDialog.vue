<template>
    <div>
        <el-drawer ref="refModal" :destroy-on-close="true" class="sale_dis_dim_wrap" size="500px"
            v-model="drawerVisible" :close-on-click-modal="false">
            <template #header>
                <div class="drawer-header">
                    <span class="drawer-title">{{ cfg["title"] }}</span>
                </div>
            </template>
            <div v-if="dimensionType == 'SUMMARY'">
                <div class="dim_title">维度选择</div>
                <el-radio-group v-model="formData.systemPreset" class="ml-4 wd_radio" @change="onTypeChange">
                    <el-radio :value="false" size="default">自定义维度</el-radio>
                    <el-radio :value="true" size="default">从模板库选择</el-radio>
                </el-radio-group>
            </div>
            <el-form ref="refForm" :model="formData" label-width="auto" label-position="top" size="default"
                require-asterisk-position="right" :rules="rules" v-show="!formData.systemPreset">
                <el-form-item label="分析维度" prop="name">
                    <el-input v-model.trim="formData.name" rows="1" maxlength="50" type="textarea" show-word-limit
                        placeholder="请输入" />
                </el-form-item>
                <el-form-item class="promot_question_icon_wrap" prop="prompt">
                    <template #label>
                        <div class="flex-row justify-between align-center">
                            <div class="prompt_label">提示词Prompt </div>
                            <div class="flex-row">
                                <el-tooltip>
                                    <template #content>
                                        您可以自定义提示词，系统将基于以下沟通信息进行分析：<br />
                                        沟通文字记录、参会人员信息、沟通主题、客户名称、沟通时间、沟通目标等
                                    </template>
                                    <div class="promot_question_icon">
                                        <QuestionIcon />
                                    </div>
                                </el-tooltip>
                                <div class="right_top_btn flex-row" @click="onOpenAi">
                                    <img :src="getOssUrl('yxt_ai_color.png')" alt="summary" />
                                    <div class="ai_btn">
                                        <span style="color: #733cfe">A</span>
                                        <span style="color: #6c5afd">I</span>
                                        <span style="color: #6578fc">辅</span>
                                        <span style="color: #5e96fb">助</span>
                                        <span style="color: #57b4fa">生</span>
                                        <span style="color: #50d2f9">成</span>
                                    </div>
                                </div>
                            </div>

                        </div>

                    </template>


                    <el-input type="textarea" v-model="formData.prompt" maxlength="5000" :rows="13" show-word-limit
                        placeholder="基于沟通过程记录，使用prompt引导AI生成对应内容" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="drawer-footer">
                    <el-button class="cancel-btn" @click="closeDrawer">取消</el-button>
                    <el-button class="confirm-btn" type="primary" @click="btnOK">确定</el-button>
                </div>
            </template>
            <dimTemplate ref="refTemplate" @callback="cbTemplate" v-show="formData.systemPreset" />
        </el-drawer>
        <DrawerAiPrompt ref="refDrawerAiPrompt" />
    </div>
</template>

<script setup>
import { nextTick, reactive, ref, toRaw } from "vue";
import { communicateApi } from "@/app_admin/api";
import Modal from "@/components/Modal.vue";
import dimTemplate from "./dimTemplate.vue";
import QuestionIcon from "@/app_admin/icons/question.vue";
import DrawerAiPrompt from "./DrawerAiPrompt.vue";
import { getOssUrl } from "@/js/utils";
const emit = defineEmits(["callback", "submit", "reload"]);

const refModal = ref();
const title = ref("");
const refTemplate = ref();
const refForm = ref("");
const currId = ref("");
const dimensionType = ref("");
const refDrawerAiPrompt = ref();
const defaultForm = {
    type: "ANALYSIS",
    prompt: "", //提示词内容
    name: "", //系统内置维度名称
};
const drawerVisible = ref(false);

const formData = ref({ ...defaultForm });

const cfg = {
    width: "700px",
};

const onOpenAi = () => {
    refDrawerAiPrompt.value.show();
};

const onTypeChange = (value) => {
    formData.value["systemPreset"] = value;
    formData.value["systemId"] = 0; //value ? 1 : 0;
    if (value) {
        nextTick(() => {
            refTemplate.value.setChoosed(formData.value["systemId"]);
        });
    }
};

const _resetForm = () => {
    formData.value = { ...defaultForm };
};

const closeDrawer = () => {
    drawerVisible.value = false;
    formRef.value?.resetFields();
};

const show_add = (pid) => {
    currId.value = pid;
    dimensionType.value = g.saleStore.dimensionType;
    formData.value = { ...defaultForm };
    cfg["title"] = "新建";
    drawerVisible.value = true;
    nextTick(() => {
        refForm.value.resetFields();
    });
};

const show_edit = async (data) => {
    currId.value = data.id;
    const obj = await communicateApi.getAnalysisDimension(data.id) || {};

    formData.value = { ...obj.data || {} };
    cfg["title"] = "编辑";
    drawerVisible.value = true;
    if (formData.value.systemPreset) {
        nextTick(() => {
            refTemplate.value.setChoosed(formData.value.systemId);
        });
    }
};

const cbTemplate = (item) => {
    if (!item) {
        return;
    }
    formData.value.systemId = item.id;
    formData.value.name = item.name;
    formData.value.prompt = "";
};

const btnCancel = () => {
    _resetForm();
    drawerVisible.value = false;
};

const btnOK = () => {
    if (!refForm.value) return;

    const { systemPreset } = formData.value;
    const fn = () => {
        const data = toRaw(formData.value);
        if (!data.id) {
            communicateApi.addAnalysisDimension(data)
                .then((resp) => {
                    console.log('bbb', resp)
                    if (resp.code == 0) {
                        ElMessage.success(`${cfg["title"]}成功`);
                        emit("callback");
                        btnCancel();
                    } else {
                        ElMessage.error(`${resp.message}`);
                    }
                })
                .catch((e) => {
                    ElMessage.error(`${cfg["title"]}失败`);
                });
        } else {
            communicateApi.updateAnalysisDimension(data.id, data)
                .then((resp) => {
                    if (resp.code == 0) {
                        emit("callback");
                        ElMessage.success(`${cfg["title"]}成功`);
                        btnCancel();
                    } else {
                        ElMessage.error(`${resp.message}`);
                    }
                })
                .catch((e) => {
                    ElMessage.error(`${cfg["title"]}失败`);
                });
        }
    };
    if (systemPreset) {
        fn();
    } else {
        refForm.value.validate((valid, fields) => {
            if (valid) {
                fn();
            } else {
                console.log("not valid", fields);
            }
        });
    }
};

const rules = reactive({
    name: [{ required: true, message: "请输入分析维度", trigger: "blur" }],
    prompt: [{ required: true, message: "请输入提示词Prompt", trigger: "blur" }],
});

onMounted(() => {
    g.emitter.on('update_defaultSalesMethodology', (value) => {
        formData.value.defaultSalesMethodology = value;
    })
})

onUnmounted(() => {
    g.emitter.off("update_defaultSalesMethodology");
});

defineExpose({
    title,
    dimTemplate,
    onTypeChange,
    cbTemplate,
    refTemplate,
    show_add,
    show_edit,
    formData,
    rules,
    dimensionType,
    QuestionIcon,
    DrawerAiPrompt,
});
</script>

<style lang="scss">
.sale_dis_dim_wrap {
    .dim_title {
        margin: 4px 0;
    }

    .drawer-header {
        display: flex;
        align-items: center;
        padding: 16px 24px;


        .drawer-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
    }

    .wd_radio {
        margin-bottom: 8px;
    }

    .promot_question_icon {
        cursor: pointer;
    }

    .right_top_btn {
        cursor: pointer;

        img {
            width: 20px;
            height: 20px;
            margin: 0 6px;
        }

        .ai_btn {
            width: 75px;
            height: 22px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 500;
            font-size: 14px;
            color: #595959;
            line-height: 22px;
            text-align: left;

        }
    }

}
</style>

<style lang="scss">
.promot_question_icon_wrap {
    .prompt_label::after {
        content: '' !important;
        color: #F5222D !important;
        width: 4px;
        height: 4px;
        background: #F5222D !important;
        border-radius: 50%;
        display: inline-block;
        vertical-align: middle;
        margin-right: 4px;
        margin-left: 4px;
    }

    .el-form-item__label::after {
        width: 0 !important;
        height: 0 !important;
        display: none !important;
    }
}
</style>

const fs = require("fs");
const Form_Data = require('form-data');
import config from "./config";

import { getCurrentDateFormatted } from "./tools";
const path = require("path");
import { uploadLogFile } from './request'

export const writeLog = (filename, ...args) => {
    const logPath = path.join(config.appPath, 'logs');
    const _filename = path.join(config.appPath, 'logs', filename + '.log');

    // 如果logs目录不存在，则创建
    if (!fs.existsSync(logPath)) {
        fs.mkdirSync(logPath, { recursive: true });
    }

    let str = new Date().toLocaleString() + ": ";
    for (const arg of args) {
        if (typeof arg === 'object') {
            str += JSON.stringify(arg) + " "
        } else {
            str += arg + " "
        }
    }
    str += "\r\n"

    console.log(str)

    try {
        if (!fs.existsSync(logPath)) {
            fs.mkdirSync(logPath, { recursive: true });
        }
        let fd = fs.openSync(_filename, 'a');
        fs.appendFileSync(fd, str);
        fs.closeSync(fd);
    } catch (e) {

    }
}

export const errorWriteLocal = (...args) => {
    writeLog(getCurrentDateFormatted(), ...args);
}

export const getLogPath = (logId: string) => {
    if (!logId) return '';
    return path.join(config.appPath, 'logs', `${logId}.log`);
}

export const uploadLog = async (logId: string, participantId: string) => {
    if (!logId) return false;


    try {
        const logPath = getLogPath(logId);
        if (!config.isPackaged) {
            // fs.unlinkSync(logPath);
            return false;
        }

        let fileStream: any = null;

        if (!fs.existsSync(logPath)) {
            errorWriteLocal('Log file not found:', logPath);
            return false;
        }
        fileStream = fs.createReadStream(logPath);
        const formData = new Form_Data();

        // 添加文件大小检查
        const stats = fs.statSync(logPath);
        if (stats.size === 0) {
            errorWriteLocal('Log file is empty:', logPath);
            return false;
        }

        formData.append('logs', fileStream);
        formData.append('participantId', participantId);
        formData.append('confId', logId);
        formData.append('type', 'meet_record');


        const result = await uploadLogFile('common/feedback/log/file', formData);
        errorWriteLocal('Upload log file result:', result);

        // 确保文件流关闭
        if (fileStream) {
            fileStream.close();
            fileStream.destroy();
        }

        // 上传成功后可以选择是否删除文件
        fs.unlinkSync(logPath);
        errorWriteLocal('Upload log file success');
        return true;
    } catch (err) {
        // 确保在出错时也关闭文件流
        if (fileStream) {
            fileStream.close();
            fileStream.destroy();
        }
        errorWriteLocal('Upload log file failed:', {
            error: err,
            message: err.message,
            stack: err.stack
        });
        return false;
    }
}

export const uploadOldLogs = async () => {
    const logPath = path.join(config.appPath, 'logs');
    if (!fs.existsSync(logPath)) {
        errorWriteLocal('Logs directory not found');
        return;
    }

    try {
        const files = fs.readdirSync(logPath);
        const logFiles = files.filter(file =>
            file.endsWith('.log') && file.replace('.log', '').length > 8
        );

        for (const file of logFiles) {
            const logId = file.replace('.log', '');
            const fullPath = path.join(logPath, file);

            try {
                // 读取文件内容获取participantId
                const content = fs.readFileSync(fullPath, 'utf8');
                const lines = content.split('\n');

                // 尝试从第一行和第三行获取participantId
                let participantId = null;
                const participantIdPattern = /participantId\s+(\S+)/;
                const cardIdPattern = /cardId\s+(\S+)/;

                // 检查第一行
                if (lines.length > 0) {
                    const firstLineMatch = lines[0].match(participantIdPattern);
                    if (firstLineMatch) {
                        participantId = firstLineMatch[1];
                    }
                }

                // 如果第一行没有找到，检查第三行
                if (!participantId && lines.length >= 3) {
                    // 先尝试获取participantId
                    const thirdLineMatch = lines[2].match(participantIdPattern);
                    if (thirdLineMatch) {
                        participantId = thirdLineMatch[1];
                    } else {
                        // 如果没有participantId，尝试获取cardId
                        const cardIdMatch = lines[2].match(cardIdPattern);
                        if (cardIdMatch) {
                            participantId = cardIdMatch[1];
                        }
                    }
                }

                if (participantId) {
                    errorWriteLocal('update old log', logId, participantId);
                    // 上传日志文件
                    const uploadSuccess = await uploadLog(logId, participantId);

                    if (uploadSuccess) {
                        errorWriteLocal(`Successfully uploaded and deleted log file: ${file}`);
                    } else {
                        errorWriteLocal(`Failed to upload log file: ${file}`);
                    }
                } else {
                    errorWriteLocal(`No participantId or cardId found in log file: ${file}`);
                }
            } catch (err) {
                errorWriteLocal(`Error processing log file ${file}:`, err);
            }
        }
    } catch (err) {
        errorWriteLocal('Error reading logs directory:', err);
    }
}

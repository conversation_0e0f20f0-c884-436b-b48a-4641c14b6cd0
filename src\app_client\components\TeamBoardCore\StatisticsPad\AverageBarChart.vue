<template>
  <div :class="['statistics-chart', isHalf ? 'sc_half' : 'sc_full']">
    <div class="chart-layout">
      <div class="category-tabs">
        <el-radio-group v-model="selectDimension">
          <div v-for="tab in itemList" :key="tab.field" class="radio-button-wrapper">
            <el-radio-button :value="tab.field">
              <div class="radio-content">
                <span>{{ lang(tab.field) }}</span>
                <el-tooltip v-if="type_column_hints[tab.field]" class="item" effect="dark"
                  :content="type_column_hints[tab.field]" placement="top">
                  <el-icon class="hint-icon" color="#409efc">
                    <Question />
                  </el-icon>
                </el-tooltip>
              </div>
            </el-radio-button>
          </div>
        </el-radio-group>
        <div class="view-detail" @click="handleViewDetail">
          查看{{ categoryInfo[currentCategory].label }}明细
          <el-icon>
            <RightArrow />
          </el-icon>
        </div>
      </div>
      <div class="chart-container" v-if="chartData.length > 0">
        <!-- 平均值显示 -->
        <div class="average-info">
          {{ isEndDept ? '成员' : '团队' }}平均值 {{ formatAverageValue }}
          <div class="bar" :style="{
            width: `${averageValue * lineZoom}px`,
          }"></div>
        </div>

        <!-- 添加垂直平均线 -->
        <div class="vertical-average-line" :style="{
          left: `${103 + averageValue * lineZoom}px`
        }"></div>
        <!-- 柱状图 -->
        <div class="chart">
          <div v-for="(item, index) in chartData" :key="item.label + selectDimension + item.value" :class="[
            'chart-item',
            item.value < averageValue ? 'bad_bar' : 'good_bar',
            item.label === selectedRegion ? 'selected' : ''
          ]" @click="handleRegionClick(item)">
            <div class="label" :title="item.label">{{ item.label }}</div>
            <div class="bar-container">
              <div class="bar" :style="{
                width: `${item.value * lineZoom}px`,
              }"></div>
            </div>
            <div class="value">{{ formatBarValue(item.value, selectDimension) }}</div>
          </div>
        </div>
      </div>
      <el-empty v-else description="暂无数据" />
    </div>
  </div>
</template>

<script setup>
import lang from '@/js/lang'
import RightArrow from '@/icons/right_arrow.vue';
import Question from "@/icons/question.vue";
import { formatBarValue } from '@/app_client/tools/utils.js';
import { extractDeptTree, jsOpenNewWindow, getUrlParam } from "@/js/utils.js"
import { categoryInfo, StatisColumnTypes, type_column_hints } from "../misc.js"
const isShangji = computed(() => g.appStore.isShangji);

const isHalf = computed(() => {
  return g.clientBoardStore.getRightChartType() !== '';
});
const selectDimension = ref('');
const averageValue = ref(0);
const tabs = ref([]);
const selectedRegion = ref('');
const lineZoom = ref(5);
const chartData = ref([]);
const reportData = ref([]);
const deptData = ref({});
const isEndDept = ref(false);
const periodType = ref('');
// const currentCategory = ref('visitPlan');
const currentCategory = computed(() => g.clientBoardStore.category);
const emit = defineEmits(['callback'])

// 计算最大柱子宽度和缩放比例
const calculateZoom = () => {
  if (chartData.value.length === 0) return;

  // 找出最大值
  const maxValue = Math.max(...chartData.value.map(item => item.value || 0));
  if (maxValue === 0) {
    lineZoom.value = 0;
    return;
  };

  // 获取容器宽度（减去左侧标签宽度和右侧数值宽度的空间）
  const containerWidth = 0.5 * (window.innerWidth - 400); // 预留左侧标签(103px)和右侧数值(约40px)的空间

  // 期望最大柱子的宽度是容器的70%
  const targetMaxWidth = containerWidth * 0.7;

  // 计算新的缩放比例
  lineZoom.value = targetMaxWidth / maxValue;
}

const formatAverageValue = computed(() => {
  return formatBarValue(averageValue.value, selectDimension.value, 2);
});

const handleRegionClick = (region) => {
  if (selectedRegion.value === region.label) {
    selectedRegion.value = '';
    g.clientBoardStore.regionType = '';
    g.clientBoardStore.regionData = {};
    g.clientBoardStore.regionDept = {};
  } else {
    selectedRegion.value = region.label;
    g.clientBoardStore.regionData = toRaw(region);
    if (isEndDept.value) {
      g.clientBoardStore.regionType = 'user';
      emit('callback', 'setUser', region.data)
    } else {
      const dept = extractDeptTree([toRaw(deptData.value)], region.deptId)
      g.clientBoardStore.regionDept = toRaw(dept);
      g.clientBoardStore.regionType = 'dept';
    }
  }
  g.emitter.emit("team_board_region_click", '');
};

const _updateUI = () => {
  if (reportData.value.length === 0) {
    averageValue.value = 0;
    chartData.value = [];
    return;
  }
  averageValue.value = reportData.value.reduce((acc, curr) => acc + (curr[selectDimension.value] || 0), 0) / reportData.value.length || 0;
  chartData.value = reportData.value.map(x => {
    const labelColumn = isEndDept.value ? 'userName' : 'deptName';
    return {
      deptId: x.deptId,
      label: x[labelColumn],
      value: x[selectDimension.value] || '0',
      data: toRaw(x)
    }
  })
  // 更新缩放比例
  calculateZoom();
}

const handleViewDetail = () => {
  const pageUrl = categoryInfo[currentCategory.value].url
  const splitor = pageUrl.includes('?') ? '&' : '?';
  const deptId = deptData.value.value;
  const startDate = getUrlParam('startDate');
  const endDate = getUrlParam('endDate');
  let url = `${g.config.publicPath}/#${pageUrl}${splitor}periodType=${periodType.value.replace('ly', '')}&deptId=${deptId}`;
  if (startDate && endDate) {
    url += `&startDate=${startDate}&endDate=${endDate}`;
  }
  jsOpenNewWindow(url)
}

const init = (is_end_dept, data, dept, _periodType) => {
  periodType.value = _periodType;
  isEndDept.value = is_end_dept;
  reportData.value = data;
  deptData.value = dept;
  _updateUI();
}

watch(selectDimension, (newVal) => {
  g.clientBoardStore.dimension = newVal;
  _updateUI();
  g.emitter.emit("team_board_field_click", newVal);
}, { immediate: true });

const itemList = computed(() => {
  return tabs.value.filter(item => item.hide ? false : (item.scope === 'both' || (!isEndDept.value && item.scope === 'dept')));
});

const _team_board_category_click = (category) => {

}


watch([currentCategory], () => {
  const list = StatisColumnTypes(isShangji.value).filter(item => item.category === currentCategory.value);
  tabs.value = list
  selectDimension.value = list[0].field
}, { immediate: true });

onMounted(() => {
  window.addEventListener('resize', calculateZoom);
});

onUnmounted(() => {
  window.removeEventListener('resize', calculateZoom);
});

defineExpose({
  selectDimension,
  averageValue,
  formatAverageValue,
  tabs,
  init,
  chartData,
  selectedRegion,
  lineZoom,
  RightArrow
});
</script>

<style lang="scss" scoped>
.statistics-chart {
  background: #fff;
  border-radius: 8px;
  padding: 16px 0;
  margin-top: 20px;

  .chart-layout {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .category-tabs {
      display: flex;
      flex-direction: row;
    }

    .view-detail {
      height: 22px;
      font-size: 14px;
      color: #436BFF;
      line-height: 22px;
      cursor: pointer;
      margin: 8px;
      display: flex;
      flex-direction: row;

      .el-icon {
        margin-left: 4px;
        margin-top: 4px;
      }
    }

    .chart-container {
      flex: 1;
      padding: 20px 0;
      position: relative;

      .average-info {
        color: #0FAD72;
        margin-bottom: 24px;
        margin-left: 103px;

        .bar {
          height: 100%;
          height: 3px;
          margin-top: 10px;
          background: #0FAD72;
          transition: width 0.3s ease;
          border-radius: 4px;
        }
      }

      .vertical-average-line {
        position: absolute;
        top: 52px;
        bottom: 0;
        width: 1px;
        z-index: 2;
        background: #0FAD72;
        transition: left 0.3s ease;
      }

      .chart {
        display: flex;
        flex-direction: column;
        gap: 24px;

        .chart-item {
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;

          &.selected {
            background-color: #F5F7FA;
          }

          display: flex;
          align-items: center;
          gap: 9px;

          .label {
            width: 80px;
            color: #666;
            font-size: 14px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }

          .bar-container {
            position: relative;
            display: flex;
            height: 14px;
            border-radius: 0px 100px 100px 0px;
            align-items: center;

            .bar {
              height: 100%;
              transition: width 0.3s ease;
              border-radius: 4px;
            }
          }

          .value {
            font-weight: bold;
            text-align: right;
          }
        }

        .bad_bar {
          .bar-container {
            background: #FF4D4F;
          }

          .value {
            z-index: 3;
            color: #FF4D4F;
          }
        }

        .good_bar {
          .bar-container {
            background: #D9D9D9;
          }

          .value {
            color: #0FAD72;
          }
        }
      }
    }
  }

  .radio-button-wrapper {
    display: inline-flex;
    align-items: center;
    position: relative;

    :deep(.el-radio-button__inner) {
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      border-radius: 0;
    }

    &:first-child {
      :deep(.el-radio-button__inner) {
        border-radius: 4px 0 0 4px;
      }
    }

    &:last-child {
      :deep(.el-radio-button__inner) {
        border-radius: 0 4px 4px 0;
      }
    }

    .radio-content {
      display: flex;
      align-items: center;
      gap: 4px;
      height: 100%;

      .hint-icon {
        font-size: 14px;
        color: #c8c8c8;
        margin-top: 1px;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      .hint-icon {
        color: #fff;
      }
    }
  }
}

.sc_half {
  width: 50%;
}

.sc_full {
  width: 100%;
}
</style>